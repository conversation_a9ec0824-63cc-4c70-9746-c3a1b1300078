"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.sliceConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.sliceConfig = {
    kernelName: tfjs_1.Slice,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var x = args.inputs.x;
        var backend = args.backend;
        var _a = args.attrs, begin = _a.begin, size = _a.size;
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', x.dtype),
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('Index', 'int32')
        ];
        // Bind tensor values
        var _b = tfjs_1.backend_util.slice_util.parseSliceParams(x, begin, size), begin_ = _b[0], size_ = _b[1];
        var beginTensor = (0, tfjs_1.tensor1d)(begin_, 'int32');
        var sizeTensor = (0, tfjs_1.tensor1d)(size_, 'int32');
        var res = backend.executeSingleOutput(tfjs_1.Slice, opAttrs, [x, beginTensor, sizeTensor]);
        beginTensor.dispose();
        sizeTensor.dispose();
        return res;
    }
};
