/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import {DepthToSpace, DepthToSpaceAttrs, DepthToSpaceInputs, KernelConfig, Tensor} from '@tensorflow/tfjs';

import {createTensorsTypeOpAttr, NodeJSKernelBackend} from '../nodejs_kernel_backend';

export const depthToSpaceConfig: KernelConfig = {
  kernelName: DepthToSpace,
  backendName: 'tensorflow',
  kernelFunc: (args) => {
    const {x} = args.inputs as DepthToSpaceInputs;
    const backend = args.backend as NodeJSKernelBackend;
    const {blockSize, dataFormat} = args.attrs as unknown as DepthToSpaceAttrs;

    const opAttrs = [
      createTensorsTypeOpAttr('T', x as Tensor), {
        name: 'block_size',
        type: backend.binding.TF_ATTR_INT,
        value: blockSize < 2 ? 2 : blockSize
      },
      {
        name: 'data_format',
        type: backend.binding.TF_ATTR_STRING,
        value: dataFormat
      }
    ];
    const inputs = [x];
    return backend.executeSingleOutput(DepthToSpace, opAttrs, inputs);
  }
};
