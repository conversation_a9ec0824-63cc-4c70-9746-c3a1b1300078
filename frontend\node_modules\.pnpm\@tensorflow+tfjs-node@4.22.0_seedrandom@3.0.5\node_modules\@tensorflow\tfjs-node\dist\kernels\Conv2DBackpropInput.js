"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.conv2DBackpropInputConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.conv2DBackpropInputConfig = {
    kernelName: tfjs_1.Conv2DBackpropInput,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var _a = args.inputs, dy = _a.dy, filter = _a.filter;
        var backend = args.backend;
        var _b = args.attrs, strides = _b.strides, pad = _b.pad, dataFormat = _b.dataFormat, dimRoundingMode = _b.dimRoundingMode, inputShape = _b.inputShape;
        var $dataFormat = tfjs_1.backend_util.convertConv2DDataFormat(dataFormat);
        var convInfo = tfjs_1.backend_util.computeConv2DInfo(inputShape, filter.shape, strides, 1 /* dilations */, pad, dimRoundingMode, false, $dataFormat);
        return conv2DBackpropInputImpl(dy, filter, convInfo, backend);
    }
};
function conv2DBackpropInputImpl(dy, filter, convInfo, backend) {
    if (convInfo.padInfo.type !== 'VALID' && convInfo.padInfo.type !== 'SAME') {
        throw new Error("TF Backend supports only 'valid' and 'same' padding " +
            "while padding was ".concat(convInfo.padInfo.type));
    }
    var strides = [1, convInfo.strideHeight, convInfo.strideWidth, 1];
    var padding = convInfo.padInfo.type;
    var dataFormat = convInfo.dataFormat === 'channelsLast' ? 'NHWC' : 'NCHW';
    var dilations = [1, convInfo.dilationHeight, convInfo.dilationWidth, 1];
    var opAttrs = [
        (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', 'float32'),
        { name: 'strides', type: backend.binding.TF_ATTR_INT, value: strides },
        { name: 'padding', type: backend.binding.TF_ATTR_STRING, value: padding }, {
            name: 'data_format',
            type: backend.binding.TF_ATTR_STRING,
            value: dataFormat
        },
        { name: 'use_cudnn_on_gpu', type: backend.binding.TF_ATTR_BOOL, value: true },
        { name: 'dilations', type: backend.binding.TF_ATTR_INT, value: dilations }
    ];
    var inputSizes = (0, tfjs_1.tensor1d)(convInfo.inShape, 'int32');
    var res = backend.executeSingleOutput(tfjs_1.Conv2DBackpropInput, opAttrs, [inputSizes, filter, dy]);
    inputSizes.dispose();
    return res;
}
