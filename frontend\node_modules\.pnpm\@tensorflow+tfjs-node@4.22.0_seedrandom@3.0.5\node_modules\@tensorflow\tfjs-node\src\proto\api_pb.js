// source: api.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.tensorflow.Any', null, global);
goog.exportSymbol('proto.tensorflow.AssetFileDef', null, global);
goog.exportSymbol('proto.tensorflow.AttrValue', null, global);
goog.exportSymbol('proto.tensorflow.AttrValue.ListValue', null, global);
goog.exportSymbol('proto.tensorflow.AttrValue.ValueCase', null, global);
goog.exportSymbol('proto.tensorflow.CollectionDef', null, global);
goog.exportSymbol('proto.tensorflow.CollectionDef.AnyList', null, global);
goog.exportSymbol('proto.tensorflow.CollectionDef.BytesList', null, global);
goog.exportSymbol('proto.tensorflow.CollectionDef.FloatList', null, global);
goog.exportSymbol('proto.tensorflow.CollectionDef.Int64List', null, global);
goog.exportSymbol('proto.tensorflow.CollectionDef.KindCase', null, global);
goog.exportSymbol('proto.tensorflow.CollectionDef.NodeList', null, global);
goog.exportSymbol('proto.tensorflow.DataClass', null, global);
goog.exportSymbol('proto.tensorflow.DataType', null, global);
goog.exportSymbol('proto.tensorflow.FunctionDef', null, global);
goog.exportSymbol('proto.tensorflow.FunctionDefLibrary', null, global);
goog.exportSymbol('proto.tensorflow.GradientDef', null, global);
goog.exportSymbol('proto.tensorflow.GraphDef', null, global);
goog.exportSymbol('proto.tensorflow.HistogramPluginData', null, global);
goog.exportSymbol('proto.tensorflow.HistogramProto', null, global);
goog.exportSymbol('proto.tensorflow.MetaGraphDef', null, global);
goog.exportSymbol('proto.tensorflow.MetaGraphDef.MetaInfoDef', null, global);
goog.exportSymbol('proto.tensorflow.NameAttrList', null, global);
goog.exportSymbol('proto.tensorflow.NodeDef', null, global);
goog.exportSymbol('proto.tensorflow.OpDef', null, global);
goog.exportSymbol('proto.tensorflow.OpDef.ArgDef', null, global);
goog.exportSymbol('proto.tensorflow.OpDef.AttrDef', null, global);
goog.exportSymbol('proto.tensorflow.OpDef.OpDeprecation', null, global);
goog.exportSymbol('proto.tensorflow.OpList', null, global);
goog.exportSymbol('proto.tensorflow.SavedModel', null, global);
goog.exportSymbol('proto.tensorflow.SaverDef', null, global);
goog.exportSymbol('proto.tensorflow.SaverDef.CheckpointFormatVersion', null, global);
goog.exportSymbol('proto.tensorflow.SignatureDef', null, global);
goog.exportSymbol('proto.tensorflow.Summary', null, global);
goog.exportSymbol('proto.tensorflow.Summary.Audio', null, global);
goog.exportSymbol('proto.tensorflow.Summary.Image', null, global);
goog.exportSymbol('proto.tensorflow.Summary.Value', null, global);
goog.exportSymbol('proto.tensorflow.Summary.Value.ValueCase', null, global);
goog.exportSymbol('proto.tensorflow.SummaryDescription', null, global);
goog.exportSymbol('proto.tensorflow.SummaryMetadata', null, global);
goog.exportSymbol('proto.tensorflow.SummaryMetadata.PluginData', null, global);
goog.exportSymbol('proto.tensorflow.Tensor', null, global);
goog.exportSymbol('proto.tensorflow.TensorInfo', null, global);
goog.exportSymbol('proto.tensorflow.TensorInfo.CooSparse', null, global);
goog.exportSymbol('proto.tensorflow.TensorInfo.EncodingCase', null, global);
goog.exportSymbol('proto.tensorflow.TensorShape', null, global);
goog.exportSymbol('proto.tensorflow.TensorShape.Dim', null, global);
goog.exportSymbol('proto.tensorflow.VersionDef', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.Any = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.Any, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.Any.displayName = 'proto.tensorflow.Any';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.TensorShape = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.TensorShape.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.TensorShape, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.TensorShape.displayName = 'proto.tensorflow.TensorShape';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.TensorShape.Dim = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.TensorShape.Dim, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.TensorShape.Dim.displayName = 'proto.tensorflow.TensorShape.Dim';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.Tensor = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.Tensor.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.Tensor, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.Tensor.displayName = 'proto.tensorflow.Tensor';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.AttrValue = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.tensorflow.AttrValue.oneofGroups_);
};
goog.inherits(proto.tensorflow.AttrValue, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.AttrValue.displayName = 'proto.tensorflow.AttrValue';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.AttrValue.ListValue = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.AttrValue.ListValue.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.AttrValue.ListValue, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.AttrValue.ListValue.displayName = 'proto.tensorflow.AttrValue.ListValue';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.NameAttrList = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.NameAttrList, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.NameAttrList.displayName = 'proto.tensorflow.NameAttrList';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.NodeDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.NodeDef.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.NodeDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.NodeDef.displayName = 'proto.tensorflow.NodeDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.VersionDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.VersionDef.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.VersionDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.VersionDef.displayName = 'proto.tensorflow.VersionDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.GraphDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.GraphDef.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.GraphDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.GraphDef.displayName = 'proto.tensorflow.GraphDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.CollectionDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.tensorflow.CollectionDef.oneofGroups_);
};
goog.inherits(proto.tensorflow.CollectionDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.CollectionDef.displayName = 'proto.tensorflow.CollectionDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.CollectionDef.NodeList = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.CollectionDef.NodeList.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.CollectionDef.NodeList, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.CollectionDef.NodeList.displayName = 'proto.tensorflow.CollectionDef.NodeList';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.CollectionDef.BytesList = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.CollectionDef.BytesList.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.CollectionDef.BytesList, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.CollectionDef.BytesList.displayName = 'proto.tensorflow.CollectionDef.BytesList';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.CollectionDef.Int64List = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.CollectionDef.Int64List.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.CollectionDef.Int64List, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.CollectionDef.Int64List.displayName = 'proto.tensorflow.CollectionDef.Int64List';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.CollectionDef.FloatList = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.CollectionDef.FloatList.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.CollectionDef.FloatList, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.CollectionDef.FloatList.displayName = 'proto.tensorflow.CollectionDef.FloatList';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.CollectionDef.AnyList = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.CollectionDef.AnyList.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.CollectionDef.AnyList, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.CollectionDef.AnyList.displayName = 'proto.tensorflow.CollectionDef.AnyList';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.SaverDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.SaverDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.SaverDef.displayName = 'proto.tensorflow.SaverDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.TensorInfo = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.tensorflow.TensorInfo.oneofGroups_);
};
goog.inherits(proto.tensorflow.TensorInfo, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.TensorInfo.displayName = 'proto.tensorflow.TensorInfo';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.TensorInfo.CooSparse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.TensorInfo.CooSparse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.TensorInfo.CooSparse.displayName = 'proto.tensorflow.TensorInfo.CooSparse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.SignatureDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.SignatureDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.SignatureDef.displayName = 'proto.tensorflow.SignatureDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.AssetFileDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.AssetFileDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.AssetFileDef.displayName = 'proto.tensorflow.AssetFileDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.OpDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.OpDef.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.OpDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.OpDef.displayName = 'proto.tensorflow.OpDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.OpDef.ArgDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.OpDef.ArgDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.OpDef.ArgDef.displayName = 'proto.tensorflow.OpDef.ArgDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.OpDef.AttrDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.OpDef.AttrDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.OpDef.AttrDef.displayName = 'proto.tensorflow.OpDef.AttrDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.OpDef.OpDeprecation = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.OpDef.OpDeprecation, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.OpDef.OpDeprecation.displayName = 'proto.tensorflow.OpDef.OpDeprecation';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.OpList = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.OpList.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.OpList, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.OpList.displayName = 'proto.tensorflow.OpList';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.MetaGraphDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.MetaGraphDef.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.MetaGraphDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.MetaGraphDef.displayName = 'proto.tensorflow.MetaGraphDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.MetaGraphDef.MetaInfoDef.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.MetaGraphDef.MetaInfoDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.MetaGraphDef.MetaInfoDef.displayName = 'proto.tensorflow.MetaGraphDef.MetaInfoDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.SavedModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.SavedModel.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.SavedModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.SavedModel.displayName = 'proto.tensorflow.SavedModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.FunctionDefLibrary = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.FunctionDefLibrary.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.FunctionDefLibrary, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.FunctionDefLibrary.displayName = 'proto.tensorflow.FunctionDefLibrary';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.FunctionDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.FunctionDef.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.FunctionDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.FunctionDef.displayName = 'proto.tensorflow.FunctionDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.GradientDef = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.GradientDef, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.GradientDef.displayName = 'proto.tensorflow.GradientDef';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.SummaryDescription = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.SummaryDescription, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.SummaryDescription.displayName = 'proto.tensorflow.SummaryDescription';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.HistogramProto = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.HistogramProto.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.HistogramProto, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.HistogramProto.displayName = 'proto.tensorflow.HistogramProto';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.SummaryMetadata = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.SummaryMetadata, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.SummaryMetadata.displayName = 'proto.tensorflow.SummaryMetadata';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.SummaryMetadata.PluginData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.SummaryMetadata.PluginData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.SummaryMetadata.PluginData.displayName = 'proto.tensorflow.SummaryMetadata.PluginData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.Summary = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.tensorflow.Summary.repeatedFields_, null);
};
goog.inherits(proto.tensorflow.Summary, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.Summary.displayName = 'proto.tensorflow.Summary';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.Summary.Image = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.Summary.Image, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.Summary.Image.displayName = 'proto.tensorflow.Summary.Image';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.Summary.Audio = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.Summary.Audio, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.Summary.Audio.displayName = 'proto.tensorflow.Summary.Audio';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.Summary.Value = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.tensorflow.Summary.Value.oneofGroups_);
};
goog.inherits(proto.tensorflow.Summary.Value, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.Summary.Value.displayName = 'proto.tensorflow.Summary.Value';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.tensorflow.HistogramPluginData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.tensorflow.HistogramPluginData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.tensorflow.HistogramPluginData.displayName = 'proto.tensorflow.HistogramPluginData';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.Any.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.Any.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.Any} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Any.toObject = function(includeInstance, msg) {
  var f, obj = {
    typeUrl: jspb.Message.getFieldWithDefault(msg, 1, ""),
    value: msg.getValue_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.Any}
 */
proto.tensorflow.Any.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.Any;
  return proto.tensorflow.Any.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.Any} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.Any}
 */
proto.tensorflow.Any.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTypeUrl(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.Any.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.Any.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.Any} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Any.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTypeUrl();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getValue_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
};


/**
 * optional string type_url = 1;
 * @return {string}
 */
proto.tensorflow.Any.prototype.getTypeUrl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.Any} returns this
 */
proto.tensorflow.Any.prototype.setTypeUrl = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional bytes value = 2;
 * @return {!(string|Uint8Array)}
 */
proto.tensorflow.Any.prototype.getValue = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes value = 2;
 * This is a type-conversion wrapper around `getValue()`
 * @return {string}
 */
proto.tensorflow.Any.prototype.getValue_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getValue()));
};


/**
 * optional bytes value = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getValue()`
 * @return {!Uint8Array}
 */
proto.tensorflow.Any.prototype.getValue_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getValue()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.tensorflow.Any} returns this
 */
proto.tensorflow.Any.prototype.setValue = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.TensorShape.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.TensorShape.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.TensorShape.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.TensorShape} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.TensorShape.toObject = function(includeInstance, msg) {
  var f, obj = {
    dimList: jspb.Message.toObjectList(msg.getDimList(),
    proto.tensorflow.TensorShape.Dim.toObject, includeInstance),
    unknownRank: jspb.Message.getBooleanFieldWithDefault(msg, 3, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.TensorShape}
 */
proto.tensorflow.TensorShape.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.TensorShape;
  return proto.tensorflow.TensorShape.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.TensorShape} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.TensorShape}
 */
proto.tensorflow.TensorShape.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 2:
      var value = new proto.tensorflow.TensorShape.Dim;
      reader.readMessage(value,proto.tensorflow.TensorShape.Dim.deserializeBinaryFromReader);
      msg.addDim(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setUnknownRank(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.TensorShape.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.TensorShape.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.TensorShape} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.TensorShape.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDimList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.tensorflow.TensorShape.Dim.serializeBinaryToWriter
    );
  }
  f = message.getUnknownRank();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.TensorShape.Dim.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.TensorShape.Dim.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.TensorShape.Dim} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.TensorShape.Dim.toObject = function(includeInstance, msg) {
  var f, obj = {
    size: jspb.Message.getFieldWithDefault(msg, 1, 0),
    name: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.TensorShape.Dim}
 */
proto.tensorflow.TensorShape.Dim.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.TensorShape.Dim;
  return proto.tensorflow.TensorShape.Dim.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.TensorShape.Dim} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.TensorShape.Dim}
 */
proto.tensorflow.TensorShape.Dim.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setSize(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.TensorShape.Dim.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.TensorShape.Dim.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.TensorShape.Dim} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.TensorShape.Dim.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSize();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int64 size = 1;
 * @return {number}
 */
proto.tensorflow.TensorShape.Dim.prototype.getSize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.TensorShape.Dim} returns this
 */
proto.tensorflow.TensorShape.Dim.prototype.setSize = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.tensorflow.TensorShape.Dim.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.TensorShape.Dim} returns this
 */
proto.tensorflow.TensorShape.Dim.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated Dim dim = 2;
 * @return {!Array<!proto.tensorflow.TensorShape.Dim>}
 */
proto.tensorflow.TensorShape.prototype.getDimList = function() {
  return /** @type{!Array<!proto.tensorflow.TensorShape.Dim>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.TensorShape.Dim, 2));
};


/**
 * @param {!Array<!proto.tensorflow.TensorShape.Dim>} value
 * @return {!proto.tensorflow.TensorShape} returns this
*/
proto.tensorflow.TensorShape.prototype.setDimList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.tensorflow.TensorShape.Dim=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.TensorShape.Dim}
 */
proto.tensorflow.TensorShape.prototype.addDim = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.tensorflow.TensorShape.Dim, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.TensorShape} returns this
 */
proto.tensorflow.TensorShape.prototype.clearDimList = function() {
  return this.setDimList([]);
};


/**
 * optional bool unknown_rank = 3;
 * @return {boolean}
 */
proto.tensorflow.TensorShape.prototype.getUnknownRank = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.tensorflow.TensorShape} returns this
 */
proto.tensorflow.TensorShape.prototype.setUnknownRank = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.Tensor.repeatedFields_ = [5,6,7,8,9,10,11,16,17];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.Tensor.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.Tensor.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.Tensor} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Tensor.toObject = function(includeInstance, msg) {
  var f, obj = {
    dtype: jspb.Message.getFieldWithDefault(msg, 1, 0),
    tensorShape: (f = msg.getTensorShape()) && proto.tensorflow.TensorShape.toObject(includeInstance, f),
    versionNumber: jspb.Message.getFieldWithDefault(msg, 3, 0),
    tensorContent: msg.getTensorContent_asB64(),
    floatValList: (f = jspb.Message.getRepeatedFloatingPointField(msg, 5)) == null ? undefined : f,
    doubleValList: (f = jspb.Message.getRepeatedFloatingPointField(msg, 6)) == null ? undefined : f,
    intValList: (f = jspb.Message.getRepeatedField(msg, 7)) == null ? undefined : f,
    stringValList: msg.getStringValList_asB64(),
    scomplexValList: (f = jspb.Message.getRepeatedFloatingPointField(msg, 9)) == null ? undefined : f,
    int64ValList: (f = jspb.Message.getRepeatedField(msg, 10)) == null ? undefined : f,
    boolValList: (f = jspb.Message.getRepeatedBooleanField(msg, 11)) == null ? undefined : f,
    uint32ValList: (f = jspb.Message.getRepeatedField(msg, 16)) == null ? undefined : f,
    uint64ValList: (f = jspb.Message.getRepeatedField(msg, 17)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.Tensor}
 */
proto.tensorflow.Tensor.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.Tensor;
  return proto.tensorflow.Tensor.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.Tensor} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.Tensor}
 */
proto.tensorflow.Tensor.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.tensorflow.DataType} */ (reader.readEnum());
      msg.setDtype(value);
      break;
    case 2:
      var value = new proto.tensorflow.TensorShape;
      reader.readMessage(value,proto.tensorflow.TensorShape.deserializeBinaryFromReader);
      msg.setTensorShape(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setVersionNumber(value);
      break;
    case 4:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setTensorContent(value);
      break;
    case 5:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedFloat() : [reader.readFloat()]);
      for (var i = 0; i < values.length; i++) {
        msg.addFloatVal(values[i]);
      }
      break;
    case 6:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedDouble() : [reader.readDouble()]);
      for (var i = 0; i < values.length; i++) {
        msg.addDoubleVal(values[i]);
      }
      break;
    case 7:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedInt32() : [reader.readInt32()]);
      for (var i = 0; i < values.length; i++) {
        msg.addIntVal(values[i]);
      }
      break;
    case 8:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.addStringVal(value);
      break;
    case 9:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedFloat() : [reader.readFloat()]);
      for (var i = 0; i < values.length; i++) {
        msg.addScomplexVal(values[i]);
      }
      break;
    case 10:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedInt64() : [reader.readInt64()]);
      for (var i = 0; i < values.length; i++) {
        msg.addInt64Val(values[i]);
      }
      break;
    case 11:
      var values = /** @type {!Array<boolean>} */ (reader.isDelimited() ? reader.readPackedBool() : [reader.readBool()]);
      for (var i = 0; i < values.length; i++) {
        msg.addBoolVal(values[i]);
      }
      break;
    case 16:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedUint32() : [reader.readUint32()]);
      for (var i = 0; i < values.length; i++) {
        msg.addUint32Val(values[i]);
      }
      break;
    case 17:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedUint64() : [reader.readUint64()]);
      for (var i = 0; i < values.length; i++) {
        msg.addUint64Val(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.Tensor.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.Tensor.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.Tensor} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Tensor.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDtype();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getTensorShape();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.tensorflow.TensorShape.serializeBinaryToWriter
    );
  }
  f = message.getVersionNumber();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getTensorContent_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      4,
      f
    );
  }
  f = message.getFloatValList();
  if (f.length > 0) {
    writer.writePackedFloat(
      5,
      f
    );
  }
  f = message.getDoubleValList();
  if (f.length > 0) {
    writer.writePackedDouble(
      6,
      f
    );
  }
  f = message.getIntValList();
  if (f.length > 0) {
    writer.writePackedInt32(
      7,
      f
    );
  }
  f = message.getStringValList_asU8();
  if (f.length > 0) {
    writer.writeRepeatedBytes(
      8,
      f
    );
  }
  f = message.getScomplexValList();
  if (f.length > 0) {
    writer.writePackedFloat(
      9,
      f
    );
  }
  f = message.getInt64ValList();
  if (f.length > 0) {
    writer.writePackedInt64(
      10,
      f
    );
  }
  f = message.getBoolValList();
  if (f.length > 0) {
    writer.writePackedBool(
      11,
      f
    );
  }
  f = message.getUint32ValList();
  if (f.length > 0) {
    writer.writePackedUint32(
      16,
      f
    );
  }
  f = message.getUint64ValList();
  if (f.length > 0) {
    writer.writePackedUint64(
      17,
      f
    );
  }
};


/**
 * optional DataType dtype = 1;
 * @return {!proto.tensorflow.DataType}
 */
proto.tensorflow.Tensor.prototype.getDtype = function() {
  return /** @type {!proto.tensorflow.DataType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.tensorflow.DataType} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setDtype = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional TensorShape tensor_shape = 2;
 * @return {?proto.tensorflow.TensorShape}
 */
proto.tensorflow.Tensor.prototype.getTensorShape = function() {
  return /** @type{?proto.tensorflow.TensorShape} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.TensorShape, 2));
};


/**
 * @param {?proto.tensorflow.TensorShape|undefined} value
 * @return {!proto.tensorflow.Tensor} returns this
*/
proto.tensorflow.Tensor.prototype.setTensorShape = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.clearTensorShape = function() {
  return this.setTensorShape(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.Tensor.prototype.hasTensorShape = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional int32 version_number = 3;
 * @return {number}
 */
proto.tensorflow.Tensor.prototype.getVersionNumber = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setVersionNumber = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional bytes tensor_content = 4;
 * @return {!(string|Uint8Array)}
 */
proto.tensorflow.Tensor.prototype.getTensorContent = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * optional bytes tensor_content = 4;
 * This is a type-conversion wrapper around `getTensorContent()`
 * @return {string}
 */
proto.tensorflow.Tensor.prototype.getTensorContent_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getTensorContent()));
};


/**
 * optional bytes tensor_content = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getTensorContent()`
 * @return {!Uint8Array}
 */
proto.tensorflow.Tensor.prototype.getTensorContent_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getTensorContent()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setTensorContent = function(value) {
  return jspb.Message.setProto3BytesField(this, 4, value);
};


/**
 * repeated float float_val = 5;
 * @return {!Array<number>}
 */
proto.tensorflow.Tensor.prototype.getFloatValList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedFloatingPointField(this, 5));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setFloatValList = function(value) {
  return jspb.Message.setField(this, 5, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.addFloatVal = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 5, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.clearFloatValList = function() {
  return this.setFloatValList([]);
};


/**
 * repeated double double_val = 6;
 * @return {!Array<number>}
 */
proto.tensorflow.Tensor.prototype.getDoubleValList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedFloatingPointField(this, 6));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setDoubleValList = function(value) {
  return jspb.Message.setField(this, 6, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.addDoubleVal = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 6, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.clearDoubleValList = function() {
  return this.setDoubleValList([]);
};


/**
 * repeated int32 int_val = 7;
 * @return {!Array<number>}
 */
proto.tensorflow.Tensor.prototype.getIntValList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 7));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setIntValList = function(value) {
  return jspb.Message.setField(this, 7, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.addIntVal = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 7, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.clearIntValList = function() {
  return this.setIntValList([]);
};


/**
 * repeated bytes string_val = 8;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.tensorflow.Tensor.prototype.getStringValList = function() {
  return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 8));
};


/**
 * repeated bytes string_val = 8;
 * This is a type-conversion wrapper around `getStringValList()`
 * @return {!Array<string>}
 */
proto.tensorflow.Tensor.prototype.getStringValList_asB64 = function() {
  return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(
      this.getStringValList()));
};


/**
 * repeated bytes string_val = 8;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getStringValList()`
 * @return {!Array<!Uint8Array>}
 */
proto.tensorflow.Tensor.prototype.getStringValList_asU8 = function() {
  return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(
      this.getStringValList()));
};


/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setStringValList = function(value) {
  return jspb.Message.setField(this, 8, value || []);
};


/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.addStringVal = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 8, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.clearStringValList = function() {
  return this.setStringValList([]);
};


/**
 * repeated float scomplex_val = 9;
 * @return {!Array<number>}
 */
proto.tensorflow.Tensor.prototype.getScomplexValList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedFloatingPointField(this, 9));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setScomplexValList = function(value) {
  return jspb.Message.setField(this, 9, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.addScomplexVal = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 9, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.clearScomplexValList = function() {
  return this.setScomplexValList([]);
};


/**
 * repeated int64 int64_val = 10;
 * @return {!Array<number>}
 */
proto.tensorflow.Tensor.prototype.getInt64ValList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 10));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setInt64ValList = function(value) {
  return jspb.Message.setField(this, 10, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.addInt64Val = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 10, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.clearInt64ValList = function() {
  return this.setInt64ValList([]);
};


/**
 * repeated bool bool_val = 11;
 * @return {!Array<boolean>}
 */
proto.tensorflow.Tensor.prototype.getBoolValList = function() {
  return /** @type {!Array<boolean>} */ (jspb.Message.getRepeatedBooleanField(this, 11));
};


/**
 * @param {!Array<boolean>} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setBoolValList = function(value) {
  return jspb.Message.setField(this, 11, value || []);
};


/**
 * @param {boolean} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.addBoolVal = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 11, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.clearBoolValList = function() {
  return this.setBoolValList([]);
};


/**
 * repeated uint32 uint32_val = 16;
 * @return {!Array<number>}
 */
proto.tensorflow.Tensor.prototype.getUint32ValList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 16));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setUint32ValList = function(value) {
  return jspb.Message.setField(this, 16, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.addUint32Val = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 16, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.clearUint32ValList = function() {
  return this.setUint32ValList([]);
};


/**
 * repeated uint64 uint64_val = 17;
 * @return {!Array<number>}
 */
proto.tensorflow.Tensor.prototype.getUint64ValList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 17));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.setUint64ValList = function(value) {
  return jspb.Message.setField(this, 17, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.addUint64Val = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 17, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.Tensor} returns this
 */
proto.tensorflow.Tensor.prototype.clearUint64ValList = function() {
  return this.setUint64ValList([]);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.tensorflow.AttrValue.oneofGroups_ = [[1,2,3,4,5,6,7,8,9,10]];

/**
 * @enum {number}
 */
proto.tensorflow.AttrValue.ValueCase = {
  VALUE_NOT_SET: 0,
  LIST: 1,
  S: 2,
  I: 3,
  F: 4,
  B: 5,
  TYPE: 6,
  SHAPE: 7,
  TENSOR: 8,
  PLACEHOLDER: 9,
  FUNC: 10
};

/**
 * @return {proto.tensorflow.AttrValue.ValueCase}
 */
proto.tensorflow.AttrValue.prototype.getValueCase = function() {
  return /** @type {proto.tensorflow.AttrValue.ValueCase} */(jspb.Message.computeOneofCase(this, proto.tensorflow.AttrValue.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.AttrValue.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.AttrValue.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.AttrValue} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.AttrValue.toObject = function(includeInstance, msg) {
  var f, obj = {
    list: (f = msg.getList()) && proto.tensorflow.AttrValue.ListValue.toObject(includeInstance, f),
    s: msg.getS_asB64(),
    i: jspb.Message.getFieldWithDefault(msg, 3, 0),
    f: jspb.Message.getFloatingPointFieldWithDefault(msg, 4, 0.0),
    b: jspb.Message.getBooleanFieldWithDefault(msg, 5, false),
    type: jspb.Message.getFieldWithDefault(msg, 6, 0),
    shape: (f = msg.getShape()) && proto.tensorflow.TensorShape.toObject(includeInstance, f),
    tensor: (f = msg.getTensor()) && proto.tensorflow.Tensor.toObject(includeInstance, f),
    placeholder: jspb.Message.getFieldWithDefault(msg, 9, ""),
    func: (f = msg.getFunc()) && proto.tensorflow.NameAttrList.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.AttrValue}
 */
proto.tensorflow.AttrValue.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.AttrValue;
  return proto.tensorflow.AttrValue.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.AttrValue} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.AttrValue}
 */
proto.tensorflow.AttrValue.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.AttrValue.ListValue;
      reader.readMessage(value,proto.tensorflow.AttrValue.ListValue.deserializeBinaryFromReader);
      msg.setList(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setS(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setI(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setF(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setB(value);
      break;
    case 6:
      var value = /** @type {!proto.tensorflow.DataType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 7:
      var value = new proto.tensorflow.TensorShape;
      reader.readMessage(value,proto.tensorflow.TensorShape.deserializeBinaryFromReader);
      msg.setShape(value);
      break;
    case 8:
      var value = new proto.tensorflow.Tensor;
      reader.readMessage(value,proto.tensorflow.Tensor.deserializeBinaryFromReader);
      msg.setTensor(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setPlaceholder(value);
      break;
    case 10:
      var value = new proto.tensorflow.NameAttrList;
      reader.readMessage(value,proto.tensorflow.NameAttrList.deserializeBinaryFromReader);
      msg.setFunc(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.AttrValue.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.AttrValue.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.AttrValue} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.AttrValue.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getList();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.tensorflow.AttrValue.ListValue.serializeBinaryToWriter
    );
  }
  f = /** @type {!(string|Uint8Array)} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeBytes(
      2,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeFloat(
      4,
      f
    );
  }
  f = /** @type {boolean} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeBool(
      5,
      f
    );
  }
  f = /** @type {!proto.tensorflow.DataType} */ (jspb.Message.getField(message, 6));
  if (f != null) {
    writer.writeEnum(
      6,
      f
    );
  }
  f = message.getShape();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      proto.tensorflow.TensorShape.serializeBinaryToWriter
    );
  }
  f = message.getTensor();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      proto.tensorflow.Tensor.serializeBinaryToWriter
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 9));
  if (f != null) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getFunc();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      proto.tensorflow.NameAttrList.serializeBinaryToWriter
    );
  }
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.AttrValue.ListValue.repeatedFields_ = [2,3,4,5,6,7,8,9];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.AttrValue.ListValue.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.AttrValue.ListValue.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.AttrValue.ListValue} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.AttrValue.ListValue.toObject = function(includeInstance, msg) {
  var f, obj = {
    sList: msg.getSList_asB64(),
    iList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f,
    fList: (f = jspb.Message.getRepeatedFloatingPointField(msg, 4)) == null ? undefined : f,
    bList: (f = jspb.Message.getRepeatedBooleanField(msg, 5)) == null ? undefined : f,
    typeList: (f = jspb.Message.getRepeatedField(msg, 6)) == null ? undefined : f,
    shapeList: jspb.Message.toObjectList(msg.getShapeList(),
    proto.tensorflow.TensorShape.toObject, includeInstance),
    tensorList: jspb.Message.toObjectList(msg.getTensorList(),
    proto.tensorflow.Tensor.toObject, includeInstance),
    funcList: jspb.Message.toObjectList(msg.getFuncList(),
    proto.tensorflow.NameAttrList.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.AttrValue.ListValue}
 */
proto.tensorflow.AttrValue.ListValue.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.AttrValue.ListValue;
  return proto.tensorflow.AttrValue.ListValue.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.AttrValue.ListValue} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.AttrValue.ListValue}
 */
proto.tensorflow.AttrValue.ListValue.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.addS(value);
      break;
    case 3:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedInt64() : [reader.readInt64()]);
      for (var i = 0; i < values.length; i++) {
        msg.addI(values[i]);
      }
      break;
    case 4:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedFloat() : [reader.readFloat()]);
      for (var i = 0; i < values.length; i++) {
        msg.addF(values[i]);
      }
      break;
    case 5:
      var values = /** @type {!Array<boolean>} */ (reader.isDelimited() ? reader.readPackedBool() : [reader.readBool()]);
      for (var i = 0; i < values.length; i++) {
        msg.addB(values[i]);
      }
      break;
    case 6:
      var values = /** @type {!Array<!proto.tensorflow.DataType>} */ (reader.isDelimited() ? reader.readPackedEnum() : [reader.readEnum()]);
      for (var i = 0; i < values.length; i++) {
        msg.addType(values[i]);
      }
      break;
    case 7:
      var value = new proto.tensorflow.TensorShape;
      reader.readMessage(value,proto.tensorflow.TensorShape.deserializeBinaryFromReader);
      msg.addShape(value);
      break;
    case 8:
      var value = new proto.tensorflow.Tensor;
      reader.readMessage(value,proto.tensorflow.Tensor.deserializeBinaryFromReader);
      msg.addTensor(value);
      break;
    case 9:
      var value = new proto.tensorflow.NameAttrList;
      reader.readMessage(value,proto.tensorflow.NameAttrList.deserializeBinaryFromReader);
      msg.addFunc(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.AttrValue.ListValue.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.AttrValue.ListValue.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.AttrValue.ListValue} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.AttrValue.ListValue.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSList_asU8();
  if (f.length > 0) {
    writer.writeRepeatedBytes(
      2,
      f
    );
  }
  f = message.getIList();
  if (f.length > 0) {
    writer.writePackedInt64(
      3,
      f
    );
  }
  f = message.getFList();
  if (f.length > 0) {
    writer.writePackedFloat(
      4,
      f
    );
  }
  f = message.getBList();
  if (f.length > 0) {
    writer.writePackedBool(
      5,
      f
    );
  }
  f = message.getTypeList();
  if (f.length > 0) {
    writer.writePackedEnum(
      6,
      f
    );
  }
  f = message.getShapeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      7,
      f,
      proto.tensorflow.TensorShape.serializeBinaryToWriter
    );
  }
  f = message.getTensorList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      8,
      f,
      proto.tensorflow.Tensor.serializeBinaryToWriter
    );
  }
  f = message.getFuncList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      9,
      f,
      proto.tensorflow.NameAttrList.serializeBinaryToWriter
    );
  }
};


/**
 * repeated bytes s = 2;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.tensorflow.AttrValue.ListValue.prototype.getSList = function() {
  return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * repeated bytes s = 2;
 * This is a type-conversion wrapper around `getSList()`
 * @return {!Array<string>}
 */
proto.tensorflow.AttrValue.ListValue.prototype.getSList_asB64 = function() {
  return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(
      this.getSList()));
};


/**
 * repeated bytes s = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSList()`
 * @return {!Array<!Uint8Array>}
 */
proto.tensorflow.AttrValue.ListValue.prototype.getSList_asU8 = function() {
  return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(
      this.getSList()));
};


/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.setSList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.addS = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.clearSList = function() {
  return this.setSList([]);
};


/**
 * repeated int64 i = 3;
 * @return {!Array<number>}
 */
proto.tensorflow.AttrValue.ListValue.prototype.getIList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 3));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.setIList = function(value) {
  return jspb.Message.setField(this, 3, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.addI = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.clearIList = function() {
  return this.setIList([]);
};


/**
 * repeated float f = 4;
 * @return {!Array<number>}
 */
proto.tensorflow.AttrValue.ListValue.prototype.getFList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedFloatingPointField(this, 4));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.setFList = function(value) {
  return jspb.Message.setField(this, 4, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.addF = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 4, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.clearFList = function() {
  return this.setFList([]);
};


/**
 * repeated bool b = 5;
 * @return {!Array<boolean>}
 */
proto.tensorflow.AttrValue.ListValue.prototype.getBList = function() {
  return /** @type {!Array<boolean>} */ (jspb.Message.getRepeatedBooleanField(this, 5));
};


/**
 * @param {!Array<boolean>} value
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.setBList = function(value) {
  return jspb.Message.setField(this, 5, value || []);
};


/**
 * @param {boolean} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.addB = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 5, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.clearBList = function() {
  return this.setBList([]);
};


/**
 * repeated DataType type = 6;
 * @return {!Array<!proto.tensorflow.DataType>}
 */
proto.tensorflow.AttrValue.ListValue.prototype.getTypeList = function() {
  return /** @type {!Array<!proto.tensorflow.DataType>} */ (jspb.Message.getRepeatedField(this, 6));
};


/**
 * @param {!Array<!proto.tensorflow.DataType>} value
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.setTypeList = function(value) {
  return jspb.Message.setField(this, 6, value || []);
};


/**
 * @param {!proto.tensorflow.DataType} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.addType = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 6, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.clearTypeList = function() {
  return this.setTypeList([]);
};


/**
 * repeated TensorShape shape = 7;
 * @return {!Array<!proto.tensorflow.TensorShape>}
 */
proto.tensorflow.AttrValue.ListValue.prototype.getShapeList = function() {
  return /** @type{!Array<!proto.tensorflow.TensorShape>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.TensorShape, 7));
};


/**
 * @param {!Array<!proto.tensorflow.TensorShape>} value
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
*/
proto.tensorflow.AttrValue.ListValue.prototype.setShapeList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 7, value);
};


/**
 * @param {!proto.tensorflow.TensorShape=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.TensorShape}
 */
proto.tensorflow.AttrValue.ListValue.prototype.addShape = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 7, opt_value, proto.tensorflow.TensorShape, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.clearShapeList = function() {
  return this.setShapeList([]);
};


/**
 * repeated Tensor tensor = 8;
 * @return {!Array<!proto.tensorflow.Tensor>}
 */
proto.tensorflow.AttrValue.ListValue.prototype.getTensorList = function() {
  return /** @type{!Array<!proto.tensorflow.Tensor>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.Tensor, 8));
};


/**
 * @param {!Array<!proto.tensorflow.Tensor>} value
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
*/
proto.tensorflow.AttrValue.ListValue.prototype.setTensorList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 8, value);
};


/**
 * @param {!proto.tensorflow.Tensor=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Tensor}
 */
proto.tensorflow.AttrValue.ListValue.prototype.addTensor = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 8, opt_value, proto.tensorflow.Tensor, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.clearTensorList = function() {
  return this.setTensorList([]);
};


/**
 * repeated NameAttrList func = 9;
 * @return {!Array<!proto.tensorflow.NameAttrList>}
 */
proto.tensorflow.AttrValue.ListValue.prototype.getFuncList = function() {
  return /** @type{!Array<!proto.tensorflow.NameAttrList>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.NameAttrList, 9));
};


/**
 * @param {!Array<!proto.tensorflow.NameAttrList>} value
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
*/
proto.tensorflow.AttrValue.ListValue.prototype.setFuncList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 9, value);
};


/**
 * @param {!proto.tensorflow.NameAttrList=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.NameAttrList}
 */
proto.tensorflow.AttrValue.ListValue.prototype.addFunc = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 9, opt_value, proto.tensorflow.NameAttrList, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.AttrValue.ListValue} returns this
 */
proto.tensorflow.AttrValue.ListValue.prototype.clearFuncList = function() {
  return this.setFuncList([]);
};


/**
 * optional ListValue list = 1;
 * @return {?proto.tensorflow.AttrValue.ListValue}
 */
proto.tensorflow.AttrValue.prototype.getList = function() {
  return /** @type{?proto.tensorflow.AttrValue.ListValue} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.AttrValue.ListValue, 1));
};


/**
 * @param {?proto.tensorflow.AttrValue.ListValue|undefined} value
 * @return {!proto.tensorflow.AttrValue} returns this
*/
proto.tensorflow.AttrValue.prototype.setList = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.tensorflow.AttrValue.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.clearList = function() {
  return this.setList(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.hasList = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional bytes s = 2;
 * @return {!(string|Uint8Array)}
 */
proto.tensorflow.AttrValue.prototype.getS = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes s = 2;
 * This is a type-conversion wrapper around `getS()`
 * @return {string}
 */
proto.tensorflow.AttrValue.prototype.getS_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getS()));
};


/**
 * optional bytes s = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getS()`
 * @return {!Uint8Array}
 */
proto.tensorflow.AttrValue.prototype.getS_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getS()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.setS = function(value) {
  return jspb.Message.setOneofField(this, 2, proto.tensorflow.AttrValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.clearS = function() {
  return jspb.Message.setOneofField(this, 2, proto.tensorflow.AttrValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.hasS = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional int64 i = 3;
 * @return {number}
 */
proto.tensorflow.AttrValue.prototype.getI = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.setI = function(value) {
  return jspb.Message.setOneofField(this, 3, proto.tensorflow.AttrValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.clearI = function() {
  return jspb.Message.setOneofField(this, 3, proto.tensorflow.AttrValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.hasI = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional float f = 4;
 * @return {number}
 */
proto.tensorflow.AttrValue.prototype.getF = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 4, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.setF = function(value) {
  return jspb.Message.setOneofField(this, 4, proto.tensorflow.AttrValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.clearF = function() {
  return jspb.Message.setOneofField(this, 4, proto.tensorflow.AttrValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.hasF = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional bool b = 5;
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.getB = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.setB = function(value) {
  return jspb.Message.setOneofField(this, 5, proto.tensorflow.AttrValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.clearB = function() {
  return jspb.Message.setOneofField(this, 5, proto.tensorflow.AttrValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.hasB = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional DataType type = 6;
 * @return {!proto.tensorflow.DataType}
 */
proto.tensorflow.AttrValue.prototype.getType = function() {
  return /** @type {!proto.tensorflow.DataType} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {!proto.tensorflow.DataType} value
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.setType = function(value) {
  return jspb.Message.setOneofField(this, 6, proto.tensorflow.AttrValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.clearType = function() {
  return jspb.Message.setOneofField(this, 6, proto.tensorflow.AttrValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.hasType = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional TensorShape shape = 7;
 * @return {?proto.tensorflow.TensorShape}
 */
proto.tensorflow.AttrValue.prototype.getShape = function() {
  return /** @type{?proto.tensorflow.TensorShape} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.TensorShape, 7));
};


/**
 * @param {?proto.tensorflow.TensorShape|undefined} value
 * @return {!proto.tensorflow.AttrValue} returns this
*/
proto.tensorflow.AttrValue.prototype.setShape = function(value) {
  return jspb.Message.setOneofWrapperField(this, 7, proto.tensorflow.AttrValue.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.clearShape = function() {
  return this.setShape(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.hasShape = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional Tensor tensor = 8;
 * @return {?proto.tensorflow.Tensor}
 */
proto.tensorflow.AttrValue.prototype.getTensor = function() {
  return /** @type{?proto.tensorflow.Tensor} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.Tensor, 8));
};


/**
 * @param {?proto.tensorflow.Tensor|undefined} value
 * @return {!proto.tensorflow.AttrValue} returns this
*/
proto.tensorflow.AttrValue.prototype.setTensor = function(value) {
  return jspb.Message.setOneofWrapperField(this, 8, proto.tensorflow.AttrValue.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.clearTensor = function() {
  return this.setTensor(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.hasTensor = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional string placeholder = 9;
 * @return {string}
 */
proto.tensorflow.AttrValue.prototype.getPlaceholder = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.setPlaceholder = function(value) {
  return jspb.Message.setOneofField(this, 9, proto.tensorflow.AttrValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.clearPlaceholder = function() {
  return jspb.Message.setOneofField(this, 9, proto.tensorflow.AttrValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.hasPlaceholder = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional NameAttrList func = 10;
 * @return {?proto.tensorflow.NameAttrList}
 */
proto.tensorflow.AttrValue.prototype.getFunc = function() {
  return /** @type{?proto.tensorflow.NameAttrList} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.NameAttrList, 10));
};


/**
 * @param {?proto.tensorflow.NameAttrList|undefined} value
 * @return {!proto.tensorflow.AttrValue} returns this
*/
proto.tensorflow.AttrValue.prototype.setFunc = function(value) {
  return jspb.Message.setOneofWrapperField(this, 10, proto.tensorflow.AttrValue.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.AttrValue} returns this
 */
proto.tensorflow.AttrValue.prototype.clearFunc = function() {
  return this.setFunc(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AttrValue.prototype.hasFunc = function() {
  return jspb.Message.getField(this, 10) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.NameAttrList.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.NameAttrList.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.NameAttrList} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.NameAttrList.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    attrMap: (f = msg.getAttrMap()) ? f.toObject(includeInstance, proto.tensorflow.AttrValue.toObject) : []
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.NameAttrList}
 */
proto.tensorflow.NameAttrList.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.NameAttrList;
  return proto.tensorflow.NameAttrList.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.NameAttrList} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.NameAttrList}
 */
proto.tensorflow.NameAttrList.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = msg.getAttrMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readMessage, proto.tensorflow.AttrValue.deserializeBinaryFromReader, "", new proto.tensorflow.AttrValue());
         });
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.NameAttrList.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.NameAttrList.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.NameAttrList} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.NameAttrList.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAttrMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(2, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeMessage, proto.tensorflow.AttrValue.serializeBinaryToWriter);
  }
};


/**
 * optional string name = 1;
 * @return {string}
 */
proto.tensorflow.NameAttrList.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.NameAttrList} returns this
 */
proto.tensorflow.NameAttrList.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * map<string, AttrValue> attr = 2;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,!proto.tensorflow.AttrValue>}
 */
proto.tensorflow.NameAttrList.prototype.getAttrMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<string,!proto.tensorflow.AttrValue>} */ (
      jspb.Message.getMapField(this, 2, opt_noLazyCreate,
      proto.tensorflow.AttrValue));
};


/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.tensorflow.NameAttrList} returns this
 */
proto.tensorflow.NameAttrList.prototype.clearAttrMap = function() {
  this.getAttrMap().clear();
  return this;};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.NodeDef.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.NodeDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.NodeDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.NodeDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.NodeDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    op: jspb.Message.getFieldWithDefault(msg, 2, ""),
    inputList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f,
    device: jspb.Message.getFieldWithDefault(msg, 4, ""),
    attrMap: (f = msg.getAttrMap()) ? f.toObject(includeInstance, proto.tensorflow.AttrValue.toObject) : []
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.NodeDef}
 */
proto.tensorflow.NodeDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.NodeDef;
  return proto.tensorflow.NodeDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.NodeDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.NodeDef}
 */
proto.tensorflow.NodeDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setOp(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.addInput(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDevice(value);
      break;
    case 5:
      var value = msg.getAttrMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readMessage, proto.tensorflow.AttrValue.deserializeBinaryFromReader, "", new proto.tensorflow.AttrValue());
         });
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.NodeDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.NodeDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.NodeDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.NodeDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getOp();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getInputList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      3,
      f
    );
  }
  f = message.getDevice();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getAttrMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(5, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeMessage, proto.tensorflow.AttrValue.serializeBinaryToWriter);
  }
};


/**
 * optional string name = 1;
 * @return {string}
 */
proto.tensorflow.NodeDef.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.NodeDef} returns this
 */
proto.tensorflow.NodeDef.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string op = 2;
 * @return {string}
 */
proto.tensorflow.NodeDef.prototype.getOp = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.NodeDef} returns this
 */
proto.tensorflow.NodeDef.prototype.setOp = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated string input = 3;
 * @return {!Array<string>}
 */
proto.tensorflow.NodeDef.prototype.getInputList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 3));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.tensorflow.NodeDef} returns this
 */
proto.tensorflow.NodeDef.prototype.setInputList = function(value) {
  return jspb.Message.setField(this, 3, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.NodeDef} returns this
 */
proto.tensorflow.NodeDef.prototype.addInput = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.NodeDef} returns this
 */
proto.tensorflow.NodeDef.prototype.clearInputList = function() {
  return this.setInputList([]);
};


/**
 * optional string device = 4;
 * @return {string}
 */
proto.tensorflow.NodeDef.prototype.getDevice = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.NodeDef} returns this
 */
proto.tensorflow.NodeDef.prototype.setDevice = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * map<string, AttrValue> attr = 5;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,!proto.tensorflow.AttrValue>}
 */
proto.tensorflow.NodeDef.prototype.getAttrMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<string,!proto.tensorflow.AttrValue>} */ (
      jspb.Message.getMapField(this, 5, opt_noLazyCreate,
      proto.tensorflow.AttrValue));
};


/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.tensorflow.NodeDef} returns this
 */
proto.tensorflow.NodeDef.prototype.clearAttrMap = function() {
  this.getAttrMap().clear();
  return this;};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.VersionDef.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.VersionDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.VersionDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.VersionDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.VersionDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    producer: jspb.Message.getFieldWithDefault(msg, 1, 0),
    minConsumer: jspb.Message.getFieldWithDefault(msg, 2, 0),
    badConsumersList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.VersionDef}
 */
proto.tensorflow.VersionDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.VersionDef;
  return proto.tensorflow.VersionDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.VersionDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.VersionDef}
 */
proto.tensorflow.VersionDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setProducer(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMinConsumer(value);
      break;
    case 3:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedInt32() : [reader.readInt32()]);
      for (var i = 0; i < values.length; i++) {
        msg.addBadConsumers(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.VersionDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.VersionDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.VersionDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.VersionDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getProducer();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getMinConsumer();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getBadConsumersList();
  if (f.length > 0) {
    writer.writePackedInt32(
      3,
      f
    );
  }
};


/**
 * optional int32 producer = 1;
 * @return {number}
 */
proto.tensorflow.VersionDef.prototype.getProducer = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.VersionDef} returns this
 */
proto.tensorflow.VersionDef.prototype.setProducer = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 min_consumer = 2;
 * @return {number}
 */
proto.tensorflow.VersionDef.prototype.getMinConsumer = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.VersionDef} returns this
 */
proto.tensorflow.VersionDef.prototype.setMinConsumer = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * repeated int32 bad_consumers = 3;
 * @return {!Array<number>}
 */
proto.tensorflow.VersionDef.prototype.getBadConsumersList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 3));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.VersionDef} returns this
 */
proto.tensorflow.VersionDef.prototype.setBadConsumersList = function(value) {
  return jspb.Message.setField(this, 3, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.VersionDef} returns this
 */
proto.tensorflow.VersionDef.prototype.addBadConsumers = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.VersionDef} returns this
 */
proto.tensorflow.VersionDef.prototype.clearBadConsumersList = function() {
  return this.setBadConsumersList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.GraphDef.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.GraphDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.GraphDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.GraphDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.GraphDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    nodeList: jspb.Message.toObjectList(msg.getNodeList(),
    proto.tensorflow.NodeDef.toObject, includeInstance),
    versions: (f = msg.getVersions()) && proto.tensorflow.VersionDef.toObject(includeInstance, f),
    library: (f = msg.getLibrary()) && proto.tensorflow.FunctionDefLibrary.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.GraphDef}
 */
proto.tensorflow.GraphDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.GraphDef;
  return proto.tensorflow.GraphDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.GraphDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.GraphDef}
 */
proto.tensorflow.GraphDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.NodeDef;
      reader.readMessage(value,proto.tensorflow.NodeDef.deserializeBinaryFromReader);
      msg.addNode(value);
      break;
    case 4:
      var value = new proto.tensorflow.VersionDef;
      reader.readMessage(value,proto.tensorflow.VersionDef.deserializeBinaryFromReader);
      msg.setVersions(value);
      break;
    case 2:
      var value = new proto.tensorflow.FunctionDefLibrary;
      reader.readMessage(value,proto.tensorflow.FunctionDefLibrary.deserializeBinaryFromReader);
      msg.setLibrary(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.GraphDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.GraphDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.GraphDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.GraphDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getNodeList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.tensorflow.NodeDef.serializeBinaryToWriter
    );
  }
  f = message.getVersions();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.tensorflow.VersionDef.serializeBinaryToWriter
    );
  }
  f = message.getLibrary();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.tensorflow.FunctionDefLibrary.serializeBinaryToWriter
    );
  }
};


/**
 * repeated NodeDef node = 1;
 * @return {!Array<!proto.tensorflow.NodeDef>}
 */
proto.tensorflow.GraphDef.prototype.getNodeList = function() {
  return /** @type{!Array<!proto.tensorflow.NodeDef>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.NodeDef, 1));
};


/**
 * @param {!Array<!proto.tensorflow.NodeDef>} value
 * @return {!proto.tensorflow.GraphDef} returns this
*/
proto.tensorflow.GraphDef.prototype.setNodeList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.tensorflow.NodeDef=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.NodeDef}
 */
proto.tensorflow.GraphDef.prototype.addNode = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.tensorflow.NodeDef, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.GraphDef} returns this
 */
proto.tensorflow.GraphDef.prototype.clearNodeList = function() {
  return this.setNodeList([]);
};


/**
 * optional VersionDef versions = 4;
 * @return {?proto.tensorflow.VersionDef}
 */
proto.tensorflow.GraphDef.prototype.getVersions = function() {
  return /** @type{?proto.tensorflow.VersionDef} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.VersionDef, 4));
};


/**
 * @param {?proto.tensorflow.VersionDef|undefined} value
 * @return {!proto.tensorflow.GraphDef} returns this
*/
proto.tensorflow.GraphDef.prototype.setVersions = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.GraphDef} returns this
 */
proto.tensorflow.GraphDef.prototype.clearVersions = function() {
  return this.setVersions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.GraphDef.prototype.hasVersions = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional FunctionDefLibrary library = 2;
 * @return {?proto.tensorflow.FunctionDefLibrary}
 */
proto.tensorflow.GraphDef.prototype.getLibrary = function() {
  return /** @type{?proto.tensorflow.FunctionDefLibrary} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.FunctionDefLibrary, 2));
};


/**
 * @param {?proto.tensorflow.FunctionDefLibrary|undefined} value
 * @return {!proto.tensorflow.GraphDef} returns this
*/
proto.tensorflow.GraphDef.prototype.setLibrary = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.GraphDef} returns this
 */
proto.tensorflow.GraphDef.prototype.clearLibrary = function() {
  return this.setLibrary(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.GraphDef.prototype.hasLibrary = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.tensorflow.CollectionDef.oneofGroups_ = [[1,2,3,4,5]];

/**
 * @enum {number}
 */
proto.tensorflow.CollectionDef.KindCase = {
  KIND_NOT_SET: 0,
  NODE_LIST: 1,
  BYTES_LIST: 2,
  INT64_LIST: 3,
  FLOAT_LIST: 4,
  ANY_LIST: 5
};

/**
 * @return {proto.tensorflow.CollectionDef.KindCase}
 */
proto.tensorflow.CollectionDef.prototype.getKindCase = function() {
  return /** @type {proto.tensorflow.CollectionDef.KindCase} */(jspb.Message.computeOneofCase(this, proto.tensorflow.CollectionDef.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.CollectionDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.CollectionDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.CollectionDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    nodeList: (f = msg.getNodeList()) && proto.tensorflow.CollectionDef.NodeList.toObject(includeInstance, f),
    bytesList: (f = msg.getBytesList()) && proto.tensorflow.CollectionDef.BytesList.toObject(includeInstance, f),
    int64List: (f = msg.getInt64List()) && proto.tensorflow.CollectionDef.Int64List.toObject(includeInstance, f),
    floatList: (f = msg.getFloatList()) && proto.tensorflow.CollectionDef.FloatList.toObject(includeInstance, f),
    anyList: (f = msg.getAnyList()) && proto.tensorflow.CollectionDef.AnyList.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.CollectionDef}
 */
proto.tensorflow.CollectionDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.CollectionDef;
  return proto.tensorflow.CollectionDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.CollectionDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.CollectionDef}
 */
proto.tensorflow.CollectionDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.CollectionDef.NodeList;
      reader.readMessage(value,proto.tensorflow.CollectionDef.NodeList.deserializeBinaryFromReader);
      msg.setNodeList(value);
      break;
    case 2:
      var value = new proto.tensorflow.CollectionDef.BytesList;
      reader.readMessage(value,proto.tensorflow.CollectionDef.BytesList.deserializeBinaryFromReader);
      msg.setBytesList(value);
      break;
    case 3:
      var value = new proto.tensorflow.CollectionDef.Int64List;
      reader.readMessage(value,proto.tensorflow.CollectionDef.Int64List.deserializeBinaryFromReader);
      msg.setInt64List(value);
      break;
    case 4:
      var value = new proto.tensorflow.CollectionDef.FloatList;
      reader.readMessage(value,proto.tensorflow.CollectionDef.FloatList.deserializeBinaryFromReader);
      msg.setFloatList(value);
      break;
    case 5:
      var value = new proto.tensorflow.CollectionDef.AnyList;
      reader.readMessage(value,proto.tensorflow.CollectionDef.AnyList.deserializeBinaryFromReader);
      msg.setAnyList(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.CollectionDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.CollectionDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.CollectionDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getNodeList();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.tensorflow.CollectionDef.NodeList.serializeBinaryToWriter
    );
  }
  f = message.getBytesList();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.tensorflow.CollectionDef.BytesList.serializeBinaryToWriter
    );
  }
  f = message.getInt64List();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.tensorflow.CollectionDef.Int64List.serializeBinaryToWriter
    );
  }
  f = message.getFloatList();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.tensorflow.CollectionDef.FloatList.serializeBinaryToWriter
    );
  }
  f = message.getAnyList();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      proto.tensorflow.CollectionDef.AnyList.serializeBinaryToWriter
    );
  }
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.CollectionDef.NodeList.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.CollectionDef.NodeList.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.CollectionDef.NodeList.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.CollectionDef.NodeList} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.NodeList.toObject = function(includeInstance, msg) {
  var f, obj = {
    valueList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.CollectionDef.NodeList}
 */
proto.tensorflow.CollectionDef.NodeList.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.CollectionDef.NodeList;
  return proto.tensorflow.CollectionDef.NodeList.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.CollectionDef.NodeList} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.CollectionDef.NodeList}
 */
proto.tensorflow.CollectionDef.NodeList.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.addValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.CollectionDef.NodeList.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.CollectionDef.NodeList.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.CollectionDef.NodeList} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.NodeList.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getValueList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      1,
      f
    );
  }
};


/**
 * repeated string value = 1;
 * @return {!Array<string>}
 */
proto.tensorflow.CollectionDef.NodeList.prototype.getValueList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.tensorflow.CollectionDef.NodeList} returns this
 */
proto.tensorflow.CollectionDef.NodeList.prototype.setValueList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.CollectionDef.NodeList} returns this
 */
proto.tensorflow.CollectionDef.NodeList.prototype.addValue = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.CollectionDef.NodeList} returns this
 */
proto.tensorflow.CollectionDef.NodeList.prototype.clearValueList = function() {
  return this.setValueList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.CollectionDef.BytesList.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.CollectionDef.BytesList.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.CollectionDef.BytesList.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.CollectionDef.BytesList} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.BytesList.toObject = function(includeInstance, msg) {
  var f, obj = {
    valueList: msg.getValueList_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.CollectionDef.BytesList}
 */
proto.tensorflow.CollectionDef.BytesList.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.CollectionDef.BytesList;
  return proto.tensorflow.CollectionDef.BytesList.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.CollectionDef.BytesList} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.CollectionDef.BytesList}
 */
proto.tensorflow.CollectionDef.BytesList.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.addValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.CollectionDef.BytesList.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.CollectionDef.BytesList.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.CollectionDef.BytesList} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.BytesList.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getValueList_asU8();
  if (f.length > 0) {
    writer.writeRepeatedBytes(
      1,
      f
    );
  }
};


/**
 * repeated bytes value = 1;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.tensorflow.CollectionDef.BytesList.prototype.getValueList = function() {
  return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * repeated bytes value = 1;
 * This is a type-conversion wrapper around `getValueList()`
 * @return {!Array<string>}
 */
proto.tensorflow.CollectionDef.BytesList.prototype.getValueList_asB64 = function() {
  return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(
      this.getValueList()));
};


/**
 * repeated bytes value = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getValueList()`
 * @return {!Array<!Uint8Array>}
 */
proto.tensorflow.CollectionDef.BytesList.prototype.getValueList_asU8 = function() {
  return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(
      this.getValueList()));
};


/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.tensorflow.CollectionDef.BytesList} returns this
 */
proto.tensorflow.CollectionDef.BytesList.prototype.setValueList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.CollectionDef.BytesList} returns this
 */
proto.tensorflow.CollectionDef.BytesList.prototype.addValue = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.CollectionDef.BytesList} returns this
 */
proto.tensorflow.CollectionDef.BytesList.prototype.clearValueList = function() {
  return this.setValueList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.CollectionDef.Int64List.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.CollectionDef.Int64List.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.CollectionDef.Int64List.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.CollectionDef.Int64List} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.Int64List.toObject = function(includeInstance, msg) {
  var f, obj = {
    valueList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.CollectionDef.Int64List}
 */
proto.tensorflow.CollectionDef.Int64List.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.CollectionDef.Int64List;
  return proto.tensorflow.CollectionDef.Int64List.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.CollectionDef.Int64List} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.CollectionDef.Int64List}
 */
proto.tensorflow.CollectionDef.Int64List.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedInt64() : [reader.readInt64()]);
      for (var i = 0; i < values.length; i++) {
        msg.addValue(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.CollectionDef.Int64List.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.CollectionDef.Int64List.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.CollectionDef.Int64List} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.Int64List.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getValueList();
  if (f.length > 0) {
    writer.writePackedInt64(
      1,
      f
    );
  }
};


/**
 * repeated int64 value = 1;
 * @return {!Array<number>}
 */
proto.tensorflow.CollectionDef.Int64List.prototype.getValueList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.CollectionDef.Int64List} returns this
 */
proto.tensorflow.CollectionDef.Int64List.prototype.setValueList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.CollectionDef.Int64List} returns this
 */
proto.tensorflow.CollectionDef.Int64List.prototype.addValue = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.CollectionDef.Int64List} returns this
 */
proto.tensorflow.CollectionDef.Int64List.prototype.clearValueList = function() {
  return this.setValueList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.CollectionDef.FloatList.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.CollectionDef.FloatList.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.CollectionDef.FloatList.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.CollectionDef.FloatList} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.FloatList.toObject = function(includeInstance, msg) {
  var f, obj = {
    valueList: (f = jspb.Message.getRepeatedFloatingPointField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.CollectionDef.FloatList}
 */
proto.tensorflow.CollectionDef.FloatList.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.CollectionDef.FloatList;
  return proto.tensorflow.CollectionDef.FloatList.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.CollectionDef.FloatList} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.CollectionDef.FloatList}
 */
proto.tensorflow.CollectionDef.FloatList.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedFloat() : [reader.readFloat()]);
      for (var i = 0; i < values.length; i++) {
        msg.addValue(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.CollectionDef.FloatList.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.CollectionDef.FloatList.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.CollectionDef.FloatList} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.FloatList.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getValueList();
  if (f.length > 0) {
    writer.writePackedFloat(
      1,
      f
    );
  }
};


/**
 * repeated float value = 1;
 * @return {!Array<number>}
 */
proto.tensorflow.CollectionDef.FloatList.prototype.getValueList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedFloatingPointField(this, 1));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.CollectionDef.FloatList} returns this
 */
proto.tensorflow.CollectionDef.FloatList.prototype.setValueList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.CollectionDef.FloatList} returns this
 */
proto.tensorflow.CollectionDef.FloatList.prototype.addValue = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.CollectionDef.FloatList} returns this
 */
proto.tensorflow.CollectionDef.FloatList.prototype.clearValueList = function() {
  return this.setValueList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.CollectionDef.AnyList.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.CollectionDef.AnyList.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.CollectionDef.AnyList.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.CollectionDef.AnyList} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.AnyList.toObject = function(includeInstance, msg) {
  var f, obj = {
    valueList: jspb.Message.toObjectList(msg.getValueList(),
    proto.tensorflow.Any.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.CollectionDef.AnyList}
 */
proto.tensorflow.CollectionDef.AnyList.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.CollectionDef.AnyList;
  return proto.tensorflow.CollectionDef.AnyList.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.CollectionDef.AnyList} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.CollectionDef.AnyList}
 */
proto.tensorflow.CollectionDef.AnyList.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.Any;
      reader.readMessage(value,proto.tensorflow.Any.deserializeBinaryFromReader);
      msg.addValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.CollectionDef.AnyList.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.CollectionDef.AnyList.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.CollectionDef.AnyList} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.CollectionDef.AnyList.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getValueList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.tensorflow.Any.serializeBinaryToWriter
    );
  }
};


/**
 * repeated Any value = 1;
 * @return {!Array<!proto.tensorflow.Any>}
 */
proto.tensorflow.CollectionDef.AnyList.prototype.getValueList = function() {
  return /** @type{!Array<!proto.tensorflow.Any>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.Any, 1));
};


/**
 * @param {!Array<!proto.tensorflow.Any>} value
 * @return {!proto.tensorflow.CollectionDef.AnyList} returns this
*/
proto.tensorflow.CollectionDef.AnyList.prototype.setValueList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.tensorflow.Any=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Any}
 */
proto.tensorflow.CollectionDef.AnyList.prototype.addValue = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.tensorflow.Any, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.CollectionDef.AnyList} returns this
 */
proto.tensorflow.CollectionDef.AnyList.prototype.clearValueList = function() {
  return this.setValueList([]);
};


/**
 * optional NodeList node_list = 1;
 * @return {?proto.tensorflow.CollectionDef.NodeList}
 */
proto.tensorflow.CollectionDef.prototype.getNodeList = function() {
  return /** @type{?proto.tensorflow.CollectionDef.NodeList} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.CollectionDef.NodeList, 1));
};


/**
 * @param {?proto.tensorflow.CollectionDef.NodeList|undefined} value
 * @return {!proto.tensorflow.CollectionDef} returns this
*/
proto.tensorflow.CollectionDef.prototype.setNodeList = function(value) {
  return jspb.Message.setOneofWrapperField(this, 1, proto.tensorflow.CollectionDef.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.CollectionDef} returns this
 */
proto.tensorflow.CollectionDef.prototype.clearNodeList = function() {
  return this.setNodeList(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.CollectionDef.prototype.hasNodeList = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional BytesList bytes_list = 2;
 * @return {?proto.tensorflow.CollectionDef.BytesList}
 */
proto.tensorflow.CollectionDef.prototype.getBytesList = function() {
  return /** @type{?proto.tensorflow.CollectionDef.BytesList} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.CollectionDef.BytesList, 2));
};


/**
 * @param {?proto.tensorflow.CollectionDef.BytesList|undefined} value
 * @return {!proto.tensorflow.CollectionDef} returns this
*/
proto.tensorflow.CollectionDef.prototype.setBytesList = function(value) {
  return jspb.Message.setOneofWrapperField(this, 2, proto.tensorflow.CollectionDef.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.CollectionDef} returns this
 */
proto.tensorflow.CollectionDef.prototype.clearBytesList = function() {
  return this.setBytesList(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.CollectionDef.prototype.hasBytesList = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional Int64List int64_list = 3;
 * @return {?proto.tensorflow.CollectionDef.Int64List}
 */
proto.tensorflow.CollectionDef.prototype.getInt64List = function() {
  return /** @type{?proto.tensorflow.CollectionDef.Int64List} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.CollectionDef.Int64List, 3));
};


/**
 * @param {?proto.tensorflow.CollectionDef.Int64List|undefined} value
 * @return {!proto.tensorflow.CollectionDef} returns this
*/
proto.tensorflow.CollectionDef.prototype.setInt64List = function(value) {
  return jspb.Message.setOneofWrapperField(this, 3, proto.tensorflow.CollectionDef.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.CollectionDef} returns this
 */
proto.tensorflow.CollectionDef.prototype.clearInt64List = function() {
  return this.setInt64List(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.CollectionDef.prototype.hasInt64List = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional FloatList float_list = 4;
 * @return {?proto.tensorflow.CollectionDef.FloatList}
 */
proto.tensorflow.CollectionDef.prototype.getFloatList = function() {
  return /** @type{?proto.tensorflow.CollectionDef.FloatList} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.CollectionDef.FloatList, 4));
};


/**
 * @param {?proto.tensorflow.CollectionDef.FloatList|undefined} value
 * @return {!proto.tensorflow.CollectionDef} returns this
*/
proto.tensorflow.CollectionDef.prototype.setFloatList = function(value) {
  return jspb.Message.setOneofWrapperField(this, 4, proto.tensorflow.CollectionDef.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.CollectionDef} returns this
 */
proto.tensorflow.CollectionDef.prototype.clearFloatList = function() {
  return this.setFloatList(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.CollectionDef.prototype.hasFloatList = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional AnyList any_list = 5;
 * @return {?proto.tensorflow.CollectionDef.AnyList}
 */
proto.tensorflow.CollectionDef.prototype.getAnyList = function() {
  return /** @type{?proto.tensorflow.CollectionDef.AnyList} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.CollectionDef.AnyList, 5));
};


/**
 * @param {?proto.tensorflow.CollectionDef.AnyList|undefined} value
 * @return {!proto.tensorflow.CollectionDef} returns this
*/
proto.tensorflow.CollectionDef.prototype.setAnyList = function(value) {
  return jspb.Message.setOneofWrapperField(this, 5, proto.tensorflow.CollectionDef.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.CollectionDef} returns this
 */
proto.tensorflow.CollectionDef.prototype.clearAnyList = function() {
  return this.setAnyList(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.CollectionDef.prototype.hasAnyList = function() {
  return jspb.Message.getField(this, 5) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.SaverDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.SaverDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.SaverDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SaverDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    filenameTensorName: jspb.Message.getFieldWithDefault(msg, 1, ""),
    saveTensorName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    restoreOpName: jspb.Message.getFieldWithDefault(msg, 3, ""),
    maxToKeep: jspb.Message.getFieldWithDefault(msg, 4, 0),
    sharded: jspb.Message.getBooleanFieldWithDefault(msg, 5, false),
    keepCheckpointEveryNHours: jspb.Message.getFloatingPointFieldWithDefault(msg, 6, 0.0),
    version: jspb.Message.getFieldWithDefault(msg, 7, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.SaverDef}
 */
proto.tensorflow.SaverDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.SaverDef;
  return proto.tensorflow.SaverDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.SaverDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.SaverDef}
 */
proto.tensorflow.SaverDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setFilenameTensorName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setSaveTensorName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setRestoreOpName(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMaxToKeep(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setSharded(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setKeepCheckpointEveryNHours(value);
      break;
    case 7:
      var value = /** @type {!proto.tensorflow.SaverDef.CheckpointFormatVersion} */ (reader.readEnum());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.SaverDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.SaverDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.SaverDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SaverDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFilenameTensorName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getSaveTensorName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getRestoreOpName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getMaxToKeep();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getSharded();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
  f = message.getKeepCheckpointEveryNHours();
  if (f !== 0.0) {
    writer.writeFloat(
      6,
      f
    );
  }
  f = message.getVersion();
  if (f !== 0.0) {
    writer.writeEnum(
      7,
      f
    );
  }
};


/**
 * @enum {number}
 */
proto.tensorflow.SaverDef.CheckpointFormatVersion = {
  LEGACY: 0,
  V1: 1,
  V2: 2
};

/**
 * optional string filename_tensor_name = 1;
 * @return {string}
 */
proto.tensorflow.SaverDef.prototype.getFilenameTensorName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.SaverDef} returns this
 */
proto.tensorflow.SaverDef.prototype.setFilenameTensorName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string save_tensor_name = 2;
 * @return {string}
 */
proto.tensorflow.SaverDef.prototype.getSaveTensorName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.SaverDef} returns this
 */
proto.tensorflow.SaverDef.prototype.setSaveTensorName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string restore_op_name = 3;
 * @return {string}
 */
proto.tensorflow.SaverDef.prototype.getRestoreOpName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.SaverDef} returns this
 */
proto.tensorflow.SaverDef.prototype.setRestoreOpName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 max_to_keep = 4;
 * @return {number}
 */
proto.tensorflow.SaverDef.prototype.getMaxToKeep = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.SaverDef} returns this
 */
proto.tensorflow.SaverDef.prototype.setMaxToKeep = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional bool sharded = 5;
 * @return {boolean}
 */
proto.tensorflow.SaverDef.prototype.getSharded = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.tensorflow.SaverDef} returns this
 */
proto.tensorflow.SaverDef.prototype.setSharded = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};


/**
 * optional float keep_checkpoint_every_n_hours = 6;
 * @return {number}
 */
proto.tensorflow.SaverDef.prototype.getKeepCheckpointEveryNHours = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 6, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.SaverDef} returns this
 */
proto.tensorflow.SaverDef.prototype.setKeepCheckpointEveryNHours = function(value) {
  return jspb.Message.setProto3FloatField(this, 6, value);
};


/**
 * optional CheckpointFormatVersion version = 7;
 * @return {!proto.tensorflow.SaverDef.CheckpointFormatVersion}
 */
proto.tensorflow.SaverDef.prototype.getVersion = function() {
  return /** @type {!proto.tensorflow.SaverDef.CheckpointFormatVersion} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {!proto.tensorflow.SaverDef.CheckpointFormatVersion} value
 * @return {!proto.tensorflow.SaverDef} returns this
 */
proto.tensorflow.SaverDef.prototype.setVersion = function(value) {
  return jspb.Message.setProto3EnumField(this, 7, value);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.tensorflow.TensorInfo.oneofGroups_ = [[1,4]];

/**
 * @enum {number}
 */
proto.tensorflow.TensorInfo.EncodingCase = {
  ENCODING_NOT_SET: 0,
  NAME: 1,
  COO_SPARSE: 4
};

/**
 * @return {proto.tensorflow.TensorInfo.EncodingCase}
 */
proto.tensorflow.TensorInfo.prototype.getEncodingCase = function() {
  return /** @type {proto.tensorflow.TensorInfo.EncodingCase} */(jspb.Message.computeOneofCase(this, proto.tensorflow.TensorInfo.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.TensorInfo.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.TensorInfo.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.TensorInfo} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.TensorInfo.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    cooSparse: (f = msg.getCooSparse()) && proto.tensorflow.TensorInfo.CooSparse.toObject(includeInstance, f),
    dtype: jspb.Message.getFieldWithDefault(msg, 2, 0),
    tensorShape: (f = msg.getTensorShape()) && proto.tensorflow.TensorShape.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.TensorInfo}
 */
proto.tensorflow.TensorInfo.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.TensorInfo;
  return proto.tensorflow.TensorInfo.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.TensorInfo} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.TensorInfo}
 */
proto.tensorflow.TensorInfo.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = new proto.tensorflow.TensorInfo.CooSparse;
      reader.readMessage(value,proto.tensorflow.TensorInfo.CooSparse.deserializeBinaryFromReader);
      msg.setCooSparse(value);
      break;
    case 2:
      var value = /** @type {!proto.tensorflow.DataType} */ (reader.readEnum());
      msg.setDtype(value);
      break;
    case 3:
      var value = new proto.tensorflow.TensorShape;
      reader.readMessage(value,proto.tensorflow.TensorShape.deserializeBinaryFromReader);
      msg.setTensorShape(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.TensorInfo.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.TensorInfo.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.TensorInfo} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.TensorInfo.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {string} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getCooSparse();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.tensorflow.TensorInfo.CooSparse.serializeBinaryToWriter
    );
  }
  f = message.getDtype();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getTensorShape();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.tensorflow.TensorShape.serializeBinaryToWriter
    );
  }
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.TensorInfo.CooSparse.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.TensorInfo.CooSparse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.TensorInfo.CooSparse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.TensorInfo.CooSparse.toObject = function(includeInstance, msg) {
  var f, obj = {
    valuesTensorName: jspb.Message.getFieldWithDefault(msg, 1, ""),
    indicesTensorName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    denseShapeTensorName: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.TensorInfo.CooSparse}
 */
proto.tensorflow.TensorInfo.CooSparse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.TensorInfo.CooSparse;
  return proto.tensorflow.TensorInfo.CooSparse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.TensorInfo.CooSparse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.TensorInfo.CooSparse}
 */
proto.tensorflow.TensorInfo.CooSparse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setValuesTensorName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setIndicesTensorName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDenseShapeTensorName(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.TensorInfo.CooSparse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.TensorInfo.CooSparse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.TensorInfo.CooSparse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.TensorInfo.CooSparse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getValuesTensorName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getIndicesTensorName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDenseShapeTensorName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string values_tensor_name = 1;
 * @return {string}
 */
proto.tensorflow.TensorInfo.CooSparse.prototype.getValuesTensorName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.TensorInfo.CooSparse} returns this
 */
proto.tensorflow.TensorInfo.CooSparse.prototype.setValuesTensorName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string indices_tensor_name = 2;
 * @return {string}
 */
proto.tensorflow.TensorInfo.CooSparse.prototype.getIndicesTensorName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.TensorInfo.CooSparse} returns this
 */
proto.tensorflow.TensorInfo.CooSparse.prototype.setIndicesTensorName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string dense_shape_tensor_name = 3;
 * @return {string}
 */
proto.tensorflow.TensorInfo.CooSparse.prototype.getDenseShapeTensorName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.TensorInfo.CooSparse} returns this
 */
proto.tensorflow.TensorInfo.CooSparse.prototype.setDenseShapeTensorName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string name = 1;
 * @return {string}
 */
proto.tensorflow.TensorInfo.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.TensorInfo} returns this
 */
proto.tensorflow.TensorInfo.prototype.setName = function(value) {
  return jspb.Message.setOneofField(this, 1, proto.tensorflow.TensorInfo.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.tensorflow.TensorInfo} returns this
 */
proto.tensorflow.TensorInfo.prototype.clearName = function() {
  return jspb.Message.setOneofField(this, 1, proto.tensorflow.TensorInfo.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.TensorInfo.prototype.hasName = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional CooSparse coo_sparse = 4;
 * @return {?proto.tensorflow.TensorInfo.CooSparse}
 */
proto.tensorflow.TensorInfo.prototype.getCooSparse = function() {
  return /** @type{?proto.tensorflow.TensorInfo.CooSparse} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.TensorInfo.CooSparse, 4));
};


/**
 * @param {?proto.tensorflow.TensorInfo.CooSparse|undefined} value
 * @return {!proto.tensorflow.TensorInfo} returns this
*/
proto.tensorflow.TensorInfo.prototype.setCooSparse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 4, proto.tensorflow.TensorInfo.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.TensorInfo} returns this
 */
proto.tensorflow.TensorInfo.prototype.clearCooSparse = function() {
  return this.setCooSparse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.TensorInfo.prototype.hasCooSparse = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional DataType dtype = 2;
 * @return {!proto.tensorflow.DataType}
 */
proto.tensorflow.TensorInfo.prototype.getDtype = function() {
  return /** @type {!proto.tensorflow.DataType} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.tensorflow.DataType} value
 * @return {!proto.tensorflow.TensorInfo} returns this
 */
proto.tensorflow.TensorInfo.prototype.setDtype = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional TensorShape tensor_shape = 3;
 * @return {?proto.tensorflow.TensorShape}
 */
proto.tensorflow.TensorInfo.prototype.getTensorShape = function() {
  return /** @type{?proto.tensorflow.TensorShape} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.TensorShape, 3));
};


/**
 * @param {?proto.tensorflow.TensorShape|undefined} value
 * @return {!proto.tensorflow.TensorInfo} returns this
*/
proto.tensorflow.TensorInfo.prototype.setTensorShape = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.TensorInfo} returns this
 */
proto.tensorflow.TensorInfo.prototype.clearTensorShape = function() {
  return this.setTensorShape(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.TensorInfo.prototype.hasTensorShape = function() {
  return jspb.Message.getField(this, 3) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.SignatureDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.SignatureDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.SignatureDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SignatureDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    inputsMap: (f = msg.getInputsMap()) ? f.toObject(includeInstance, proto.tensorflow.TensorInfo.toObject) : [],
    outputsMap: (f = msg.getOutputsMap()) ? f.toObject(includeInstance, proto.tensorflow.TensorInfo.toObject) : [],
    methodName: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.SignatureDef}
 */
proto.tensorflow.SignatureDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.SignatureDef;
  return proto.tensorflow.SignatureDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.SignatureDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.SignatureDef}
 */
proto.tensorflow.SignatureDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = msg.getInputsMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readMessage, proto.tensorflow.TensorInfo.deserializeBinaryFromReader, "", new proto.tensorflow.TensorInfo());
         });
      break;
    case 2:
      var value = msg.getOutputsMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readMessage, proto.tensorflow.TensorInfo.deserializeBinaryFromReader, "", new proto.tensorflow.TensorInfo());
         });
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setMethodName(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.SignatureDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.SignatureDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.SignatureDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SignatureDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getInputsMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(1, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeMessage, proto.tensorflow.TensorInfo.serializeBinaryToWriter);
  }
  f = message.getOutputsMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(2, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeMessage, proto.tensorflow.TensorInfo.serializeBinaryToWriter);
  }
  f = message.getMethodName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * map<string, TensorInfo> inputs = 1;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,!proto.tensorflow.TensorInfo>}
 */
proto.tensorflow.SignatureDef.prototype.getInputsMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<string,!proto.tensorflow.TensorInfo>} */ (
      jspb.Message.getMapField(this, 1, opt_noLazyCreate,
      proto.tensorflow.TensorInfo));
};


/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.tensorflow.SignatureDef} returns this
 */
proto.tensorflow.SignatureDef.prototype.clearInputsMap = function() {
  this.getInputsMap().clear();
  return this;};


/**
 * map<string, TensorInfo> outputs = 2;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,!proto.tensorflow.TensorInfo>}
 */
proto.tensorflow.SignatureDef.prototype.getOutputsMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<string,!proto.tensorflow.TensorInfo>} */ (
      jspb.Message.getMapField(this, 2, opt_noLazyCreate,
      proto.tensorflow.TensorInfo));
};


/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.tensorflow.SignatureDef} returns this
 */
proto.tensorflow.SignatureDef.prototype.clearOutputsMap = function() {
  this.getOutputsMap().clear();
  return this;};


/**
 * optional string method_name = 3;
 * @return {string}
 */
proto.tensorflow.SignatureDef.prototype.getMethodName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.SignatureDef} returns this
 */
proto.tensorflow.SignatureDef.prototype.setMethodName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.AssetFileDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.AssetFileDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.AssetFileDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.AssetFileDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    tensorInfo: (f = msg.getTensorInfo()) && proto.tensorflow.TensorInfo.toObject(includeInstance, f),
    filename: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.AssetFileDef}
 */
proto.tensorflow.AssetFileDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.AssetFileDef;
  return proto.tensorflow.AssetFileDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.AssetFileDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.AssetFileDef}
 */
proto.tensorflow.AssetFileDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.TensorInfo;
      reader.readMessage(value,proto.tensorflow.TensorInfo.deserializeBinaryFromReader);
      msg.setTensorInfo(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setFilename(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.AssetFileDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.AssetFileDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.AssetFileDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.AssetFileDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTensorInfo();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.tensorflow.TensorInfo.serializeBinaryToWriter
    );
  }
  f = message.getFilename();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional TensorInfo tensor_info = 1;
 * @return {?proto.tensorflow.TensorInfo}
 */
proto.tensorflow.AssetFileDef.prototype.getTensorInfo = function() {
  return /** @type{?proto.tensorflow.TensorInfo} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.TensorInfo, 1));
};


/**
 * @param {?proto.tensorflow.TensorInfo|undefined} value
 * @return {!proto.tensorflow.AssetFileDef} returns this
*/
proto.tensorflow.AssetFileDef.prototype.setTensorInfo = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.AssetFileDef} returns this
 */
proto.tensorflow.AssetFileDef.prototype.clearTensorInfo = function() {
  return this.setTensorInfo(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.AssetFileDef.prototype.hasTensorInfo = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string filename = 2;
 * @return {string}
 */
proto.tensorflow.AssetFileDef.prototype.getFilename = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.AssetFileDef} returns this
 */
proto.tensorflow.AssetFileDef.prototype.setFilename = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.OpDef.repeatedFields_ = [2,3,4];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.OpDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.OpDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.OpDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.OpDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    inputArgList: jspb.Message.toObjectList(msg.getInputArgList(),
    proto.tensorflow.OpDef.ArgDef.toObject, includeInstance),
    outputArgList: jspb.Message.toObjectList(msg.getOutputArgList(),
    proto.tensorflow.OpDef.ArgDef.toObject, includeInstance),
    attrList: jspb.Message.toObjectList(msg.getAttrList(),
    proto.tensorflow.OpDef.AttrDef.toObject, includeInstance),
    deprecation: (f = msg.getDeprecation()) && proto.tensorflow.OpDef.OpDeprecation.toObject(includeInstance, f),
    summary: jspb.Message.getFieldWithDefault(msg, 5, ""),
    description: jspb.Message.getFieldWithDefault(msg, 6, ""),
    isCommutative: jspb.Message.getBooleanFieldWithDefault(msg, 18, false),
    isAggregate: jspb.Message.getBooleanFieldWithDefault(msg, 16, false),
    isStateful: jspb.Message.getBooleanFieldWithDefault(msg, 17, false),
    allowsUninitializedInput: jspb.Message.getBooleanFieldWithDefault(msg, 19, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.OpDef}
 */
proto.tensorflow.OpDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.OpDef;
  return proto.tensorflow.OpDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.OpDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.OpDef}
 */
proto.tensorflow.OpDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = new proto.tensorflow.OpDef.ArgDef;
      reader.readMessage(value,proto.tensorflow.OpDef.ArgDef.deserializeBinaryFromReader);
      msg.addInputArg(value);
      break;
    case 3:
      var value = new proto.tensorflow.OpDef.ArgDef;
      reader.readMessage(value,proto.tensorflow.OpDef.ArgDef.deserializeBinaryFromReader);
      msg.addOutputArg(value);
      break;
    case 4:
      var value = new proto.tensorflow.OpDef.AttrDef;
      reader.readMessage(value,proto.tensorflow.OpDef.AttrDef.deserializeBinaryFromReader);
      msg.addAttr(value);
      break;
    case 8:
      var value = new proto.tensorflow.OpDef.OpDeprecation;
      reader.readMessage(value,proto.tensorflow.OpDef.OpDeprecation.deserializeBinaryFromReader);
      msg.setDeprecation(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setSummary(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 18:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsCommutative(value);
      break;
    case 16:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsAggregate(value);
      break;
    case 17:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsStateful(value);
      break;
    case 19:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setAllowsUninitializedInput(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.OpDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.OpDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.OpDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.OpDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getInputArgList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.tensorflow.OpDef.ArgDef.serializeBinaryToWriter
    );
  }
  f = message.getOutputArgList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.tensorflow.OpDef.ArgDef.serializeBinaryToWriter
    );
  }
  f = message.getAttrList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      4,
      f,
      proto.tensorflow.OpDef.AttrDef.serializeBinaryToWriter
    );
  }
  f = message.getDeprecation();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      proto.tensorflow.OpDef.OpDeprecation.serializeBinaryToWriter
    );
  }
  f = message.getSummary();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getDescription();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getIsCommutative();
  if (f) {
    writer.writeBool(
      18,
      f
    );
  }
  f = message.getIsAggregate();
  if (f) {
    writer.writeBool(
      16,
      f
    );
  }
  f = message.getIsStateful();
  if (f) {
    writer.writeBool(
      17,
      f
    );
  }
  f = message.getAllowsUninitializedInput();
  if (f) {
    writer.writeBool(
      19,
      f
    );
  }
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.OpDef.ArgDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.OpDef.ArgDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.OpDef.ArgDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.OpDef.ArgDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    description: jspb.Message.getFieldWithDefault(msg, 2, ""),
    type: jspb.Message.getFieldWithDefault(msg, 3, 0),
    typeAttr: jspb.Message.getFieldWithDefault(msg, 4, ""),
    numberAttr: jspb.Message.getFieldWithDefault(msg, 5, ""),
    typeListAttr: jspb.Message.getFieldWithDefault(msg, 6, ""),
    isRef: jspb.Message.getBooleanFieldWithDefault(msg, 16, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.OpDef.ArgDef}
 */
proto.tensorflow.OpDef.ArgDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.OpDef.ArgDef;
  return proto.tensorflow.OpDef.ArgDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.OpDef.ArgDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.OpDef.ArgDef}
 */
proto.tensorflow.OpDef.ArgDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 3:
      var value = /** @type {!proto.tensorflow.DataType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setTypeAttr(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setNumberAttr(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setTypeListAttr(value);
      break;
    case 16:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsRef(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.OpDef.ArgDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.OpDef.ArgDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.OpDef.ArgDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.OpDef.ArgDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getDescription();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getTypeAttr();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getNumberAttr();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getTypeListAttr();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getIsRef();
  if (f) {
    writer.writeBool(
      16,
      f
    );
  }
};


/**
 * optional string name = 1;
 * @return {string}
 */
proto.tensorflow.OpDef.ArgDef.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef.ArgDef} returns this
 */
proto.tensorflow.OpDef.ArgDef.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string description = 2;
 * @return {string}
 */
proto.tensorflow.OpDef.ArgDef.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef.ArgDef} returns this
 */
proto.tensorflow.OpDef.ArgDef.prototype.setDescription = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional DataType type = 3;
 * @return {!proto.tensorflow.DataType}
 */
proto.tensorflow.OpDef.ArgDef.prototype.getType = function() {
  return /** @type {!proto.tensorflow.DataType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.tensorflow.DataType} value
 * @return {!proto.tensorflow.OpDef.ArgDef} returns this
 */
proto.tensorflow.OpDef.ArgDef.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional string type_attr = 4;
 * @return {string}
 */
proto.tensorflow.OpDef.ArgDef.prototype.getTypeAttr = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef.ArgDef} returns this
 */
proto.tensorflow.OpDef.ArgDef.prototype.setTypeAttr = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string number_attr = 5;
 * @return {string}
 */
proto.tensorflow.OpDef.ArgDef.prototype.getNumberAttr = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef.ArgDef} returns this
 */
proto.tensorflow.OpDef.ArgDef.prototype.setNumberAttr = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string type_list_attr = 6;
 * @return {string}
 */
proto.tensorflow.OpDef.ArgDef.prototype.getTypeListAttr = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef.ArgDef} returns this
 */
proto.tensorflow.OpDef.ArgDef.prototype.setTypeListAttr = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional bool is_ref = 16;
 * @return {boolean}
 */
proto.tensorflow.OpDef.ArgDef.prototype.getIsRef = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 16, false));
};


/**
 * @param {boolean} value
 * @return {!proto.tensorflow.OpDef.ArgDef} returns this
 */
proto.tensorflow.OpDef.ArgDef.prototype.setIsRef = function(value) {
  return jspb.Message.setProto3BooleanField(this, 16, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.OpDef.AttrDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.OpDef.AttrDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.OpDef.AttrDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.OpDef.AttrDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    type: jspb.Message.getFieldWithDefault(msg, 2, ""),
    defaultValue: (f = msg.getDefaultValue()) && proto.tensorflow.AttrValue.toObject(includeInstance, f),
    description: jspb.Message.getFieldWithDefault(msg, 4, ""),
    hasMinimum: jspb.Message.getBooleanFieldWithDefault(msg, 5, false),
    minimum: jspb.Message.getFieldWithDefault(msg, 6, 0),
    allowedValues: (f = msg.getAllowedValues()) && proto.tensorflow.AttrValue.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.OpDef.AttrDef}
 */
proto.tensorflow.OpDef.AttrDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.OpDef.AttrDef;
  return proto.tensorflow.OpDef.AttrDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.OpDef.AttrDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.OpDef.AttrDef}
 */
proto.tensorflow.OpDef.AttrDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setType(value);
      break;
    case 3:
      var value = new proto.tensorflow.AttrValue;
      reader.readMessage(value,proto.tensorflow.AttrValue.deserializeBinaryFromReader);
      msg.setDefaultValue(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setHasMinimum(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setMinimum(value);
      break;
    case 7:
      var value = new proto.tensorflow.AttrValue;
      reader.readMessage(value,proto.tensorflow.AttrValue.deserializeBinaryFromReader);
      msg.setAllowedValues(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.OpDef.AttrDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.OpDef.AttrDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.OpDef.AttrDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.OpDef.AttrDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getType();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDefaultValue();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.tensorflow.AttrValue.serializeBinaryToWriter
    );
  }
  f = message.getDescription();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getHasMinimum();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
  f = message.getMinimum();
  if (f !== 0) {
    writer.writeInt64(
      6,
      f
    );
  }
  f = message.getAllowedValues();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      proto.tensorflow.AttrValue.serializeBinaryToWriter
    );
  }
};


/**
 * optional string name = 1;
 * @return {string}
 */
proto.tensorflow.OpDef.AttrDef.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef.AttrDef} returns this
 */
proto.tensorflow.OpDef.AttrDef.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string type = 2;
 * @return {string}
 */
proto.tensorflow.OpDef.AttrDef.prototype.getType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef.AttrDef} returns this
 */
proto.tensorflow.OpDef.AttrDef.prototype.setType = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional AttrValue default_value = 3;
 * @return {?proto.tensorflow.AttrValue}
 */
proto.tensorflow.OpDef.AttrDef.prototype.getDefaultValue = function() {
  return /** @type{?proto.tensorflow.AttrValue} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.AttrValue, 3));
};


/**
 * @param {?proto.tensorflow.AttrValue|undefined} value
 * @return {!proto.tensorflow.OpDef.AttrDef} returns this
*/
proto.tensorflow.OpDef.AttrDef.prototype.setDefaultValue = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.OpDef.AttrDef} returns this
 */
proto.tensorflow.OpDef.AttrDef.prototype.clearDefaultValue = function() {
  return this.setDefaultValue(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.OpDef.AttrDef.prototype.hasDefaultValue = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional string description = 4;
 * @return {string}
 */
proto.tensorflow.OpDef.AttrDef.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef.AttrDef} returns this
 */
proto.tensorflow.OpDef.AttrDef.prototype.setDescription = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional bool has_minimum = 5;
 * @return {boolean}
 */
proto.tensorflow.OpDef.AttrDef.prototype.getHasMinimum = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.tensorflow.OpDef.AttrDef} returns this
 */
proto.tensorflow.OpDef.AttrDef.prototype.setHasMinimum = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};


/**
 * optional int64 minimum = 6;
 * @return {number}
 */
proto.tensorflow.OpDef.AttrDef.prototype.getMinimum = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.OpDef.AttrDef} returns this
 */
proto.tensorflow.OpDef.AttrDef.prototype.setMinimum = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional AttrValue allowed_values = 7;
 * @return {?proto.tensorflow.AttrValue}
 */
proto.tensorflow.OpDef.AttrDef.prototype.getAllowedValues = function() {
  return /** @type{?proto.tensorflow.AttrValue} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.AttrValue, 7));
};


/**
 * @param {?proto.tensorflow.AttrValue|undefined} value
 * @return {!proto.tensorflow.OpDef.AttrDef} returns this
*/
proto.tensorflow.OpDef.AttrDef.prototype.setAllowedValues = function(value) {
  return jspb.Message.setWrapperField(this, 7, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.OpDef.AttrDef} returns this
 */
proto.tensorflow.OpDef.AttrDef.prototype.clearAllowedValues = function() {
  return this.setAllowedValues(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.OpDef.AttrDef.prototype.hasAllowedValues = function() {
  return jspb.Message.getField(this, 7) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.OpDef.OpDeprecation.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.OpDef.OpDeprecation.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.OpDef.OpDeprecation} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.OpDef.OpDeprecation.toObject = function(includeInstance, msg) {
  var f, obj = {
    version: jspb.Message.getFieldWithDefault(msg, 1, 0),
    explanation: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.OpDef.OpDeprecation}
 */
proto.tensorflow.OpDef.OpDeprecation.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.OpDef.OpDeprecation;
  return proto.tensorflow.OpDef.OpDeprecation.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.OpDef.OpDeprecation} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.OpDef.OpDeprecation}
 */
proto.tensorflow.OpDef.OpDeprecation.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setVersion(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setExplanation(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.OpDef.OpDeprecation.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.OpDef.OpDeprecation.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.OpDef.OpDeprecation} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.OpDef.OpDeprecation.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVersion();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getExplanation();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional int32 version = 1;
 * @return {number}
 */
proto.tensorflow.OpDef.OpDeprecation.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.OpDef.OpDeprecation} returns this
 */
proto.tensorflow.OpDef.OpDeprecation.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string explanation = 2;
 * @return {string}
 */
proto.tensorflow.OpDef.OpDeprecation.prototype.getExplanation = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef.OpDeprecation} returns this
 */
proto.tensorflow.OpDef.OpDeprecation.prototype.setExplanation = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string name = 1;
 * @return {string}
 */
proto.tensorflow.OpDef.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated ArgDef input_arg = 2;
 * @return {!Array<!proto.tensorflow.OpDef.ArgDef>}
 */
proto.tensorflow.OpDef.prototype.getInputArgList = function() {
  return /** @type{!Array<!proto.tensorflow.OpDef.ArgDef>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.OpDef.ArgDef, 2));
};


/**
 * @param {!Array<!proto.tensorflow.OpDef.ArgDef>} value
 * @return {!proto.tensorflow.OpDef} returns this
*/
proto.tensorflow.OpDef.prototype.setInputArgList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.tensorflow.OpDef.ArgDef=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.OpDef.ArgDef}
 */
proto.tensorflow.OpDef.prototype.addInputArg = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.tensorflow.OpDef.ArgDef, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.clearInputArgList = function() {
  return this.setInputArgList([]);
};


/**
 * repeated ArgDef output_arg = 3;
 * @return {!Array<!proto.tensorflow.OpDef.ArgDef>}
 */
proto.tensorflow.OpDef.prototype.getOutputArgList = function() {
  return /** @type{!Array<!proto.tensorflow.OpDef.ArgDef>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.OpDef.ArgDef, 3));
};


/**
 * @param {!Array<!proto.tensorflow.OpDef.ArgDef>} value
 * @return {!proto.tensorflow.OpDef} returns this
*/
proto.tensorflow.OpDef.prototype.setOutputArgList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.tensorflow.OpDef.ArgDef=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.OpDef.ArgDef}
 */
proto.tensorflow.OpDef.prototype.addOutputArg = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.tensorflow.OpDef.ArgDef, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.clearOutputArgList = function() {
  return this.setOutputArgList([]);
};


/**
 * repeated AttrDef attr = 4;
 * @return {!Array<!proto.tensorflow.OpDef.AttrDef>}
 */
proto.tensorflow.OpDef.prototype.getAttrList = function() {
  return /** @type{!Array<!proto.tensorflow.OpDef.AttrDef>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.OpDef.AttrDef, 4));
};


/**
 * @param {!Array<!proto.tensorflow.OpDef.AttrDef>} value
 * @return {!proto.tensorflow.OpDef} returns this
*/
proto.tensorflow.OpDef.prototype.setAttrList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 4, value);
};


/**
 * @param {!proto.tensorflow.OpDef.AttrDef=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.OpDef.AttrDef}
 */
proto.tensorflow.OpDef.prototype.addAttr = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.tensorflow.OpDef.AttrDef, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.clearAttrList = function() {
  return this.setAttrList([]);
};


/**
 * optional OpDeprecation deprecation = 8;
 * @return {?proto.tensorflow.OpDef.OpDeprecation}
 */
proto.tensorflow.OpDef.prototype.getDeprecation = function() {
  return /** @type{?proto.tensorflow.OpDef.OpDeprecation} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.OpDef.OpDeprecation, 8));
};


/**
 * @param {?proto.tensorflow.OpDef.OpDeprecation|undefined} value
 * @return {!proto.tensorflow.OpDef} returns this
*/
proto.tensorflow.OpDef.prototype.setDeprecation = function(value) {
  return jspb.Message.setWrapperField(this, 8, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.clearDeprecation = function() {
  return this.setDeprecation(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.OpDef.prototype.hasDeprecation = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional string summary = 5;
 * @return {string}
 */
proto.tensorflow.OpDef.prototype.getSummary = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.setSummary = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string description = 6;
 * @return {string}
 */
proto.tensorflow.OpDef.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.setDescription = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional bool is_commutative = 18;
 * @return {boolean}
 */
proto.tensorflow.OpDef.prototype.getIsCommutative = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 18, false));
};


/**
 * @param {boolean} value
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.setIsCommutative = function(value) {
  return jspb.Message.setProto3BooleanField(this, 18, value);
};


/**
 * optional bool is_aggregate = 16;
 * @return {boolean}
 */
proto.tensorflow.OpDef.prototype.getIsAggregate = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 16, false));
};


/**
 * @param {boolean} value
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.setIsAggregate = function(value) {
  return jspb.Message.setProto3BooleanField(this, 16, value);
};


/**
 * optional bool is_stateful = 17;
 * @return {boolean}
 */
proto.tensorflow.OpDef.prototype.getIsStateful = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 17, false));
};


/**
 * @param {boolean} value
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.setIsStateful = function(value) {
  return jspb.Message.setProto3BooleanField(this, 17, value);
};


/**
 * optional bool allows_uninitialized_input = 19;
 * @return {boolean}
 */
proto.tensorflow.OpDef.prototype.getAllowsUninitializedInput = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 19, false));
};


/**
 * @param {boolean} value
 * @return {!proto.tensorflow.OpDef} returns this
 */
proto.tensorflow.OpDef.prototype.setAllowsUninitializedInput = function(value) {
  return jspb.Message.setProto3BooleanField(this, 19, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.OpList.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.OpList.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.OpList.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.OpList} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.OpList.toObject = function(includeInstance, msg) {
  var f, obj = {
    opList: jspb.Message.toObjectList(msg.getOpList(),
    proto.tensorflow.OpDef.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.OpList}
 */
proto.tensorflow.OpList.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.OpList;
  return proto.tensorflow.OpList.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.OpList} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.OpList}
 */
proto.tensorflow.OpList.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.OpDef;
      reader.readMessage(value,proto.tensorflow.OpDef.deserializeBinaryFromReader);
      msg.addOp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.OpList.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.OpList.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.OpList} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.OpList.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOpList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.tensorflow.OpDef.serializeBinaryToWriter
    );
  }
};


/**
 * repeated OpDef op = 1;
 * @return {!Array<!proto.tensorflow.OpDef>}
 */
proto.tensorflow.OpList.prototype.getOpList = function() {
  return /** @type{!Array<!proto.tensorflow.OpDef>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.OpDef, 1));
};


/**
 * @param {!Array<!proto.tensorflow.OpDef>} value
 * @return {!proto.tensorflow.OpList} returns this
*/
proto.tensorflow.OpList.prototype.setOpList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.tensorflow.OpDef=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.OpDef}
 */
proto.tensorflow.OpList.prototype.addOp = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.tensorflow.OpDef, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.OpList} returns this
 */
proto.tensorflow.OpList.prototype.clearOpList = function() {
  return this.setOpList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.MetaGraphDef.repeatedFields_ = [6];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.MetaGraphDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.MetaGraphDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.MetaGraphDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.MetaGraphDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    metaInfoDef: (f = msg.getMetaInfoDef()) && proto.tensorflow.MetaGraphDef.MetaInfoDef.toObject(includeInstance, f),
    graphDef: (f = msg.getGraphDef()) && proto.tensorflow.GraphDef.toObject(includeInstance, f),
    saverDef: (f = msg.getSaverDef()) && proto.tensorflow.SaverDef.toObject(includeInstance, f),
    collectionDefMap: (f = msg.getCollectionDefMap()) ? f.toObject(includeInstance, proto.tensorflow.CollectionDef.toObject) : [],
    signatureDefMap: (f = msg.getSignatureDefMap()) ? f.toObject(includeInstance, proto.tensorflow.SignatureDef.toObject) : [],
    assetFileDefList: jspb.Message.toObjectList(msg.getAssetFileDefList(),
    proto.tensorflow.AssetFileDef.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.MetaGraphDef}
 */
proto.tensorflow.MetaGraphDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.MetaGraphDef;
  return proto.tensorflow.MetaGraphDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.MetaGraphDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.MetaGraphDef}
 */
proto.tensorflow.MetaGraphDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.MetaGraphDef.MetaInfoDef;
      reader.readMessage(value,proto.tensorflow.MetaGraphDef.MetaInfoDef.deserializeBinaryFromReader);
      msg.setMetaInfoDef(value);
      break;
    case 2:
      var value = new proto.tensorflow.GraphDef;
      reader.readMessage(value,proto.tensorflow.GraphDef.deserializeBinaryFromReader);
      msg.setGraphDef(value);
      break;
    case 3:
      var value = new proto.tensorflow.SaverDef;
      reader.readMessage(value,proto.tensorflow.SaverDef.deserializeBinaryFromReader);
      msg.setSaverDef(value);
      break;
    case 4:
      var value = msg.getCollectionDefMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readMessage, proto.tensorflow.CollectionDef.deserializeBinaryFromReader, "", new proto.tensorflow.CollectionDef());
         });
      break;
    case 5:
      var value = msg.getSignatureDefMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readMessage, proto.tensorflow.SignatureDef.deserializeBinaryFromReader, "", new proto.tensorflow.SignatureDef());
         });
      break;
    case 6:
      var value = new proto.tensorflow.AssetFileDef;
      reader.readMessage(value,proto.tensorflow.AssetFileDef.deserializeBinaryFromReader);
      msg.addAssetFileDef(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.MetaGraphDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.MetaGraphDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.MetaGraphDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.MetaGraphDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMetaInfoDef();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.tensorflow.MetaGraphDef.MetaInfoDef.serializeBinaryToWriter
    );
  }
  f = message.getGraphDef();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.tensorflow.GraphDef.serializeBinaryToWriter
    );
  }
  f = message.getSaverDef();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.tensorflow.SaverDef.serializeBinaryToWriter
    );
  }
  f = message.getCollectionDefMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(4, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeMessage, proto.tensorflow.CollectionDef.serializeBinaryToWriter);
  }
  f = message.getSignatureDefMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(5, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeMessage, proto.tensorflow.SignatureDef.serializeBinaryToWriter);
  }
  f = message.getAssetFileDefList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      6,
      f,
      proto.tensorflow.AssetFileDef.serializeBinaryToWriter
    );
  }
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.repeatedFields_ = [4];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.MetaGraphDef.MetaInfoDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.MetaGraphDef.MetaInfoDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    metaGraphVersion: jspb.Message.getFieldWithDefault(msg, 1, ""),
    strippedOpList: (f = msg.getStrippedOpList()) && proto.tensorflow.OpList.toObject(includeInstance, f),
    anyInfo: (f = msg.getAnyInfo()) && proto.tensorflow.Any.toObject(includeInstance, f),
    tagsList: (f = jspb.Message.getRepeatedField(msg, 4)) == null ? undefined : f,
    tensorflowVersion: jspb.Message.getFieldWithDefault(msg, 5, ""),
    tensorflowGitVersion: jspb.Message.getFieldWithDefault(msg, 6, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.MetaGraphDef.MetaInfoDef;
  return proto.tensorflow.MetaGraphDef.MetaInfoDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.MetaGraphDef.MetaInfoDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setMetaGraphVersion(value);
      break;
    case 2:
      var value = new proto.tensorflow.OpList;
      reader.readMessage(value,proto.tensorflow.OpList.deserializeBinaryFromReader);
      msg.setStrippedOpList(value);
      break;
    case 3:
      var value = new proto.tensorflow.Any;
      reader.readMessage(value,proto.tensorflow.Any.deserializeBinaryFromReader);
      msg.setAnyInfo(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.addTags(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setTensorflowVersion(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setTensorflowGitVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.MetaGraphDef.MetaInfoDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.MetaGraphDef.MetaInfoDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMetaGraphVersion();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getStrippedOpList();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.tensorflow.OpList.serializeBinaryToWriter
    );
  }
  f = message.getAnyInfo();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      proto.tensorflow.Any.serializeBinaryToWriter
    );
  }
  f = message.getTagsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      4,
      f
    );
  }
  f = message.getTensorflowVersion();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getTensorflowGitVersion();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
};


/**
 * optional string meta_graph_version = 1;
 * @return {string}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.getMetaGraphVersion = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef} returns this
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.setMetaGraphVersion = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional OpList stripped_op_list = 2;
 * @return {?proto.tensorflow.OpList}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.getStrippedOpList = function() {
  return /** @type{?proto.tensorflow.OpList} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.OpList, 2));
};


/**
 * @param {?proto.tensorflow.OpList|undefined} value
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef} returns this
*/
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.setStrippedOpList = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef} returns this
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.clearStrippedOpList = function() {
  return this.setStrippedOpList(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.hasStrippedOpList = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional Any any_info = 3;
 * @return {?proto.tensorflow.Any}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.getAnyInfo = function() {
  return /** @type{?proto.tensorflow.Any} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.Any, 3));
};


/**
 * @param {?proto.tensorflow.Any|undefined} value
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef} returns this
*/
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.setAnyInfo = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef} returns this
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.clearAnyInfo = function() {
  return this.setAnyInfo(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.hasAnyInfo = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * repeated string tags = 4;
 * @return {!Array<string>}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.getTagsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 4));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef} returns this
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.setTagsList = function(value) {
  return jspb.Message.setField(this, 4, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef} returns this
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.addTags = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 4, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef} returns this
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.clearTagsList = function() {
  return this.setTagsList([]);
};


/**
 * optional string tensorflow_version = 5;
 * @return {string}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.getTensorflowVersion = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef} returns this
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.setTensorflowVersion = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string tensorflow_git_version = 6;
 * @return {string}
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.getTensorflowGitVersion = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.MetaGraphDef.MetaInfoDef} returns this
 */
proto.tensorflow.MetaGraphDef.MetaInfoDef.prototype.setTensorflowGitVersion = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional MetaInfoDef meta_info_def = 1;
 * @return {?proto.tensorflow.MetaGraphDef.MetaInfoDef}
 */
proto.tensorflow.MetaGraphDef.prototype.getMetaInfoDef = function() {
  return /** @type{?proto.tensorflow.MetaGraphDef.MetaInfoDef} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.MetaGraphDef.MetaInfoDef, 1));
};


/**
 * @param {?proto.tensorflow.MetaGraphDef.MetaInfoDef|undefined} value
 * @return {!proto.tensorflow.MetaGraphDef} returns this
*/
proto.tensorflow.MetaGraphDef.prototype.setMetaInfoDef = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.MetaGraphDef} returns this
 */
proto.tensorflow.MetaGraphDef.prototype.clearMetaInfoDef = function() {
  return this.setMetaInfoDef(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.MetaGraphDef.prototype.hasMetaInfoDef = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional GraphDef graph_def = 2;
 * @return {?proto.tensorflow.GraphDef}
 */
proto.tensorflow.MetaGraphDef.prototype.getGraphDef = function() {
  return /** @type{?proto.tensorflow.GraphDef} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.GraphDef, 2));
};


/**
 * @param {?proto.tensorflow.GraphDef|undefined} value
 * @return {!proto.tensorflow.MetaGraphDef} returns this
*/
proto.tensorflow.MetaGraphDef.prototype.setGraphDef = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.MetaGraphDef} returns this
 */
proto.tensorflow.MetaGraphDef.prototype.clearGraphDef = function() {
  return this.setGraphDef(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.MetaGraphDef.prototype.hasGraphDef = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional SaverDef saver_def = 3;
 * @return {?proto.tensorflow.SaverDef}
 */
proto.tensorflow.MetaGraphDef.prototype.getSaverDef = function() {
  return /** @type{?proto.tensorflow.SaverDef} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.SaverDef, 3));
};


/**
 * @param {?proto.tensorflow.SaverDef|undefined} value
 * @return {!proto.tensorflow.MetaGraphDef} returns this
*/
proto.tensorflow.MetaGraphDef.prototype.setSaverDef = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.MetaGraphDef} returns this
 */
proto.tensorflow.MetaGraphDef.prototype.clearSaverDef = function() {
  return this.setSaverDef(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.MetaGraphDef.prototype.hasSaverDef = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * map<string, CollectionDef> collection_def = 4;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,!proto.tensorflow.CollectionDef>}
 */
proto.tensorflow.MetaGraphDef.prototype.getCollectionDefMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<string,!proto.tensorflow.CollectionDef>} */ (
      jspb.Message.getMapField(this, 4, opt_noLazyCreate,
      proto.tensorflow.CollectionDef));
};


/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.tensorflow.MetaGraphDef} returns this
 */
proto.tensorflow.MetaGraphDef.prototype.clearCollectionDefMap = function() {
  this.getCollectionDefMap().clear();
  return this;};


/**
 * map<string, SignatureDef> signature_def = 5;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,!proto.tensorflow.SignatureDef>}
 */
proto.tensorflow.MetaGraphDef.prototype.getSignatureDefMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<string,!proto.tensorflow.SignatureDef>} */ (
      jspb.Message.getMapField(this, 5, opt_noLazyCreate,
      proto.tensorflow.SignatureDef));
};


/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.tensorflow.MetaGraphDef} returns this
 */
proto.tensorflow.MetaGraphDef.prototype.clearSignatureDefMap = function() {
  this.getSignatureDefMap().clear();
  return this;};


/**
 * repeated AssetFileDef asset_file_def = 6;
 * @return {!Array<!proto.tensorflow.AssetFileDef>}
 */
proto.tensorflow.MetaGraphDef.prototype.getAssetFileDefList = function() {
  return /** @type{!Array<!proto.tensorflow.AssetFileDef>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.AssetFileDef, 6));
};


/**
 * @param {!Array<!proto.tensorflow.AssetFileDef>} value
 * @return {!proto.tensorflow.MetaGraphDef} returns this
*/
proto.tensorflow.MetaGraphDef.prototype.setAssetFileDefList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 6, value);
};


/**
 * @param {!proto.tensorflow.AssetFileDef=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.AssetFileDef}
 */
proto.tensorflow.MetaGraphDef.prototype.addAssetFileDef = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 6, opt_value, proto.tensorflow.AssetFileDef, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.MetaGraphDef} returns this
 */
proto.tensorflow.MetaGraphDef.prototype.clearAssetFileDefList = function() {
  return this.setAssetFileDefList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.SavedModel.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.SavedModel.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.SavedModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.SavedModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SavedModel.toObject = function(includeInstance, msg) {
  var f, obj = {
    savedModelSchemaVersion: jspb.Message.getFieldWithDefault(msg, 1, 0),
    metaGraphsList: jspb.Message.toObjectList(msg.getMetaGraphsList(),
    proto.tensorflow.MetaGraphDef.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.SavedModel}
 */
proto.tensorflow.SavedModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.SavedModel;
  return proto.tensorflow.SavedModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.SavedModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.SavedModel}
 */
proto.tensorflow.SavedModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setSavedModelSchemaVersion(value);
      break;
    case 2:
      var value = new proto.tensorflow.MetaGraphDef;
      reader.readMessage(value,proto.tensorflow.MetaGraphDef.deserializeBinaryFromReader);
      msg.addMetaGraphs(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.SavedModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.SavedModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.SavedModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SavedModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSavedModelSchemaVersion();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getMetaGraphsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.tensorflow.MetaGraphDef.serializeBinaryToWriter
    );
  }
};


/**
 * optional int64 saved_model_schema_version = 1;
 * @return {number}
 */
proto.tensorflow.SavedModel.prototype.getSavedModelSchemaVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.SavedModel} returns this
 */
proto.tensorflow.SavedModel.prototype.setSavedModelSchemaVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * repeated MetaGraphDef meta_graphs = 2;
 * @return {!Array<!proto.tensorflow.MetaGraphDef>}
 */
proto.tensorflow.SavedModel.prototype.getMetaGraphsList = function() {
  return /** @type{!Array<!proto.tensorflow.MetaGraphDef>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.MetaGraphDef, 2));
};


/**
 * @param {!Array<!proto.tensorflow.MetaGraphDef>} value
 * @return {!proto.tensorflow.SavedModel} returns this
*/
proto.tensorflow.SavedModel.prototype.setMetaGraphsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.tensorflow.MetaGraphDef=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.MetaGraphDef}
 */
proto.tensorflow.SavedModel.prototype.addMetaGraphs = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.tensorflow.MetaGraphDef, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.SavedModel} returns this
 */
proto.tensorflow.SavedModel.prototype.clearMetaGraphsList = function() {
  return this.setMetaGraphsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.FunctionDefLibrary.repeatedFields_ = [1,2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.FunctionDefLibrary.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.FunctionDefLibrary.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.FunctionDefLibrary} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.FunctionDefLibrary.toObject = function(includeInstance, msg) {
  var f, obj = {
    functionList: jspb.Message.toObjectList(msg.getFunctionList(),
    proto.tensorflow.FunctionDef.toObject, includeInstance),
    gradientList: jspb.Message.toObjectList(msg.getGradientList(),
    proto.tensorflow.GradientDef.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.FunctionDefLibrary}
 */
proto.tensorflow.FunctionDefLibrary.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.FunctionDefLibrary;
  return proto.tensorflow.FunctionDefLibrary.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.FunctionDefLibrary} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.FunctionDefLibrary}
 */
proto.tensorflow.FunctionDefLibrary.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.FunctionDef;
      reader.readMessage(value,proto.tensorflow.FunctionDef.deserializeBinaryFromReader);
      msg.addFunction(value);
      break;
    case 2:
      var value = new proto.tensorflow.GradientDef;
      reader.readMessage(value,proto.tensorflow.GradientDef.deserializeBinaryFromReader);
      msg.addGradient(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.FunctionDefLibrary.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.FunctionDefLibrary.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.FunctionDefLibrary} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.FunctionDefLibrary.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFunctionList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.tensorflow.FunctionDef.serializeBinaryToWriter
    );
  }
  f = message.getGradientList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.tensorflow.GradientDef.serializeBinaryToWriter
    );
  }
};


/**
 * repeated FunctionDef function = 1;
 * @return {!Array<!proto.tensorflow.FunctionDef>}
 */
proto.tensorflow.FunctionDefLibrary.prototype.getFunctionList = function() {
  return /** @type{!Array<!proto.tensorflow.FunctionDef>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.FunctionDef, 1));
};


/**
 * @param {!Array<!proto.tensorflow.FunctionDef>} value
 * @return {!proto.tensorflow.FunctionDefLibrary} returns this
*/
proto.tensorflow.FunctionDefLibrary.prototype.setFunctionList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.tensorflow.FunctionDef=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.FunctionDef}
 */
proto.tensorflow.FunctionDefLibrary.prototype.addFunction = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.tensorflow.FunctionDef, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.FunctionDefLibrary} returns this
 */
proto.tensorflow.FunctionDefLibrary.prototype.clearFunctionList = function() {
  return this.setFunctionList([]);
};


/**
 * repeated GradientDef gradient = 2;
 * @return {!Array<!proto.tensorflow.GradientDef>}
 */
proto.tensorflow.FunctionDefLibrary.prototype.getGradientList = function() {
  return /** @type{!Array<!proto.tensorflow.GradientDef>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.GradientDef, 2));
};


/**
 * @param {!Array<!proto.tensorflow.GradientDef>} value
 * @return {!proto.tensorflow.FunctionDefLibrary} returns this
*/
proto.tensorflow.FunctionDefLibrary.prototype.setGradientList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.tensorflow.GradientDef=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.GradientDef}
 */
proto.tensorflow.FunctionDefLibrary.prototype.addGradient = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.tensorflow.GradientDef, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.FunctionDefLibrary} returns this
 */
proto.tensorflow.FunctionDefLibrary.prototype.clearGradientList = function() {
  return this.setGradientList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.FunctionDef.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.FunctionDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.FunctionDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.FunctionDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.FunctionDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    signature: (f = msg.getSignature()) && proto.tensorflow.OpDef.toObject(includeInstance, f),
    attrMap: (f = msg.getAttrMap()) ? f.toObject(includeInstance, proto.tensorflow.AttrValue.toObject) : [],
    nodeDefList: jspb.Message.toObjectList(msg.getNodeDefList(),
    proto.tensorflow.NodeDef.toObject, includeInstance),
    retMap: (f = msg.getRetMap()) ? f.toObject(includeInstance, undefined) : []
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.FunctionDef}
 */
proto.tensorflow.FunctionDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.FunctionDef;
  return proto.tensorflow.FunctionDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.FunctionDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.FunctionDef}
 */
proto.tensorflow.FunctionDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.OpDef;
      reader.readMessage(value,proto.tensorflow.OpDef.deserializeBinaryFromReader);
      msg.setSignature(value);
      break;
    case 5:
      var value = msg.getAttrMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readMessage, proto.tensorflow.AttrValue.deserializeBinaryFromReader, "", new proto.tensorflow.AttrValue());
         });
      break;
    case 3:
      var value = new proto.tensorflow.NodeDef;
      reader.readMessage(value,proto.tensorflow.NodeDef.deserializeBinaryFromReader);
      msg.addNodeDef(value);
      break;
    case 4:
      var value = msg.getRetMap();
      reader.readMessage(value, function(message, reader) {
        jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readString, null, "", "");
         });
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.FunctionDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.FunctionDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.FunctionDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.FunctionDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSignature();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.tensorflow.OpDef.serializeBinaryToWriter
    );
  }
  f = message.getAttrMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(5, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeMessage, proto.tensorflow.AttrValue.serializeBinaryToWriter);
  }
  f = message.getNodeDefList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      3,
      f,
      proto.tensorflow.NodeDef.serializeBinaryToWriter
    );
  }
  f = message.getRetMap(true);
  if (f && f.getLength() > 0) {
    f.serializeBinary(4, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeString);
  }
};


/**
 * optional OpDef signature = 1;
 * @return {?proto.tensorflow.OpDef}
 */
proto.tensorflow.FunctionDef.prototype.getSignature = function() {
  return /** @type{?proto.tensorflow.OpDef} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.OpDef, 1));
};


/**
 * @param {?proto.tensorflow.OpDef|undefined} value
 * @return {!proto.tensorflow.FunctionDef} returns this
*/
proto.tensorflow.FunctionDef.prototype.setSignature = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.FunctionDef} returns this
 */
proto.tensorflow.FunctionDef.prototype.clearSignature = function() {
  return this.setSignature(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.FunctionDef.prototype.hasSignature = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * map<string, AttrValue> attr = 5;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,!proto.tensorflow.AttrValue>}
 */
proto.tensorflow.FunctionDef.prototype.getAttrMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<string,!proto.tensorflow.AttrValue>} */ (
      jspb.Message.getMapField(this, 5, opt_noLazyCreate,
      proto.tensorflow.AttrValue));
};


/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.tensorflow.FunctionDef} returns this
 */
proto.tensorflow.FunctionDef.prototype.clearAttrMap = function() {
  this.getAttrMap().clear();
  return this;};


/**
 * repeated NodeDef node_def = 3;
 * @return {!Array<!proto.tensorflow.NodeDef>}
 */
proto.tensorflow.FunctionDef.prototype.getNodeDefList = function() {
  return /** @type{!Array<!proto.tensorflow.NodeDef>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.NodeDef, 3));
};


/**
 * @param {!Array<!proto.tensorflow.NodeDef>} value
 * @return {!proto.tensorflow.FunctionDef} returns this
*/
proto.tensorflow.FunctionDef.prototype.setNodeDefList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 3, value);
};


/**
 * @param {!proto.tensorflow.NodeDef=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.NodeDef}
 */
proto.tensorflow.FunctionDef.prototype.addNodeDef = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.tensorflow.NodeDef, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.FunctionDef} returns this
 */
proto.tensorflow.FunctionDef.prototype.clearNodeDefList = function() {
  return this.setNodeDefList([]);
};


/**
 * map<string, string> ret = 4;
 * @param {boolean=} opt_noLazyCreate Do not create the map if
 * empty, instead returning `undefined`
 * @return {!jspb.Map<string,string>}
 */
proto.tensorflow.FunctionDef.prototype.getRetMap = function(opt_noLazyCreate) {
  return /** @type {!jspb.Map<string,string>} */ (
      jspb.Message.getMapField(this, 4, opt_noLazyCreate,
      null));
};


/**
 * Clears values from the map. The map will be non-null.
 * @return {!proto.tensorflow.FunctionDef} returns this
 */
proto.tensorflow.FunctionDef.prototype.clearRetMap = function() {
  this.getRetMap().clear();
  return this;};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.GradientDef.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.GradientDef.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.GradientDef} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.GradientDef.toObject = function(includeInstance, msg) {
  var f, obj = {
    functionName: jspb.Message.getFieldWithDefault(msg, 1, ""),
    gradientFunc: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.GradientDef}
 */
proto.tensorflow.GradientDef.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.GradientDef;
  return proto.tensorflow.GradientDef.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.GradientDef} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.GradientDef}
 */
proto.tensorflow.GradientDef.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setFunctionName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setGradientFunc(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.GradientDef.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.GradientDef.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.GradientDef} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.GradientDef.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFunctionName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getGradientFunc();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string function_name = 1;
 * @return {string}
 */
proto.tensorflow.GradientDef.prototype.getFunctionName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.GradientDef} returns this
 */
proto.tensorflow.GradientDef.prototype.setFunctionName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string gradient_func = 2;
 * @return {string}
 */
proto.tensorflow.GradientDef.prototype.getGradientFunc = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.GradientDef} returns this
 */
proto.tensorflow.GradientDef.prototype.setGradientFunc = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.SummaryDescription.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.SummaryDescription.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.SummaryDescription} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SummaryDescription.toObject = function(includeInstance, msg) {
  var f, obj = {
    typeHint: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.SummaryDescription}
 */
proto.tensorflow.SummaryDescription.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.SummaryDescription;
  return proto.tensorflow.SummaryDescription.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.SummaryDescription} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.SummaryDescription}
 */
proto.tensorflow.SummaryDescription.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTypeHint(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.SummaryDescription.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.SummaryDescription.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.SummaryDescription} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SummaryDescription.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTypeHint();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string type_hint = 1;
 * @return {string}
 */
proto.tensorflow.SummaryDescription.prototype.getTypeHint = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.SummaryDescription} returns this
 */
proto.tensorflow.SummaryDescription.prototype.setTypeHint = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.HistogramProto.repeatedFields_ = [6,7];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.HistogramProto.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.HistogramProto.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.HistogramProto} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.HistogramProto.toObject = function(includeInstance, msg) {
  var f, obj = {
    min: jspb.Message.getFloatingPointFieldWithDefault(msg, 1, 0.0),
    max: jspb.Message.getFloatingPointFieldWithDefault(msg, 2, 0.0),
    num: jspb.Message.getFloatingPointFieldWithDefault(msg, 3, 0.0),
    sum: jspb.Message.getFloatingPointFieldWithDefault(msg, 4, 0.0),
    sumSquares: jspb.Message.getFloatingPointFieldWithDefault(msg, 5, 0.0),
    bucketLimitList: (f = jspb.Message.getRepeatedFloatingPointField(msg, 6)) == null ? undefined : f,
    bucketList: (f = jspb.Message.getRepeatedFloatingPointField(msg, 7)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.HistogramProto}
 */
proto.tensorflow.HistogramProto.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.HistogramProto;
  return proto.tensorflow.HistogramProto.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.HistogramProto} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.HistogramProto}
 */
proto.tensorflow.HistogramProto.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setMin(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setMax(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setNum(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setSum(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readDouble());
      msg.setSumSquares(value);
      break;
    case 6:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedDouble() : [reader.readDouble()]);
      for (var i = 0; i < values.length; i++) {
        msg.addBucketLimit(values[i]);
      }
      break;
    case 7:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedDouble() : [reader.readDouble()]);
      for (var i = 0; i < values.length; i++) {
        msg.addBucket(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.HistogramProto.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.HistogramProto.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.HistogramProto} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.HistogramProto.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMin();
  if (f !== 0.0) {
    writer.writeDouble(
      1,
      f
    );
  }
  f = message.getMax();
  if (f !== 0.0) {
    writer.writeDouble(
      2,
      f
    );
  }
  f = message.getNum();
  if (f !== 0.0) {
    writer.writeDouble(
      3,
      f
    );
  }
  f = message.getSum();
  if (f !== 0.0) {
    writer.writeDouble(
      4,
      f
    );
  }
  f = message.getSumSquares();
  if (f !== 0.0) {
    writer.writeDouble(
      5,
      f
    );
  }
  f = message.getBucketLimitList();
  if (f.length > 0) {
    writer.writePackedDouble(
      6,
      f
    );
  }
  f = message.getBucketList();
  if (f.length > 0) {
    writer.writePackedDouble(
      7,
      f
    );
  }
};


/**
 * optional double min = 1;
 * @return {number}
 */
proto.tensorflow.HistogramProto.prototype.getMin = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 1, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.setMin = function(value) {
  return jspb.Message.setProto3FloatField(this, 1, value);
};


/**
 * optional double max = 2;
 * @return {number}
 */
proto.tensorflow.HistogramProto.prototype.getMax = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 2, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.setMax = function(value) {
  return jspb.Message.setProto3FloatField(this, 2, value);
};


/**
 * optional double num = 3;
 * @return {number}
 */
proto.tensorflow.HistogramProto.prototype.getNum = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 3, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.setNum = function(value) {
  return jspb.Message.setProto3FloatField(this, 3, value);
};


/**
 * optional double sum = 4;
 * @return {number}
 */
proto.tensorflow.HistogramProto.prototype.getSum = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 4, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.setSum = function(value) {
  return jspb.Message.setProto3FloatField(this, 4, value);
};


/**
 * optional double sum_squares = 5;
 * @return {number}
 */
proto.tensorflow.HistogramProto.prototype.getSumSquares = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 5, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.setSumSquares = function(value) {
  return jspb.Message.setProto3FloatField(this, 5, value);
};


/**
 * repeated double bucket_limit = 6;
 * @return {!Array<number>}
 */
proto.tensorflow.HistogramProto.prototype.getBucketLimitList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedFloatingPointField(this, 6));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.setBucketLimitList = function(value) {
  return jspb.Message.setField(this, 6, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.addBucketLimit = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 6, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.clearBucketLimitList = function() {
  return this.setBucketLimitList([]);
};


/**
 * repeated double bucket = 7;
 * @return {!Array<number>}
 */
proto.tensorflow.HistogramProto.prototype.getBucketList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedFloatingPointField(this, 7));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.setBucketList = function(value) {
  return jspb.Message.setField(this, 7, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.addBucket = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 7, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.HistogramProto} returns this
 */
proto.tensorflow.HistogramProto.prototype.clearBucketList = function() {
  return this.setBucketList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.SummaryMetadata.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.SummaryMetadata.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.SummaryMetadata} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SummaryMetadata.toObject = function(includeInstance, msg) {
  var f, obj = {
    pluginData: (f = msg.getPluginData()) && proto.tensorflow.SummaryMetadata.PluginData.toObject(includeInstance, f),
    displayName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    summaryDescription: jspb.Message.getFieldWithDefault(msg, 3, ""),
    dataClass: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.SummaryMetadata}
 */
proto.tensorflow.SummaryMetadata.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.SummaryMetadata;
  return proto.tensorflow.SummaryMetadata.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.SummaryMetadata} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.SummaryMetadata}
 */
proto.tensorflow.SummaryMetadata.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.SummaryMetadata.PluginData;
      reader.readMessage(value,proto.tensorflow.SummaryMetadata.PluginData.deserializeBinaryFromReader);
      msg.setPluginData(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDisplayName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSummaryDescription(value);
      break;
    case 4:
      var value = /** @type {!proto.tensorflow.DataClass} */ (reader.readEnum());
      msg.setDataClass(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.SummaryMetadata.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.SummaryMetadata.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.SummaryMetadata} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SummaryMetadata.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPluginData();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.tensorflow.SummaryMetadata.PluginData.serializeBinaryToWriter
    );
  }
  f = message.getDisplayName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSummaryDescription();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getDataClass();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.SummaryMetadata.PluginData.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.SummaryMetadata.PluginData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.SummaryMetadata.PluginData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SummaryMetadata.PluginData.toObject = function(includeInstance, msg) {
  var f, obj = {
    pluginName: jspb.Message.getFieldWithDefault(msg, 1, ""),
    content: msg.getContent_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.SummaryMetadata.PluginData}
 */
proto.tensorflow.SummaryMetadata.PluginData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.SummaryMetadata.PluginData;
  return proto.tensorflow.SummaryMetadata.PluginData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.SummaryMetadata.PluginData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.SummaryMetadata.PluginData}
 */
proto.tensorflow.SummaryMetadata.PluginData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPluginName(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.SummaryMetadata.PluginData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.SummaryMetadata.PluginData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.SummaryMetadata.PluginData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.SummaryMetadata.PluginData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPluginName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getContent_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
};


/**
 * optional string plugin_name = 1;
 * @return {string}
 */
proto.tensorflow.SummaryMetadata.PluginData.prototype.getPluginName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.SummaryMetadata.PluginData} returns this
 */
proto.tensorflow.SummaryMetadata.PluginData.prototype.setPluginName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional bytes content = 2;
 * @return {!(string|Uint8Array)}
 */
proto.tensorflow.SummaryMetadata.PluginData.prototype.getContent = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes content = 2;
 * This is a type-conversion wrapper around `getContent()`
 * @return {string}
 */
proto.tensorflow.SummaryMetadata.PluginData.prototype.getContent_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getContent()));
};


/**
 * optional bytes content = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getContent()`
 * @return {!Uint8Array}
 */
proto.tensorflow.SummaryMetadata.PluginData.prototype.getContent_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getContent()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.tensorflow.SummaryMetadata.PluginData} returns this
 */
proto.tensorflow.SummaryMetadata.PluginData.prototype.setContent = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};


/**
 * optional PluginData plugin_data = 1;
 * @return {?proto.tensorflow.SummaryMetadata.PluginData}
 */
proto.tensorflow.SummaryMetadata.prototype.getPluginData = function() {
  return /** @type{?proto.tensorflow.SummaryMetadata.PluginData} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.SummaryMetadata.PluginData, 1));
};


/**
 * @param {?proto.tensorflow.SummaryMetadata.PluginData|undefined} value
 * @return {!proto.tensorflow.SummaryMetadata} returns this
*/
proto.tensorflow.SummaryMetadata.prototype.setPluginData = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.SummaryMetadata} returns this
 */
proto.tensorflow.SummaryMetadata.prototype.clearPluginData = function() {
  return this.setPluginData(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.SummaryMetadata.prototype.hasPluginData = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string display_name = 2;
 * @return {string}
 */
proto.tensorflow.SummaryMetadata.prototype.getDisplayName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.SummaryMetadata} returns this
 */
proto.tensorflow.SummaryMetadata.prototype.setDisplayName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string summary_description = 3;
 * @return {string}
 */
proto.tensorflow.SummaryMetadata.prototype.getSummaryDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.SummaryMetadata} returns this
 */
proto.tensorflow.SummaryMetadata.prototype.setSummaryDescription = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional DataClass data_class = 4;
 * @return {!proto.tensorflow.DataClass}
 */
proto.tensorflow.SummaryMetadata.prototype.getDataClass = function() {
  return /** @type {!proto.tensorflow.DataClass} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.tensorflow.DataClass} value
 * @return {!proto.tensorflow.SummaryMetadata} returns this
 */
proto.tensorflow.SummaryMetadata.prototype.setDataClass = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.tensorflow.Summary.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.Summary.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.Summary.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.Summary} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Summary.toObject = function(includeInstance, msg) {
  var f, obj = {
    valueList: jspb.Message.toObjectList(msg.getValueList(),
    proto.tensorflow.Summary.Value.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.Summary}
 */
proto.tensorflow.Summary.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.Summary;
  return proto.tensorflow.Summary.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.Summary} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.Summary}
 */
proto.tensorflow.Summary.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.tensorflow.Summary.Value;
      reader.readMessage(value,proto.tensorflow.Summary.Value.deserializeBinaryFromReader);
      msg.addValue(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.Summary.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.Summary.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.Summary} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Summary.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getValueList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.tensorflow.Summary.Value.serializeBinaryToWriter
    );
  }
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.Summary.Image.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.Summary.Image.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.Summary.Image} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Summary.Image.toObject = function(includeInstance, msg) {
  var f, obj = {
    height: jspb.Message.getFieldWithDefault(msg, 1, 0),
    width: jspb.Message.getFieldWithDefault(msg, 2, 0),
    colorspace: jspb.Message.getFieldWithDefault(msg, 3, 0),
    encodedImageString: msg.getEncodedImageString_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.Summary.Image}
 */
proto.tensorflow.Summary.Image.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.Summary.Image;
  return proto.tensorflow.Summary.Image.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.Summary.Image} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.Summary.Image}
 */
proto.tensorflow.Summary.Image.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setHeight(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setWidth(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setColorspace(value);
      break;
    case 4:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setEncodedImageString(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.Summary.Image.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.Summary.Image.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.Summary.Image} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Summary.Image.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getHeight();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getWidth();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getColorspace();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getEncodedImageString_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      4,
      f
    );
  }
};


/**
 * optional int32 height = 1;
 * @return {number}
 */
proto.tensorflow.Summary.Image.prototype.getHeight = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.Summary.Image} returns this
 */
proto.tensorflow.Summary.Image.prototype.setHeight = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 width = 2;
 * @return {number}
 */
proto.tensorflow.Summary.Image.prototype.getWidth = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.Summary.Image} returns this
 */
proto.tensorflow.Summary.Image.prototype.setWidth = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 colorspace = 3;
 * @return {number}
 */
proto.tensorflow.Summary.Image.prototype.getColorspace = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.Summary.Image} returns this
 */
proto.tensorflow.Summary.Image.prototype.setColorspace = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional bytes encoded_image_string = 4;
 * @return {!(string|Uint8Array)}
 */
proto.tensorflow.Summary.Image.prototype.getEncodedImageString = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * optional bytes encoded_image_string = 4;
 * This is a type-conversion wrapper around `getEncodedImageString()`
 * @return {string}
 */
proto.tensorflow.Summary.Image.prototype.getEncodedImageString_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getEncodedImageString()));
};


/**
 * optional bytes encoded_image_string = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getEncodedImageString()`
 * @return {!Uint8Array}
 */
proto.tensorflow.Summary.Image.prototype.getEncodedImageString_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getEncodedImageString()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.tensorflow.Summary.Image} returns this
 */
proto.tensorflow.Summary.Image.prototype.setEncodedImageString = function(value) {
  return jspb.Message.setProto3BytesField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.Summary.Audio.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.Summary.Audio.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.Summary.Audio} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Summary.Audio.toObject = function(includeInstance, msg) {
  var f, obj = {
    sampleRate: jspb.Message.getFloatingPointFieldWithDefault(msg, 1, 0.0),
    numChannels: jspb.Message.getFieldWithDefault(msg, 2, 0),
    lengthFrames: jspb.Message.getFieldWithDefault(msg, 3, 0),
    encodedAudioString: msg.getEncodedAudioString_asB64(),
    contentType: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.Summary.Audio}
 */
proto.tensorflow.Summary.Audio.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.Summary.Audio;
  return proto.tensorflow.Summary.Audio.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.Summary.Audio} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.Summary.Audio}
 */
proto.tensorflow.Summary.Audio.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setSampleRate(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setNumChannels(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setLengthFrames(value);
      break;
    case 4:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setEncodedAudioString(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setContentType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.Summary.Audio.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.Summary.Audio.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.Summary.Audio} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Summary.Audio.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSampleRate();
  if (f !== 0.0) {
    writer.writeFloat(
      1,
      f
    );
  }
  f = message.getNumChannels();
  if (f !== 0) {
    writer.writeInt64(
      2,
      f
    );
  }
  f = message.getLengthFrames();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = message.getEncodedAudioString_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      4,
      f
    );
  }
  f = message.getContentType();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional float sample_rate = 1;
 * @return {number}
 */
proto.tensorflow.Summary.Audio.prototype.getSampleRate = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 1, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.Summary.Audio} returns this
 */
proto.tensorflow.Summary.Audio.prototype.setSampleRate = function(value) {
  return jspb.Message.setProto3FloatField(this, 1, value);
};


/**
 * optional int64 num_channels = 2;
 * @return {number}
 */
proto.tensorflow.Summary.Audio.prototype.getNumChannels = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.Summary.Audio} returns this
 */
proto.tensorflow.Summary.Audio.prototype.setNumChannels = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int64 length_frames = 3;
 * @return {number}
 */
proto.tensorflow.Summary.Audio.prototype.getLengthFrames = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.Summary.Audio} returns this
 */
proto.tensorflow.Summary.Audio.prototype.setLengthFrames = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional bytes encoded_audio_string = 4;
 * @return {!(string|Uint8Array)}
 */
proto.tensorflow.Summary.Audio.prototype.getEncodedAudioString = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * optional bytes encoded_audio_string = 4;
 * This is a type-conversion wrapper around `getEncodedAudioString()`
 * @return {string}
 */
proto.tensorflow.Summary.Audio.prototype.getEncodedAudioString_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getEncodedAudioString()));
};


/**
 * optional bytes encoded_audio_string = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getEncodedAudioString()`
 * @return {!Uint8Array}
 */
proto.tensorflow.Summary.Audio.prototype.getEncodedAudioString_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getEncodedAudioString()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.tensorflow.Summary.Audio} returns this
 */
proto.tensorflow.Summary.Audio.prototype.setEncodedAudioString = function(value) {
  return jspb.Message.setProto3BytesField(this, 4, value);
};


/**
 * optional string content_type = 5;
 * @return {string}
 */
proto.tensorflow.Summary.Audio.prototype.getContentType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.Summary.Audio} returns this
 */
proto.tensorflow.Summary.Audio.prototype.setContentType = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.tensorflow.Summary.Value.oneofGroups_ = [[2,3,4,5,6,8]];

/**
 * @enum {number}
 */
proto.tensorflow.Summary.Value.ValueCase = {
  VALUE_NOT_SET: 0,
  SIMPLE_VALUE: 2,
  OBSOLETE_OLD_STYLE_HISTOGRAM: 3,
  IMAGE: 4,
  HISTO: 5,
  AUDIO: 6,
  TENSOR: 8
};

/**
 * @return {proto.tensorflow.Summary.Value.ValueCase}
 */
proto.tensorflow.Summary.Value.prototype.getValueCase = function() {
  return /** @type {proto.tensorflow.Summary.Value.ValueCase} */(jspb.Message.computeOneofCase(this, proto.tensorflow.Summary.Value.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.Summary.Value.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.Summary.Value.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.Summary.Value} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Summary.Value.toObject = function(includeInstance, msg) {
  var f, obj = {
    nodeName: jspb.Message.getFieldWithDefault(msg, 7, ""),
    tag: jspb.Message.getFieldWithDefault(msg, 1, ""),
    metadata: (f = msg.getMetadata()) && proto.tensorflow.SummaryMetadata.toObject(includeInstance, f),
    simpleValue: jspb.Message.getFloatingPointFieldWithDefault(msg, 2, 0.0),
    obsoleteOldStyleHistogram: msg.getObsoleteOldStyleHistogram_asB64(),
    image: (f = msg.getImage()) && proto.tensorflow.Summary.Image.toObject(includeInstance, f),
    histo: (f = msg.getHisto()) && proto.tensorflow.HistogramProto.toObject(includeInstance, f),
    audio: (f = msg.getAudio()) && proto.tensorflow.Summary.Audio.toObject(includeInstance, f),
    tensor: (f = msg.getTensor()) && proto.tensorflow.Tensor.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.Summary.Value}
 */
proto.tensorflow.Summary.Value.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.Summary.Value;
  return proto.tensorflow.Summary.Value.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.Summary.Value} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.Summary.Value}
 */
proto.tensorflow.Summary.Value.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setNodeName(value);
      break;
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTag(value);
      break;
    case 9:
      var value = new proto.tensorflow.SummaryMetadata;
      reader.readMessage(value,proto.tensorflow.SummaryMetadata.deserializeBinaryFromReader);
      msg.setMetadata(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setSimpleValue(value);
      break;
    case 3:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setObsoleteOldStyleHistogram(value);
      break;
    case 4:
      var value = new proto.tensorflow.Summary.Image;
      reader.readMessage(value,proto.tensorflow.Summary.Image.deserializeBinaryFromReader);
      msg.setImage(value);
      break;
    case 5:
      var value = new proto.tensorflow.HistogramProto;
      reader.readMessage(value,proto.tensorflow.HistogramProto.deserializeBinaryFromReader);
      msg.setHisto(value);
      break;
    case 6:
      var value = new proto.tensorflow.Summary.Audio;
      reader.readMessage(value,proto.tensorflow.Summary.Audio.deserializeBinaryFromReader);
      msg.setAudio(value);
      break;
    case 8:
      var value = new proto.tensorflow.Tensor;
      reader.readMessage(value,proto.tensorflow.Tensor.deserializeBinaryFromReader);
      msg.setTensor(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.Summary.Value.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.Summary.Value.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.Summary.Value} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.Summary.Value.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getNodeName();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getTag();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getMetadata();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      proto.tensorflow.SummaryMetadata.serializeBinaryToWriter
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeFloat(
      2,
      f
    );
  }
  f = /** @type {!(string|Uint8Array)} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeBytes(
      3,
      f
    );
  }
  f = message.getImage();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.tensorflow.Summary.Image.serializeBinaryToWriter
    );
  }
  f = message.getHisto();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      proto.tensorflow.HistogramProto.serializeBinaryToWriter
    );
  }
  f = message.getAudio();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      proto.tensorflow.Summary.Audio.serializeBinaryToWriter
    );
  }
  f = message.getTensor();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      proto.tensorflow.Tensor.serializeBinaryToWriter
    );
  }
};


/**
 * optional string node_name = 7;
 * @return {string}
 */
proto.tensorflow.Summary.Value.prototype.getNodeName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.setNodeName = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string tag = 1;
 * @return {string}
 */
proto.tensorflow.Summary.Value.prototype.getTag = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.setTag = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional SummaryMetadata metadata = 9;
 * @return {?proto.tensorflow.SummaryMetadata}
 */
proto.tensorflow.Summary.Value.prototype.getMetadata = function() {
  return /** @type{?proto.tensorflow.SummaryMetadata} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.SummaryMetadata, 9));
};


/**
 * @param {?proto.tensorflow.SummaryMetadata|undefined} value
 * @return {!proto.tensorflow.Summary.Value} returns this
*/
proto.tensorflow.Summary.Value.prototype.setMetadata = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.clearMetadata = function() {
  return this.setMetadata(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.Summary.Value.prototype.hasMetadata = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional float simple_value = 2;
 * @return {number}
 */
proto.tensorflow.Summary.Value.prototype.getSimpleValue = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 2, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.setSimpleValue = function(value) {
  return jspb.Message.setOneofField(this, 2, proto.tensorflow.Summary.Value.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.clearSimpleValue = function() {
  return jspb.Message.setOneofField(this, 2, proto.tensorflow.Summary.Value.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.Summary.Value.prototype.hasSimpleValue = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional bytes obsolete_old_style_histogram = 3;
 * @return {!(string|Uint8Array)}
 */
proto.tensorflow.Summary.Value.prototype.getObsoleteOldStyleHistogram = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * optional bytes obsolete_old_style_histogram = 3;
 * This is a type-conversion wrapper around `getObsoleteOldStyleHistogram()`
 * @return {string}
 */
proto.tensorflow.Summary.Value.prototype.getObsoleteOldStyleHistogram_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getObsoleteOldStyleHistogram()));
};


/**
 * optional bytes obsolete_old_style_histogram = 3;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getObsoleteOldStyleHistogram()`
 * @return {!Uint8Array}
 */
proto.tensorflow.Summary.Value.prototype.getObsoleteOldStyleHistogram_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getObsoleteOldStyleHistogram()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.setObsoleteOldStyleHistogram = function(value) {
  return jspb.Message.setOneofField(this, 3, proto.tensorflow.Summary.Value.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.clearObsoleteOldStyleHistogram = function() {
  return jspb.Message.setOneofField(this, 3, proto.tensorflow.Summary.Value.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.Summary.Value.prototype.hasObsoleteOldStyleHistogram = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional Image image = 4;
 * @return {?proto.tensorflow.Summary.Image}
 */
proto.tensorflow.Summary.Value.prototype.getImage = function() {
  return /** @type{?proto.tensorflow.Summary.Image} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.Summary.Image, 4));
};


/**
 * @param {?proto.tensorflow.Summary.Image|undefined} value
 * @return {!proto.tensorflow.Summary.Value} returns this
*/
proto.tensorflow.Summary.Value.prototype.setImage = function(value) {
  return jspb.Message.setOneofWrapperField(this, 4, proto.tensorflow.Summary.Value.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.clearImage = function() {
  return this.setImage(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.Summary.Value.prototype.hasImage = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional HistogramProto histo = 5;
 * @return {?proto.tensorflow.HistogramProto}
 */
proto.tensorflow.Summary.Value.prototype.getHisto = function() {
  return /** @type{?proto.tensorflow.HistogramProto} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.HistogramProto, 5));
};


/**
 * @param {?proto.tensorflow.HistogramProto|undefined} value
 * @return {!proto.tensorflow.Summary.Value} returns this
*/
proto.tensorflow.Summary.Value.prototype.setHisto = function(value) {
  return jspb.Message.setOneofWrapperField(this, 5, proto.tensorflow.Summary.Value.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.clearHisto = function() {
  return this.setHisto(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.Summary.Value.prototype.hasHisto = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional Audio audio = 6;
 * @return {?proto.tensorflow.Summary.Audio}
 */
proto.tensorflow.Summary.Value.prototype.getAudio = function() {
  return /** @type{?proto.tensorflow.Summary.Audio} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.Summary.Audio, 6));
};


/**
 * @param {?proto.tensorflow.Summary.Audio|undefined} value
 * @return {!proto.tensorflow.Summary.Value} returns this
*/
proto.tensorflow.Summary.Value.prototype.setAudio = function(value) {
  return jspb.Message.setOneofWrapperField(this, 6, proto.tensorflow.Summary.Value.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.clearAudio = function() {
  return this.setAudio(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.Summary.Value.prototype.hasAudio = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional Tensor tensor = 8;
 * @return {?proto.tensorflow.Tensor}
 */
proto.tensorflow.Summary.Value.prototype.getTensor = function() {
  return /** @type{?proto.tensorflow.Tensor} */ (
    jspb.Message.getWrapperField(this, proto.tensorflow.Tensor, 8));
};


/**
 * @param {?proto.tensorflow.Tensor|undefined} value
 * @return {!proto.tensorflow.Summary.Value} returns this
*/
proto.tensorflow.Summary.Value.prototype.setTensor = function(value) {
  return jspb.Message.setOneofWrapperField(this, 8, proto.tensorflow.Summary.Value.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.tensorflow.Summary.Value} returns this
 */
proto.tensorflow.Summary.Value.prototype.clearTensor = function() {
  return this.setTensor(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.tensorflow.Summary.Value.prototype.hasTensor = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * repeated Value value = 1;
 * @return {!Array<!proto.tensorflow.Summary.Value>}
 */
proto.tensorflow.Summary.prototype.getValueList = function() {
  return /** @type{!Array<!proto.tensorflow.Summary.Value>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.tensorflow.Summary.Value, 1));
};


/**
 * @param {!Array<!proto.tensorflow.Summary.Value>} value
 * @return {!proto.tensorflow.Summary} returns this
*/
proto.tensorflow.Summary.prototype.setValueList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.tensorflow.Summary.Value=} opt_value
 * @param {number=} opt_index
 * @return {!proto.tensorflow.Summary.Value}
 */
proto.tensorflow.Summary.prototype.addValue = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.tensorflow.Summary.Value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.tensorflow.Summary} returns this
 */
proto.tensorflow.Summary.prototype.clearValueList = function() {
  return this.setValueList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.tensorflow.HistogramPluginData.prototype.toObject = function(opt_includeInstance) {
  return proto.tensorflow.HistogramPluginData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.tensorflow.HistogramPluginData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.HistogramPluginData.toObject = function(includeInstance, msg) {
  var f, obj = {
    version: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.tensorflow.HistogramPluginData}
 */
proto.tensorflow.HistogramPluginData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.tensorflow.HistogramPluginData;
  return proto.tensorflow.HistogramPluginData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.tensorflow.HistogramPluginData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.tensorflow.HistogramPluginData}
 */
proto.tensorflow.HistogramPluginData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setVersion(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.tensorflow.HistogramPluginData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.tensorflow.HistogramPluginData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.tensorflow.HistogramPluginData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.tensorflow.HistogramPluginData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVersion();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
};


/**
 * optional int32 version = 1;
 * @return {number}
 */
proto.tensorflow.HistogramPluginData.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.tensorflow.HistogramPluginData} returns this
 */
proto.tensorflow.HistogramPluginData.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * @enum {number}
 */
proto.tensorflow.DataType = {
  DT_INVALID: 0,
  DT_FLOAT: 1,
  DT_DOUBLE: 2,
  DT_INT32: 3,
  DT_UINT8: 4,
  DT_INT16: 5,
  DT_INT8: 6,
  DT_STRING: 7,
  DT_COMPLEX64: 8,
  DT_INT64: 9,
  DT_BOOL: 10,
  DT_QINT8: 11,
  DT_QUINT8: 12,
  DT_QINT32: 13,
  DT_BFLOAT16: 14,
  DT_FLOAT_REF: 101,
  DT_DOUBLE_REF: 102,
  DT_INT32_REF: 103,
  DT_UINT8_REF: 104,
  DT_INT16_REF: 105,
  DT_INT8_REF: 106,
  DT_STRING_REF: 107,
  DT_COMPLEX64_REF: 108,
  DT_INT64_REF: 109,
  DT_BOOL_REF: 110,
  DT_QINT8_REF: 111,
  DT_QUINT8_REF: 112,
  DT_QINT32_REF: 113,
  DT_BFLOAT16_REF: 114
};

/**
 * @enum {number}
 */
proto.tensorflow.DataClass = {
  DATA_CLASS_UNKNOWN: 0,
  DATA_CLASS_SCALAR: 1,
  DATA_CLASS_TENSOR: 2,
  DATA_CLASS_BLOB_SEQUENCE: 3
};

goog.object.extend(exports, proto.tensorflow);
