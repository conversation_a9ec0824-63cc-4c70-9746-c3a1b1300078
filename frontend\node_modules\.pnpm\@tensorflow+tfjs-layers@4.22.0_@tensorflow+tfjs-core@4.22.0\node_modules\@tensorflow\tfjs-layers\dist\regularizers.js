/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/* original source: keras/regularizers.py */
import * as tfc from '@tensorflow/tfjs-core';
import { abs, add, serialization, sum, tidy, zeros } from '@tensorflow/tfjs-core';
import * as K from './backend/tfjs_backend';
import { deserializeKerasObject, serializeKerasObject } from './utils/generic_utils';
function assertObjectArgs(args) {
    if (args != null && typeof args !== 'object') {
        throw new Error(`Argument to L1L2 regularizer's constructor is expected to be an ` +
            `object, but received: ${args}`);
    }
}
/**
 * Regularizer base class.
 */
export class Regularizer extends serialization.Serializable {
}
class L1L2 extends Regularizer {
    constructor(args) {
        super();
        assertObjectArgs(args);
        this.l1 = args == null || args.l1 == null ? 0.01 : args.l1;
        this.l2 = args == null || args.l2 == null ? 0.01 : args.l2;
        this.hasL1 = this.l1 !== 0;
        this.hasL2 = this.l2 !== 0;
    }
    /**
     * Porting note: Renamed from __call__.
     * @param x Variable of which to calculate the regularization score.
     */
    apply(x) {
        return tidy(() => {
            let regularization = zeros([1]);
            if (this.hasL1) {
                regularization = add(regularization, sum(tfc.mul(this.l1, abs(x))));
            }
            if (this.hasL2) {
                regularization =
                    add(regularization, sum(tfc.mul(this.l2, K.square(x))));
            }
            return tfc.reshape(regularization, []);
        });
    }
    getConfig() {
        return { 'l1': this.l1, 'l2': this.l2 };
    }
    /** @nocollapse */
    static fromConfig(cls, config) {
        return new cls({ l1: config['l1'], l2: config['l2'] });
    }
}
/** @nocollapse */
L1L2.className = 'L1L2';
export { L1L2 };
serialization.registerClass(L1L2);
export function l1(args) {
    assertObjectArgs(args);
    return new L1L2({ l1: args != null ? args.l1 : null, l2: 0 });
}
export function l2(args) {
    assertObjectArgs(args);
    return new L1L2({ l2: args != null ? args.l2 : null, l1: 0 });
}
// Maps the JavaScript-like identifier keys to the corresponding keras symbols.
export const REGULARIZER_IDENTIFIER_REGISTRY_SYMBOL_MAP = {
    'l1l2': 'L1L2'
};
export function serializeRegularizer(constraint) {
    return serializeKerasObject(constraint);
}
export function deserializeRegularizer(config, customObjects = {}) {
    return deserializeKerasObject(config, serialization.SerializationMap.getMap().classNameMap, customObjects, 'regularizer');
}
export function getRegularizer(identifier) {
    if (identifier == null) {
        return null;
    }
    if (typeof identifier === 'string') {
        const className = identifier in REGULARIZER_IDENTIFIER_REGISTRY_SYMBOL_MAP ?
            REGULARIZER_IDENTIFIER_REGISTRY_SYMBOL_MAP[identifier] :
            identifier;
        const config = { className, config: {} };
        return deserializeRegularizer(config);
    }
    else if (identifier instanceof Regularizer) {
        return identifier;
    }
    else {
        return deserializeRegularizer(identifier);
    }
}
//# sourceMappingURL=data:application/json;base64,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