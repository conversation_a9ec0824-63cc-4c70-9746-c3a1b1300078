/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid.
/**
 * A string array of valid PaddingLayer class names.
 *
 * This is guaranteed to match the `PaddingLayerClassName` union type.
 */
export const paddingLayerClassNames = [
    'ZeroPadding2D',
];
//# sourceMappingURL=data:application/json;base64,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