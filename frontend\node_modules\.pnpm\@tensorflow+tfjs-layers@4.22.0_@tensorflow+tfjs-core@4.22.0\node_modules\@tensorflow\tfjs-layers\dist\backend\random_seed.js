/**
 * @license
 * Copyright 2023 CodeSmith LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/**
 * Keeps track of seed and handles pseudorandomness
 * Instance created in BaseRandomLayer class
 * Utilized for random preprocessing layers
 */
class RandomSeed {
    constructor(seed) {
        this.seed = seed;
    }
    next() {
        if (this.seed === undefined) {
            return undefined;
        }
        return this.seed++;
    }
}
RandomSeed.className = 'RandomSeed';
export { RandomSeed };
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmFuZG9tX3NlZWQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi90ZmpzLWxheWVycy9zcmMvYmFja2VuZC9yYW5kb21fc2VlZC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7Ozs7R0FRRztBQUVIOzs7O0dBSUc7QUFFSCxNQUFhLFVBQVU7SUFHckIsWUFBWSxJQUF3QjtRQUNsQyxJQUFJLENBQUMsSUFBSSxHQUFHLElBQUksQ0FBQztJQUNuQixDQUFDO0lBQ0QsSUFBSTtRQUNGLElBQUksSUFBSSxDQUFDLElBQUksS0FBSyxTQUFTLEVBQUU7WUFDM0IsT0FBTyxTQUFTLENBQUM7U0FDbEI7UUFDRCxPQUFPLElBQUksQ0FBQyxJQUFJLEVBQUUsQ0FBQztJQUNyQixDQUFDOztBQVZNLG9CQUFTLEdBQUcsWUFBWSxDQUFDO1NBRHJCLFVBQVUiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAyMyBDb2RlU21pdGggTExDXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlXG4gKiBsaWNlbnNlIHRoYXQgY2FuIGJlIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgb3IgYXRcbiAqIGh0dHBzOi8vb3BlbnNvdXJjZS5vcmcvbGljZW5zZXMvTUlULlxuICogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAqL1xuXG4vKipcbiAqIEtlZXBzIHRyYWNrIG9mIHNlZWQgYW5kIGhhbmRsZXMgcHNldWRvcmFuZG9tbmVzc1xuICogSW5zdGFuY2UgY3JlYXRlZCBpbiBCYXNlUmFuZG9tTGF5ZXIgY2xhc3NcbiAqIFV0aWxpemVkIGZvciByYW5kb20gcHJlcHJvY2Vzc2luZyBsYXllcnNcbiAqL1xuXG5leHBvcnQgY2xhc3MgUmFuZG9tU2VlZCB7XG4gIHN0YXRpYyBjbGFzc05hbWUgPSAnUmFuZG9tU2VlZCc7XG4gIHNlZWQ6IG51bWJlciB8IHVuZGVmaW5lZDtcbiAgY29uc3RydWN0b3Ioc2VlZDogbnVtYmVyIHwgdW5kZWZpbmVkKSB7IFxuICAgIHRoaXMuc2VlZCA9IHNlZWQ7IFxuICB9XG4gIG5leHQoKTogbnVtYmVyIHwgdW5kZWZpbmVkIHsgXG4gICAgaWYgKHRoaXMuc2VlZCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5zZWVkKys7IFxuICB9XG59XG4iXX0=