/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid.
/**
 * A string array of valid WrapperLayer class names.
 *
 * This is guaranteed to match the `WrapperLayerClassName` union type.
 */
export const wrapperLayerClassNames = [
    'Bidirectional',
    'TimeDistributed',
];
//# sourceMappingURL=data:application/json;base64,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