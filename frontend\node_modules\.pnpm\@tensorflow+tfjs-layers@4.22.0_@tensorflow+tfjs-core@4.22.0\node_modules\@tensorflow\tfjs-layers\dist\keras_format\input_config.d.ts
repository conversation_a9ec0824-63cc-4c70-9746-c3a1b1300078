/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-layers/dist/keras_format/input_config" />
import { DataType } from '@tensorflow/tfjs-core';
import { Shape } from './common';
import { BaseLayerSerialization } from './topology_config';
export type InputLayerConfig = {
    name?: string;
    input_shape?: Shape;
    batch_size?: number;
    batch_input_shape?: Shape;
    dtype?: DataType;
    sparse?: boolean;
};
export type InputLayerSerialization = BaseLayerSerialization<'InputLayer', InputLayerConfig>;
export type InputLayerClassName = InputLayerSerialization['class_name'];
/**
 * A string array of valid InputLayer class names.
 *
 * This is guaranteed to match the `InputLayerClassName` union type.
 */
export declare const inputLayerClassNames: InputLayerClassName[];
