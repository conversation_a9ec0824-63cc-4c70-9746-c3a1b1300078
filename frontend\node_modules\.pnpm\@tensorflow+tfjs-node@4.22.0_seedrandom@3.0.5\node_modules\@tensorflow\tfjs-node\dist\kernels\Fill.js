"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.fillConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
exports.fillConfig = {
    kernelName: tfjs_1.Fill,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var backend = args.backend;
        var _a = args.attrs, shape = _a.shape, value = _a.value;
        var dtype = args.attrs.dtype;
        // TODO(cais, nkreeger): Investigate whether backend can be made into
        // a dtype helper method. The underlying op kernel doesn't accept undefined
        // or null dtype.
        if (dtype == null) {
            if (typeof value === 'number') {
                dtype = 'float32';
            }
            else {
                dtype = 'string';
            }
        }
        var shapeTensor = (0, tfjs_1.tensor1d)(shape, 'int32');
        var valueTensor = (0, tfjs_1.scalar)(value, dtype);
        var opAttrs = [
            {
                name: 'T',
                type: backend.binding.TF_ATTR_TYPE,
                value: backend.getDTypeInteger(dtype)
            },
            {
                name: 'index_type',
                type: backend.binding.TF_ATTR_TYPE,
                value: backend.binding.TF_INT32
            }
        ];
        var res = backend.executeSingleOutput(tfjs_1.Fill, opAttrs, [shapeTensor, valueTensor]);
        shapeTensor.dispose();
        valueTensor.dispose();
        return res;
    }
};
