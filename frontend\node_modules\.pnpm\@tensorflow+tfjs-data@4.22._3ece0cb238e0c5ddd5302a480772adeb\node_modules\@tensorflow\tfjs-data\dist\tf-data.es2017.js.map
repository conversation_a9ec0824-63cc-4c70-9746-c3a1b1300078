{"version": 3, "file": "tf-data.es2017.js", "sources": ["../../../../node_modules/seedrandom/lib/alea.js", "../../../../node_modules/seedrandom/lib/xor128.js", "../../../../node_modules/seedrandom/lib/xorwow.js", "../../../../node_modules/seedrandom/lib/xorshift7.js", "../../../../node_modules/seedrandom/lib/xor4096.js", "../../../../node_modules/seedrandom/lib/tychei.js", "../../../../node_modules/seedrandom/seedrandom.js", "../../../../node_modules/seedrandom/index.js", "../../../../tfjs-data/src/util/deep_map.ts", "../../../../tfjs-data/src/util/deep_clone.ts", "../../../../tfjs-data/src/util/ring_buffer.ts", "../../../../tfjs-data/src/util/growing_ring_buffer.ts", "../../../../tfjs-data/src/iterators/lazy_iterator.ts", "../../../../tfjs-data/src/dataset.ts", "../../../../tfjs-data/src/datasets/text_line_dataset.ts", "../../../../tfjs-data/src/datasets/csv_dataset.ts", "../../../../tfjs-data/src/iterators/microphone_iterator.ts", "../../../../tfjs-data/src/iterators/webcam_iterator.ts", "../../../../tfjs-data/src/datasource.ts", "../../../../tfjs-data/src/iterators/string_iterator.ts", "../../../../tfjs-data/src/iterators/byte_chunk_iterator.ts", "../../../../tfjs-data/src/iterators/file_chunk_iterator.ts", "../../../../tfjs-data/src/iterators/url_chunk_iterator.ts", "../../../../tfjs-data/src/util/source_util.ts", "../../../../tfjs-data/src/sources/file_data_source.ts", "../../../../tfjs-data/src/sources/url_data_source.ts", "../../../../tfjs-data/src/readers.ts", "../../../../tfjs-data/src/version.ts"], "sourcesContent": ["// A port of an algorithm by <PERSON> <<EMAIL>>, 2010\n// http://baagoe.com/en/RandomMusings/javascript/\n// https://github.com/nquinlan/better-random-numbers-for-javascript-mirror\n// Original work is under MIT license -\n\n// Copyright (C) 2010 by <PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\n(function(global, module, define) {\n\nfunction Alea(seed) {\n  var me = this, mash = Mash();\n\n  me.next = function() {\n    var t = 2091639 * me.s0 + me.c * 2.3283064365386963e-10; // 2^-32\n    me.s0 = me.s1;\n    me.s1 = me.s2;\n    return me.s2 = t - (me.c = t | 0);\n  };\n\n  // Apply the seeding algorithm from Baagoe.\n  me.c = 1;\n  me.s0 = mash(' ');\n  me.s1 = mash(' ');\n  me.s2 = mash(' ');\n  me.s0 -= mash(seed);\n  if (me.s0 < 0) { me.s0 += 1; }\n  me.s1 -= mash(seed);\n  if (me.s1 < 0) { me.s1 += 1; }\n  me.s2 -= mash(seed);\n  if (me.s2 < 0) { me.s2 += 1; }\n  mash = null;\n}\n\nfunction copy(f, t) {\n  t.c = f.c;\n  t.s0 = f.s0;\n  t.s1 = f.s1;\n  t.s2 = f.s2;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new Alea(seed),\n      state = opts && opts.state,\n      prng = xg.next;\n  prng.int32 = function() { return (xg.next() * 0x100000000) | 0; }\n  prng.double = function() {\n    return prng() + (prng() * 0x200000 | 0) * 1.1102230246251565e-16; // 2^-53\n  };\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nfunction Mash() {\n  var n = 0xefc8249d;\n\n  var mash = function(data) {\n    data = String(data);\n    for (var i = 0; i < data.length; i++) {\n      n += data.charCodeAt(i);\n      var h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n\n  return mash;\n}\n\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.alea = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n\n  // Set up generator function.\n  me.next = function() {\n    var t = me.x ^ (me.x << 11);\n    me.x = me.y;\n    me.y = me.z;\n    me.z = me.w;\n    return me.w ^= (me.w >>> 19) ^ t ^ (t >>> 8);\n  };\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor128 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorwow\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var t = (me.x ^ (me.x >>> 2));\n    me.x = me.y; me.y = me.z; me.z = me.w; me.w = me.v;\n    return (me.d = (me.d + 362437 | 0)) +\n       (me.v = (me.v ^ (me.v << 4)) ^ (t ^ (t << 1))) | 0;\n  };\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n  me.v = 0;\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    if (k == strseed.length) {\n      me.d = me.x << 10 ^ me.x >>> 4;\n    }\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  t.v = f.v;\n  t.d = f.d;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorwow = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorshift7\" algorithm by\n// <PERSON> and <PERSON>:\n// \"On the Xorgshift Random Number Generators\"\n// http://saluc.engr.uconn.edu/refs/crypto/rng/panneton05onthexorshift.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    // Update xor generator.\n    var X = me.x, i = me.i, t, v, w;\n    t = X[i]; t ^= (t >>> 7); v = t ^ (t << 24);\n    t = X[(i + 1) & 7]; v ^= t ^ (t >>> 10);\n    t = X[(i + 3) & 7]; v ^= t ^ (t >>> 3);\n    t = X[(i + 4) & 7]; v ^= t ^ (t << 7);\n    t = X[(i + 7) & 7]; t = t ^ (t << 13); v ^= t ^ (t << 9);\n    X[i] = v;\n    me.i = (i + 1) & 7;\n    return v;\n  };\n\n  function init(me, seed) {\n    var j, w, X = [];\n\n    if (seed === (seed | 0)) {\n      // Seed state array using a 32-bit integer.\n      w = X[0] = seed;\n    } else {\n      // Seed state using a string.\n      seed = '' + seed;\n      for (j = 0; j < seed.length; ++j) {\n        X[j & 7] = (X[j & 7] << 15) ^\n            (seed.charCodeAt(j) + X[(j + 1) & 7] << 13);\n      }\n    }\n    // Enforce an array length of 8, not all zeroes.\n    while (X.length < 8) X.push(0);\n    for (j = 0; j < 8 && X[j] === 0; ++j);\n    if (j == 8) w = X[7] = -1; else w = X[j];\n\n    me.x = X;\n    me.i = 0;\n\n    // Discard an initial 256 values.\n    for (j = 256; j > 0; --j) {\n      me.next();\n    }\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.x = f.x.slice();\n  t.i = f.i;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.x) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorshift7 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n", "// A Javascript implementaion of <PERSON>'s Xorgens xor4096 algorithm.\n//\n// This fast non-cryptographic random number generator is designed for\n// use in Monte-Carlo algorithms. It combines a long-period xorshift\n// generator with a Weyl generator, and it passes all common batteries\n// of stasticial tests for randomness while consuming only a few nanoseconds\n// for each prng generated.  For background on the generator, see <PERSON>'s\n// paper: \"Some long-period random number generators using shifts and xors.\"\n// http://arxiv.org/pdf/1004.3115v1.pdf\n//\n// Usage:\n//\n// var xor4096 = require('xor4096');\n// random = xor4096(1);                        // Seed with int32 or string.\n// assert.equal(random(), 0.1520436450538547); // (0, 1) range, 53 bits.\n// assert.equal(random.int32(), 1806534897);   // signed int32, 32 bits.\n//\n// For nonzero numeric keys, this impelementation provides a sequence\n// identical to that by <PERSON>'s xorgens 3 implementaion in C.  This\n// implementation also provides for initalizing the generator with\n// string seeds, or for saving and restoring the state of the generator.\n//\n// On Chrome, this prng benchmarks about 2.1 times slower than\n// Javascript's built-in Math.random().\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    var w = me.w,\n        X = me.X, i = me.i, t, v;\n    // Update Weyl generator.\n    me.w = w = (w + 0x61c88647) | 0;\n    // Update xor generator.\n    v = X[(i + 34) & 127];\n    t = X[i = ((i + 1) & 127)];\n    v ^= v << 13;\n    t ^= t << 17;\n    v ^= v >>> 15;\n    t ^= t >>> 12;\n    // Update Xor generator array state.\n    v = X[i] = v ^ t;\n    me.i = i;\n    // Result is the combination.\n    return (v + (w ^ (w >>> 16))) | 0;\n  };\n\n  function init(me, seed) {\n    var t, v, i, j, w, X = [], limit = 128;\n    if (seed === (seed | 0)) {\n      // Numeric seeds initialize v, which is used to generates X.\n      v = seed;\n      seed = null;\n    } else {\n      // String seeds are mixed into v and X one character at a time.\n      seed = seed + '\\0';\n      v = 0;\n      limit = Math.max(limit, seed.length);\n    }\n    // Initialize circular array and weyl value.\n    for (i = 0, j = -32; j < limit; ++j) {\n      // Put the unicode characters into the array, and shuffle them.\n      if (seed) v ^= seed.charCodeAt((j + 32) % seed.length);\n      // After 32 shuffles, take v as the starting w value.\n      if (j === 0) w = v;\n      v ^= v << 10;\n      v ^= v >>> 15;\n      v ^= v << 4;\n      v ^= v >>> 13;\n      if (j >= 0) {\n        w = (w + 0x61c88647) | 0;     // Weyl.\n        t = (X[j & 127] ^= (v + w));  // Combine xor and weyl to init array.\n        i = (0 == t) ? i + 1 : 0;     // Count zeroes.\n      }\n    }\n    // We have detected all zeroes; make the key nonzero.\n    if (i >= 128) {\n      X[(seed && seed.length || 0) & 127] = -1;\n    }\n    // Run the generator 512 times to further mix the state before using it.\n    // Factoring this as a function slows the main generator, so it is just\n    // unrolled here.  The weyl generator is not advanced while warming up.\n    i = 127;\n    for (j = 4 * 128; j > 0; --j) {\n      v = X[(i + 34) & 127];\n      t = X[i = ((i + 1) & 127)];\n      v ^= v << 13;\n      t ^= t << 17;\n      v ^= v >>> 15;\n      t ^= t >>> 12;\n      X[i] = v ^ t;\n    }\n    // Storing state as object members is faster than using closure variables.\n    me.w = w;\n    me.X = X;\n    me.i = i;\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.i = f.i;\n  t.w = f.w;\n  t.X = f.X.slice();\n  return t;\n};\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.X) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor4096 = impl;\n}\n\n})(\n  this,                                     // window object or global\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n", "// A Javascript implementaion of the \"Tyche-i\" prng algorithm by\n// <PERSON> and <PERSON><PERSON><PERSON>.\n// See https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var b = me.b, c = me.c, d = me.d, a = me.a;\n    b = (b << 25) ^ (b >>> 7) ^ c;\n    c = (c - d) | 0;\n    d = (d << 24) ^ (d >>> 8) ^ a;\n    a = (a - b) | 0;\n    me.b = b = (b << 20) ^ (b >>> 12) ^ c;\n    me.c = c = (c - d) | 0;\n    me.d = (d << 16) ^ (c >>> 16) ^ a;\n    return me.a = (a - b) | 0;\n  };\n\n  /* The following is non-inverted tyche, which has better internal\n   * bit diffusion, but which is about 25% slower than tyche-i in JS.\n  me.next = function() {\n    var a = me.a, b = me.b, c = me.c, d = me.d;\n    a = (me.a + me.b | 0) >>> 0;\n    d = me.d ^ a; d = d << 16 ^ d >>> 16;\n    c = me.c + d | 0;\n    b = me.b ^ c; b = b << 12 ^ d >>> 20;\n    me.a = a = a + b | 0;\n    d = d ^ a; me.d = d = d << 8 ^ d >>> 24;\n    me.c = c = c + d | 0;\n    b = b ^ c;\n    return me.b = (b << 7 ^ b >>> 25);\n  }\n  */\n\n  me.a = 0;\n  me.b = 0;\n  me.c = 2654435769 | 0;\n  me.d = 1367130551;\n\n  if (seed === Math.floor(seed)) {\n    // Integer seed.\n    me.a = (seed / 0x100000000) | 0;\n    me.b = seed | 0;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 20; k++) {\n    me.b ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.a = f.a;\n  t.b = f.b;\n  t.c = f.c;\n  t.d = f.d;\n  return t;\n};\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.tychei = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "/*\nCopyright 2019 <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\n\n(function (global, pool, math) {\n//\n// The following constants are related to IEEE 754 limits.\n//\n\nvar width = 256,        // each RC4 output is 0 <= x < 256\n    chunks = 6,         // at least six RC4 outputs for each double\n    digits = 52,        // there are 52 significant digits in a double\n    rngname = 'random', // rngname: name for Math.random and Math.seedrandom\n    startdenom = math.pow(width, chunks),\n    significance = math.pow(2, digits),\n    overflow = significance * 2,\n    mask = width - 1,\n    nodecrypto;         // node.js crypto module, initialized at the bottom.\n\n//\n// seedrandom()\n// This is the seedrandom function described above.\n//\nfunction seedrandom(seed, options, callback) {\n  var key = [];\n  options = (options == true) ? { entropy: true } : (options || {});\n\n  // Flatten the seed string or build one from local entropy if needed.\n  var shortseed = mixkey(flatten(\n    options.entropy ? [seed, tostring(pool)] :\n    (seed == null) ? autoseed() : seed, 3), key);\n\n  // Use the seed to initialize an ARC4 generator.\n  var arc4 = new ARC4(key);\n\n  // This function returns a random double in [0, 1) that contains\n  // randomness in every bit of the mantissa of the IEEE 754 value.\n  var prng = function() {\n    var n = arc4.g(chunks),             // Start with a numerator n < 2 ^ 48\n        d = startdenom,                 //   and denominator d = 2 ^ 48.\n        x = 0;                          //   and no 'extra last byte'.\n    while (n < significance) {          // Fill up all significant digits by\n      n = (n + x) * width;              //   shifting numerator and\n      d *= width;                       //   denominator and generating a\n      x = arc4.g(1);                    //   new least-significant-byte.\n    }\n    while (n >= overflow) {             // To avoid rounding up, before adding\n      n /= 2;                           //   last byte, shift everything\n      d /= 2;                           //   right using integer math until\n      x >>>= 1;                         //   we have exactly the desired bits.\n    }\n    return (n + x) / d;                 // Form the number within [0, 1).\n  };\n\n  prng.int32 = function() { return arc4.g(4) | 0; }\n  prng.quick = function() { return arc4.g(4) / 0x100000000; }\n  prng.double = prng;\n\n  // Mix the randomness into accumulated entropy.\n  mixkey(tostring(arc4.S), pool);\n\n  // Calling convention: what to return as a function of prng, seed, is_math.\n  return (options.pass || callback ||\n      function(prng, seed, is_math_call, state) {\n        if (state) {\n          // Load the arc4 state from the given state if it has an S array.\n          if (state.S) { copy(state, arc4); }\n          // Only provide the .state method if requested via options.state.\n          prng.state = function() { return copy(arc4, {}); }\n        }\n\n        // If called as a method of Math (Math.seedrandom()), mutate\n        // Math.random because that is how seedrandom.js has worked since v1.0.\n        if (is_math_call) { math[rngname] = prng; return seed; }\n\n        // Otherwise, it is a newer calling convention, so return the\n        // prng directly.\n        else return prng;\n      })(\n  prng,\n  shortseed,\n  'global' in options ? options.global : (this == math),\n  options.state);\n}\n\n//\n// ARC4\n//\n// An ARC4 implementation.  The constructor takes a key in the form of\n// an array of at most (width) integers that should be 0 <= x < (width).\n//\n// The g(count) method returns a pseudorandom integer that concatenates\n// the next (count) outputs from ARC4.  Its return value is a number x\n// that is in the range 0 <= x < (width ^ count).\n//\nfunction ARC4(key) {\n  var t, keylen = key.length,\n      me = this, i = 0, j = me.i = me.j = 0, s = me.S = [];\n\n  // The empty key [] is treated as [0].\n  if (!keylen) { key = [keylen++]; }\n\n  // Set up S using the standard key scheduling algorithm.\n  while (i < width) {\n    s[i] = i++;\n  }\n  for (i = 0; i < width; i++) {\n    s[i] = s[j = mask & (j + key[i % keylen] + (t = s[i]))];\n    s[j] = t;\n  }\n\n  // The \"g\" method returns the next (count) outputs as one number.\n  (me.g = function(count) {\n    // Using instance members instead of closure state nearly doubles speed.\n    var t, r = 0,\n        i = me.i, j = me.j, s = me.S;\n    while (count--) {\n      t = s[i = mask & (i + 1)];\n      r = r * width + s[mask & ((s[i] = s[j = mask & (j + t)]) + (s[j] = t))];\n    }\n    me.i = i; me.j = j;\n    return r;\n    // For robust unpredictability, the function call below automatically\n    // discards an initial batch of values.  This is called RC4-drop[256].\n    // See http://google.com/search?q=rsa+fluhrer+response&btnI\n  })(width);\n}\n\n//\n// copy()\n// Copies internal state of ARC4 to or from a plain object.\n//\nfunction copy(f, t) {\n  t.i = f.i;\n  t.j = f.j;\n  t.S = f.S.slice();\n  return t;\n};\n\n//\n// flatten()\n// Converts an object tree to nested arrays of strings.\n//\nfunction flatten(obj, depth) {\n  var result = [], typ = (typeof obj), prop;\n  if (depth && typ == 'object') {\n    for (prop in obj) {\n      try { result.push(flatten(obj[prop], depth - 1)); } catch (e) {}\n    }\n  }\n  return (result.length ? result : typ == 'string' ? obj : obj + '\\0');\n}\n\n//\n// mixkey()\n// Mixes a string seed into a key that is an array of integers, and\n// returns a shortened string seed that is equivalent to the result key.\n//\nfunction mixkey(seed, key) {\n  var stringseed = seed + '', smear, j = 0;\n  while (j < stringseed.length) {\n    key[mask & j] =\n      mask & ((smear ^= key[mask & j] * 19) + stringseed.charCodeAt(j++));\n  }\n  return tostring(key);\n}\n\n//\n// autoseed()\n// Returns an object for autoseeding, using window.crypto and Node crypto\n// module if available.\n//\nfunction autoseed() {\n  try {\n    var out;\n    if (nodecrypto && (out = nodecrypto.randomBytes)) {\n      // The use of 'out' to remember randomBytes makes tight minified code.\n      out = out(width);\n    } else {\n      out = new Uint8Array(width);\n      (global.crypto || global.msCrypto).getRandomValues(out);\n    }\n    return tostring(out);\n  } catch (e) {\n    var browser = global.navigator,\n        plugins = browser && browser.plugins;\n    return [+new Date, global, plugins, global.screen, tostring(pool)];\n  }\n}\n\n//\n// tostring()\n// Converts an array of charcodes to a string\n//\nfunction tostring(a) {\n  return String.fromCharCode.apply(0, a);\n}\n\n//\n// When seedrandom.js is loaded, we immediately mix a few bits\n// from the built-in RNG into the entropy pool.  Because we do\n// not want to interfere with deterministic PRNG state later,\n// seedrandom will not call math.random on its own again after\n// initialization.\n//\nmixkey(math.random(), pool);\n\n//\n// Nodejs and AMD support: export the implementation as a module using\n// either convention.\n//\nif ((typeof module) == 'object' && module.exports) {\n  module.exports = seedrandom;\n  // When in node.js, try using crypto package for autoseeding.\n  try {\n    nodecrypto = require('crypto');\n  } catch (ex) {}\n} else if ((typeof define) == 'function' && define.amd) {\n  define(function() { return seedrandom; });\n} else {\n  // When included as a plain script, set up Math.seedrandom global.\n  math['seed' + rngname] = seedrandom;\n}\n\n\n// End anonymous scope, and pass initial values.\n})(\n  // global: `self` in browsers (including strict mode and web workers),\n  // otherwise `this` in Node and other environments\n  (typeof self !== 'undefined') ? self : this,\n  [],     // pool: entropy pool starts empty\n  Math    // math: package containing random, pow, and seedrandom\n);\n", "// A library of seedable RNGs implemented in Javascript.\n//\n// Usage:\n//\n// var seedrandom = require('seedrandom');\n// var random = seedrandom(1); // or any seed.\n// var x = random();       // 0 <= x < 1.  Every bit is random.\n// var x = random.quick(); // 0 <= x < 1.  32 bits of randomness.\n\n// alea, a 53-bit multiply-with-carry generator by <PERSON>.\n// Period: ~2^116\n// Reported to pass all BigCrush tests.\nvar alea = require('./lib/alea');\n\n// xor128, a pure xor-shift generator by <PERSON>.\n// Period: 2^128-1.\n// Reported to fail: MatrixRank and LinearComp.\nvar xor128 = require('./lib/xor128');\n\n// xorwow, <PERSON>'s 160-bit xor-shift combined plus weyl.\n// Period: 2^192-2^32\n// Reported to fail: CollisionOver, SimpPoker, and LinearComp.\nvar xorwow = require('./lib/xorwow');\n\n// xorshift7, by <PERSON> and <PERSON>, takes\n// a different approach: it adds robustness by allowing more shifts\n// than Marsaglia's original three.  It is a 7-shift generator\n// with 256 bits, that passes BigCrush with no systmatic failures.\n// Period 2^256-1.\n// No systematic BigCrush failures reported.\nvar xorshift7 = require('./lib/xorshift7');\n\n// xor4096, by Richard Brent, is a 4096-bit xor-shift with a\n// very long period that also adds a Weyl generator. It also passes\n// BigCrush with no systematic failures.  Its long period may\n// be useful if you have many generators and need to avoid\n// collisions.\n// Period: 2^4128-2^32.\n// No systematic BigCrush failures reported.\nvar xor4096 = require('./lib/xor4096');\n\n// Tyche-i, by Samuel Neves and Filipe Araujo, is a bit-shifting random\n// number generator derived from ChaCha, a modern stream cipher.\n// https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n// Period: ~2^127\n// No systematic BigCrush failures reported.\nvar tychei = require('./lib/tychei');\n\n// The original ARC4-based prng included in this library.\n// Period: ~2^1600\nvar sr = require('./seedrandom');\n\nsr.alea = alea;\nsr.xor128 = xor128;\nsr.xorwow = xorwow;\nsr.xorshift7 = xorshift7;\nsr.xor4096 = xor4096;\nsr.tychei = tychei;\n\nmodule.exports = sr;\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\n\n// tslint:disable:no-any\n\n/**\n * A return value for a mapping function that can be applied via deepMap.\n *\n * If recurse is true, the value should be empty, and iteration will continue\n * into the object or array.\n */\nexport type DeepMapResult = {\n  value: any,\n  recurse: boolean\n};\n\n/**\n * Apply a mapping function to a nested structure in a recursive manner.\n *\n * The result of the mapping is an object with the same nested structure (i.e.,\n * of arrays and dicts) as the input, except that some subtrees are replaced,\n * according to the results of the mapping function.\n *\n * Mappings are memoized.  Thus, if the nested structure contains the same\n * object in multiple positions, the output will contain the same mapped object\n * in those positions.  Cycles are not supported, however.\n *\n * @param input: The object to which to apply the mapping function.\n * @param mapFn: A function that expects a single node of the object tree, and\n *   returns a `DeepMapResult`.  The `DeepMapResult` either provides a\n *   replacement value for that node (i.e., replacing the subtree), or indicates\n *   that the node should be processed recursively.\n */\nexport function deepMap(input: any, mapFn: (x: any) => DeepMapResult): any|\n    any[] {\n  return deepMapInternal(input, mapFn);\n}\n\n/**\n * @param seen: A Map of known object mappings (i.e., memoized results of\n *   `mapFn()`)\n * @param containedIn: An set containing objects on the reference path currently\n *   being processed (used to detect cycles).\n */\nfunction deepMapInternal(\n    input: any, mapFn: (x: any) => DeepMapResult,\n    seen: Map<any, any> = new Map(), containedIn: Set<{}> = new Set()): any|\n    any[] {\n  if (input == null) {\n    return null;\n  }\n  if (typeof Blob === 'function' && input instanceof Blob) {\n    return input.slice();\n  }\n\n  if (containedIn.has(input)) {\n    throw new Error('Circular references are not supported.');\n  }\n  if (seen.has(input)) {\n    return seen.get(input);\n  }\n  const result = mapFn(input);\n\n  if (result.recurse && result.value !== null) {\n    throw new Error(\n        'A deep map function may not return both a value and recurse=true.');\n  }\n\n  if (!result.recurse) {\n    seen.set(input, result.value);\n    return result.value;\n  } else if (isIterable(input)) {\n    // tslint:disable-next-line:no-any\n    const mappedIterable: any|any[] = Array.isArray(input) ? [] : {};\n    containedIn.add(input);\n    for (const k in input) {\n      const child = input[k];\n      const childResult = deepMapInternal(child, mapFn, seen, containedIn);\n      mappedIterable[k] = childResult;\n    }\n    containedIn.delete(input);\n    if (input.__proto__) {\n      mappedIterable.__proto__ = input.__proto__;\n    }\n    return mappedIterable;\n  } else {\n    throw new Error(`Can't recurse into non-iterable type: ${input}`);\n  }\n}\n\n// TODO(soergel, kangyizhang) Reconsider naming of deepZip() to avoid confusion\n// with zip()\n\n/**\n * Zip nested structures together in a recursive manner.\n *\n * This has the effect of transposing or pivoting data, e.g. converting it from\n * a row-major representation to a column-major representation.\n *\n * For example, `deepZip([{a: 1, b: 2}, {a: 3, b: 4}])` returns\n * `{a: [1, 3], b: [2, 4]}`.\n *\n * The inputs should all have the same nested structure (i.e., of arrays and\n * dicts).  The result is a single object with the same nested structure, where\n * the leaves are arrays collecting the values of the inputs at that location\n * (or, optionally, the result of a custom function applied to those arrays).\n *\n * @param inputs: An array of the objects to zip together.\n * @param zipFn: (optional) A function that expects an array of elements at a\n *   single node of the object tree, and returns a `DeepMapResult`.  The\n *   `DeepMapResult` either provides a result value for that node (i.e.,\n *   representing the subtree), or indicates that the node should be processed\n *   recursively.  The default zipFn recurses as far as possible and places\n *   arrays at the leaves.\n */\nexport function deepZip(\n    inputs: any[], zipFn: (xs: any[]) => DeepMapResult = zipToList): any|any[] {\n  return deepZipInternal(inputs, zipFn);\n}\n\n/**\n * @param containedIn: An set containing objects on the reference path currently\n *   being processed (used to detect cycles).\n */\nfunction deepZipInternal(\n    inputs: any[], zipFn: (xs: any[]) => DeepMapResult,\n    containedIn: Set<{}> = new Set()): any|any[] {\n  // The recursion follows the structure of input 0; it's assumed that all the\n  // other inputs have the same structure.\n  const input = inputs[0];\n  if (containedIn.has(input)) {\n    throw new Error('Circular references are not supported.');\n  }\n  const result = zipFn(inputs);\n\n  if (result.recurse && result.value !== null) {\n    throw new Error(\n        'A deep zip function may not return both a value and recurse=true.');\n  }\n\n  if (!result.recurse) {\n    return result.value;\n  } else if (isIterable(input)) {\n    // tslint:disable-next-line:no-any\n    const mappedIterable: any|any[] = Array.isArray(input) ? [] : {};\n    containedIn.add(input);\n    for (const k in input) {\n      const children = inputs.map(x => x[k]);\n      const childResult = deepZipInternal(children, zipFn, containedIn);\n      mappedIterable[k] = childResult;\n    }\n    containedIn.delete(input);\n    return mappedIterable;\n  } else {\n    throw new Error(`Can't recurse into non-iterable type: ${input}`);\n  }\n}\n\n// tslint:disable-next-line:no-any\nexport function zipToList(x: any[]): DeepMapResult {\n  if (x === null) {\n    return null;\n  }\n  // TODO(soergel): validate array type?\n\n  if (isIterable(x[0])) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: x, recurse: false};\n  }\n}\n\n/**\n * A return value for an async map function for use with deepMapAndAwaitAll.\n *\n * If recurse is true, the value should be empty, and iteration will continue\n * into the object or array.\n */\nexport type DeepMapAsyncResult = {\n  value: Promise<any>,\n  recurse: boolean\n};\n\n/**\n * Apply an async mapping function to a nested structure in a recursive manner.\n *\n * This first creates a nested structure of Promises, and then awaits all of\n * those, resulting in a single Promise for a resolved nested structure.\n *\n * The result of the mapping is an object with the same nested structure (i.e.,\n * of arrays and dicts) as the input, except that some subtrees are replaced,\n * according to the results of the mapping function.\n *\n * Mappings are memoized.  Thus, if the nested structure contains the same\n * object in multiple positions, the output will contain the same mapped object\n * in those positions.  Cycles are not supported, however.\n *\n * @param input: The object to which to apply the mapping function.\n * @param mapFn: A function that expects a single node of the object tree, and\n *   returns a `DeepMapAsyncResult`.  The `DeepMapAsyncResult` either provides\n *   a `Promise` for a replacement value for that node (i.e., replacing the\n *   subtree), or indicates that the node should be processed recursively.  Note\n *   that the decision whether or not to recurse must be made immediately; only\n *   the mapped value may be promised.\n */\nexport async function deepMapAndAwaitAll(\n    input: any, mapFn: (x: any) => DeepMapAsyncResult): Promise<any|any[]> {\n  const seen: Map<any, any> = new Map();\n\n  // First do a normal deepMap, collecting Promises in 'seen' as a side effect.\n  deepMapInternal(input, mapFn, seen);\n\n  // Replace the Promises in 'seen' in place.\n  // Note TypeScript provides no async map iteration, and regular map iteration\n  // is broken too, so sadly we have to do Array.from() to make it work.\n  // (There's no advantage to Promise.all(), and that would be tricky anyway.)\n  for (const key of Array.from(seen.keys())) {\n    const value = seen.get(key);\n    if (tf.util.isPromise(value)) {\n      const mappedValue = await value;\n      seen.set(key, mappedValue);\n    }\n  }\n\n  // Normal deepMap again, this time filling in the resolved values.\n  // It's unfortunate that we have to do two passes.\n  // TODO(soergel): test performance and think harder about a fast solution.\n  const result = deepMapInternal(input, mapFn, seen);\n  return result;\n}\n\n/**\n * Determine whether the argument is iterable.\n *\n * @returns true if the argument is an array or any non-Tensor object.\n */\n// tslint:disable-next-line:no-any\nexport function isIterable(obj: any): boolean {\n  let isTextDecoder = false;\n  if (tf.env().get('IS_BROWSER')) {\n    isTextDecoder = obj instanceof TextDecoder;\n  } else {\n    // tslint:disable-next-line:no-require-imports\n    const {StringDecoder} = require('string_decoder');\n    isTextDecoder = obj instanceof StringDecoder;\n  }\n  return obj != null && (!ArrayBuffer.isView(obj)) &&\n      (Array.isArray(obj) ||\n       (typeof obj === 'object' && !(obj instanceof tf.Tensor) &&\n        !(obj instanceof Promise) && !isTextDecoder));\n}\n\n/**\n * Determine whether the argument can be converted to Tensor.\n *\n * Tensors, primitives, arrays, and TypedArrays all qualify; anything else does\n * not.\n *\n * @returns true if the argument can be converted to Tensor.\n */\n// tslint:disable-next-line:no-any\nexport function canTensorify(obj: any): boolean {\n  return obj == null || isPrimitive(obj) || Array.isArray(obj) ||\n      (typeof obj === 'object' && (obj instanceof tf.Tensor)) ||\n      tf.util.isTypedArray(obj);\n}\n\n/**\n * Returns true if the given `value` is a primitive type. Otherwise returns\n * false. This is equivalant to node util.isPrimitive\n */\nfunction isPrimitive(value: any): boolean {\n  return (\n      value === null ||\n      (typeof value !== 'object' && typeof value !== 'function'));\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {deepMap, DeepMapResult, isIterable} from './deep_map';\n\nexport function deepClone<T>(container: T): T {\n  return deepMap(container, cloneIfTensor);\n}\n\n// tslint:disable-next-line: no-any\nfunction cloneIfTensor(item: any): DeepMapResult {\n  if (item instanceof tf.Tensor) {\n    return ({value: item.clone(), recurse: false});\n  } else if (isIterable(item)) {\n    return {value: null, recurse: true};\n  } else {\n    return {value: item, recurse: false};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n/**\n * A ring buffer, providing O(1) FIFO, LIFO, and related operations.\n */\nexport class RingBuffer<T> {\n  // Note we store the indices in the range 0 <= index < 2*capacity.\n  // This allows us to distinguish the full from the empty case.\n  // See https://www.snellman.net/blog/archive/2016-12-13-ring-buffers/\n  protected begin = 0;  // inclusive\n  protected end = 0;    // exclusive\n  protected doubledCapacity: number;\n\n  protected data: T[];\n\n  /**\n   * Constructs a `RingBuffer`.\n   * @param capacity The number of items that the buffer can accomodate.\n   */\n  constructor(public capacity: number) {\n    if (capacity == null) {\n      throw new RangeError('Can\\'t create a ring buffer of unknown capacity.');\n    }\n    if (capacity < 1) {\n      throw new RangeError('Can\\'t create ring buffer of capacity < 1.');\n    }\n    this.data = new Array<T>(capacity);\n    this.doubledCapacity = 2 * capacity;\n  }\n\n  /**\n   * Map any index into the range 0 <= index < 2*capacity.\n   */\n  protected wrap(index: number) {\n    // don't trust % on negative numbers\n    while (index < 0) {\n      index += this.doubledCapacity;\n    }\n    return index % this.doubledCapacity;\n  }\n\n  protected get(index: number) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t get item at a negative index.');\n    }\n    return this.data[index % this.capacity];\n  }\n\n  protected set(index: number, value: T) {\n    if (index < 0) {\n      throw new RangeError('Can\\'t set item at a negative index.');\n    }\n    this.data[index % this.capacity] = value;\n  }\n\n  /**\n   * Returns the current number of items in the buffer.\n   */\n  length(): number {\n    let length = this.end - this.begin;\n    if (length < 0) {\n      length = this.doubledCapacity + length;\n    }\n    return length;\n  }\n\n  /**\n   * Reports whether the buffer is full.\n   * @returns true if the number of items in the buffer equals its capacity, and\n   *   false otherwise.\n   */\n  isFull() {\n    return this.length() === this.capacity;\n  }\n\n  /**\n   * Reports whether the buffer is empty.\n   * @returns true if the number of items in the buffer equals zero, and\n   *   false otherwise.\n   */\n  isEmpty() {\n    return this.length() === 0;\n  }\n\n  /**\n   * Adds an item to the end of the buffer.\n   */\n  push(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.set(this.end, value);\n    this.end = this.wrap(this.end + 1);\n  }\n\n  /**\n   * Adds many items to the end of the buffer, in order.\n   */\n  pushAll(values: T[]) {\n    for (const value of values) {\n      this.push(value);\n    }\n  }\n\n  /**\n   * Removes and returns the last item in the buffer.\n   */\n  pop(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    this.end = this.wrap(this.end - 1);\n    const result = this.get(this.end);\n    this.set(this.end, undefined);\n    return result;\n  }\n\n  /**\n   * Adds an item to the beginning of the buffer.\n   */\n  unshift(value: T) {\n    if (this.isFull()) {\n      throw new RangeError('Ring buffer is full.');\n    }\n    this.begin = this.wrap(this.begin - 1);\n    this.set(this.begin, value);\n  }\n\n  /**\n   * Removes and returns the first item in the buffer.\n   */\n  shift(): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const result = this.get(this.begin);\n    this.set(this.begin, undefined);\n    this.begin = this.wrap(this.begin + 1);\n    return result;\n  }\n\n  /**\n   * Removes and returns a specific item in the buffer, and moves the last item\n   * to the vacated slot.  This is useful for implementing a shuffling stream.\n   * Note that this operation necessarily scrambles the original order.\n   *\n   * @param relativeIndex: the index of the item to remove, relative to the\n   *   first item in the buffer (e.g., hiding the ring nature of the underlying\n   *   storage).\n   */\n  shuffleExcise(relativeIndex: number): T {\n    if (this.isEmpty()) {\n      throw new RangeError('Ring buffer is empty.');\n    }\n    const index = this.wrap(this.begin + relativeIndex);\n    const result = this.get(index);\n    this.set(index, this.pop());\n    return result;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {RingBuffer} from './ring_buffer';\n\nexport class GrowingRingBuffer<T> extends RingBuffer<T> {\n  private static INITIAL_CAPACITY = 32;\n\n  /**\n   * Constructs a `GrowingRingBuffer`.\n   */\n  constructor() {\n    super(GrowingRingBuffer.INITIAL_CAPACITY);\n  }\n\n  override isFull() {\n    return false;\n  }\n\n  override push(value: T) {\n    if (super.isFull()) {\n      this.expand();\n    }\n    super.push(value);\n  }\n\n  override unshift(value: T) {\n    if (super.isFull()) {\n      this.expand();\n    }\n    super.unshift(value);\n  }\n\n  /**\n   * Doubles the capacity of the buffer.\n   */\n  private expand() {\n    const newCapacity = this.capacity * 2;\n    const newData = new Array<T>(newCapacity);\n    const len = this.length();\n\n    // Rotate the buffer to start at index 0 again, since we can't just\n    // allocate more space at the end.\n    for (let i = 0; i < len; i++) {\n      newData[i] = this.get(this.wrap(this.begin + i));\n    }\n\n    this.data = newData;\n    this.capacity = newCapacity;\n    this.doubledCapacity = 2 * this.capacity;\n    this.begin = 0;\n    this.end = len;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {Container} from '../types';\nimport {deepClone} from '../util/deep_clone';\nimport {deepMapAndAwaitAll, DeepMapAsyncResult, DeepMapResult, deepZip, zipToList} from '../util/deep_map';\nimport {GrowingRingBuffer} from '../util/growing_ring_buffer';\nimport {RingBuffer} from '../util/ring_buffer';\n\n/**\n * A nested structure of LazyIterators, used as the input to zip().\n */\nexport type IteratorContainer = Container<LazyIterator<tf.TensorContainer>>;\n\n// Here we implement a simple asynchronous iterator.\n// This lets us avoid using either third-party stream libraries or\n// recent TypeScript language support requiring polyfills.\n\n/**\n * Create a `LazyIterator` from an array of items.\n */\nexport function iteratorFromItems<T>(items: T[]): LazyIterator<T> {\n  return new ArrayIterator(items);\n}\n\n/**\n * Create a `LazyIterator` of incrementing integers.\n */\nexport function iteratorFromIncrementing(start: number): LazyIterator<number> {\n  let i = start;\n  return iteratorFromFunction(() => ({value: i++, done: false}));\n}\n\n/**\n * Create a `LazyIterator` from a function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * await iter.forEachAsync(e => console.log(e));\n * ```\n *\n * @param func A function that produces data on each call.\n */\nexport function iteratorFromFunction<T>(\n    func: () =>\n        IteratorResult<T>| Promise<IteratorResult<T>>): LazyIterator<T> {\n  return new FunctionCallIterator(func);\n}\n\n/**\n * Create a `LazyIterator` by concatenating underlying streams, which are\n * themselves provided as a stream.\n *\n * This can also be thought of as a \"stream flatten\" operation.\n *\n * @param baseIterators A stream of streams to be concatenated.\n * @param baseErrorHandler An optional function that can intercept `Error`s\n *   raised during a `next()` call on the base stream.  This function can decide\n *   whether the error should be propagated, whether the error should be\n *   ignored, or whether the base stream should be terminated.\n */\nexport function iteratorFromConcatenated<T>(\n    baseIterators: LazyIterator<LazyIterator<T>>,\n    baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n  return new ChainedIterator(baseIterators, baseErrorHandler);\n}\n\n/**\n * Create a `LazyIterator` by concatenating streams produced by calling a\n * stream-generating function a given number of times.\n *\n * Since a `LazyIterator` is read-once, it cannot be repeated, but this\n * function can be used to achieve a similar effect:\n *\n *   LazyIterator.ofConcatenatedFunction(() => new MyIterator(), 6);\n *\n * @param iteratorFunc: A function that produces a new stream on each call.\n * @param count: The number of times to call the function.\n * @param baseErrorHandler An optional function that can intercept `Error`s\n *   raised during a `next()` call on the base stream.  This function can decide\n *   whether the error should be propagated, whether the error should be\n *   ignored, or whether the base stream should be terminated.\n */\nexport function iteratorFromConcatenatedFunction<T>(\n    iteratorFunc: () => IteratorResult<LazyIterator<T>>, count: number,\n    baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n  return iteratorFromConcatenated(\n      iteratorFromFunction(iteratorFunc).take(count), baseErrorHandler);\n}\n\n/**\n * Create a `LazyIterator` by zipping together an array, dict, or nested\n * structure of `LazyIterator`s (and perhaps additional constants).\n *\n * The underlying streams must provide elements in a consistent order such\n * that they correspond.\n *\n * Typically, the underlying streams should have the same number of\n * elements. If they do not, the behavior is determined by the\n * `mismatchMode` argument.\n *\n * The nested structure of the `iterators` argument determines the\n * structure of elements in the resulting iterator.\n *\n * @param iterators: An array or object containing LazyIterators at the\n * leaves.\n * @param mismatchMode: Determines what to do when one underlying iterator\n * is exhausted before the others.  `ZipMismatchMode.FAIL` (the default)\n * causes an error to be thrown in this case.  `ZipMismatchMode.SHORTEST`\n * causes the zipped iterator to terminate with the furst underlying\n * streams, so elements remaining on the longer streams are ignored.\n * `ZipMismatchMode.LONGEST` causes the zipped stream to continue, filling\n * in nulls for the exhausted streams, until all streams are exhausted.\n */\nexport function iteratorFromZipped<O extends tf.TensorContainer>(\n    iterators: IteratorContainer,\n    mismatchMode: ZipMismatchMode = ZipMismatchMode.FAIL): LazyIterator<O> {\n  return new ZipIterator<O>(iterators, mismatchMode);\n}\n\n/**\n * An asynchronous iterator, providing lazy access to a potentially\n * unbounded stream of elements.\n *\n * Iterator can be obtained from a dataset:\n * `const iter = await dataset.iterator();`\n */\nexport abstract class LazyIterator<T> {\n  // This class implements AsyncIterator<T>, but we have not yet set the\n  // TypeScript --downlevelIteration flag to enable that.\n\n  abstract summary(): string;\n\n  /**\n   * Returns a `Promise` for the next element in the stream.\n   *\n   * When an item can be provided successfully, the return value is\n   * `{value:T, done:false}`.\n   *\n   * Calling next() on a closed stream returns `{value:null, done:true}`.\n   */\n  abstract next(): Promise<IteratorResult<T>>;\n\n  /**\n   * Collect all remaining elements of a bounded stream into an array.\n   * Obviously this will succeed only for small streams that fit in memory.\n   * Useful for testing.\n   *\n   * @returns A Promise for an array of stream elements, which will resolve\n   *   when the stream is exhausted.\n   */\n  async toArray(): Promise<T[]> {\n    const result: T[] = [];\n    let x = await this.next();\n    while (!x.done) {\n      result.push(x.value);\n      x = await this.next();\n    }\n    return result;\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of stream elements, which will resolve\n   *   when the stream is exhausted.\n   */\n  async toArrayForTest(): Promise<T[]> {\n    const stream = this.prefetch(100);\n    const result: T[] = [];\n    let x = await stream.next();\n    while (!x.done) {\n      result.push(x.value);\n      x = await stream.next();\n    }\n    return result;\n  }\n\n  /**\n   * Draw items from the stream until it is exhausted.\n   *\n   * This can be useful when the stream has side effects but no output.  In\n   * that case, calling this function guarantees that the stream will be\n   * fully processed.\n   */\n  async resolveFully(): Promise<void> {\n    let x = await this.next();\n    while (!x.done) {\n      x = await this.next();\n    }\n  }\n\n  /**\n   * Draw items from the stream until it is exhausted, or a predicate fails.\n   *\n   * This can be useful when the stream has side effects but no output.  In\n   * that case, calling this function guarantees that the stream will be\n   * fully processed.\n   */\n  async resolveWhile(predicate: (r: T) => boolean): Promise<void> {\n    let x = await this.next();\n    let shouldContinue = predicate(x.value);\n    while ((!x.done) && shouldContinue) {\n      x = await this.next();\n      shouldContinue = predicate(x.value);\n    }\n  }\n\n  /**\n   * Handles errors thrown on this stream using a provided handler function.\n   *\n   * @param handler A function that handles any `Error` thrown during a `next()`\n   *   call and returns true if the stream should continue (dropping the failed\n   *   call) or false if the stream should quietly terminate.  If the handler\n   *   itself throws (or rethrows) an `Error`, that will be propagated.\n   *\n   * @returns A `LazyIterator` of elements passed through from upstream,\n   *   possibly filtering or terminating on upstream `next()` calls that\n   *   throw an `Error`.\n   */\n  handleErrors(handler: (error: Error) => boolean): LazyIterator<T> {\n    return new ErrorHandlingLazyIterator(this, handler);\n  }\n\n  // TODO(soergel): Implement reduce() etc.\n\n  /**\n   * Filters this stream according to `predicate`.\n   *\n   * @param predicate A function mapping a stream element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `LazyIterator` of elements for which the predicate was true.\n   */\n  filter(predicate: (value: T) => boolean): LazyIterator<T> {\n    return new FilterIterator(this, predicate);\n  }\n\n  /**\n   * Maps this stream through a 1-to-1 transform.\n   *\n   * @param transform A function mapping a stream element to a transformed\n   *   element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  map<O>(transform: (value: T) => O): LazyIterator<O> {\n    return new MapIterator(this, transform);\n  }\n\n  /**\n   * Maps this stream through an async 1-to-1 transform.\n   *\n   * @param transform A function mapping a stream element to a `Promise` for a\n   *   transformed stream element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  mapAsync<O>(transform: (value: T) => Promise<O>): LazyIterator<O> {\n    return new AsyncMapIterator(this, transform);\n  }\n\n  /**\n   * Maps this stream through a 1-to-1 transform, forcing serial execution.\n   *\n   * @param transform A function mapping a stream element to a transformed\n   *   element.\n   *\n   * @returns A `LazyIterator` of transformed elements.\n   */\n  serialMapAsync<O>(transform: (value: T) => Promise<O>): LazyIterator<O> {\n    return new AsyncMapIterator(this, transform).serial();\n  }\n\n  /**\n   * Maps this stream through a 1-to-many transform.\n   *\n   * @param transform A function mapping a stream element to an array of\n   *   transformed elements.\n   *\n   * @returns A `DataStream` of transformed elements.\n   */\n  flatmap<O>(transform: (value: T) => O[]): LazyIterator<O> {\n    return new FlatmapIterator(this, transform);\n  }\n\n  /**\n   * Apply a function to every element of the stream.\n   *\n   * @param f A function to apply to each stream element.\n   */\n  async forEachAsync(f: (value: T) => void): Promise<void> {\n    return this.map(f).resolveFully();\n  }\n\n  /**\n   * Apply a function to every element of the stream, forcing serial execution.\n   *\n   * @param f A function to apply to each stream element.  Should return 'true'\n   *   to indicate that the stream should continue, or 'false' to cause it to\n   *   terminate.\n   */\n  async serialForEach(f: (value: T) => Promise<boolean>): Promise<void> {\n    return this.serialMapAsync(f).resolveWhile(x => (x === true));\n  }\n\n  /**\n   * Groups elements into batches, represented as arrays of elements.\n   *\n   * We can think of the elements of this iterator as 'rows' (even if they are\n   * nested structures).  By the same token, consecutive values for a given\n   * key within the elements form a 'column'.  This matches the usual sense of\n   * 'row' and 'column' when processing tabular data (e.g., parsing a CSV).\n   *\n   * Thus, \"Row-major\" means that the resulting batch is simply a collection of\n   * rows: `[row1, row2, row3, ...]`.  This is contrast to the column-major\n   * form, which is needed for vectorized computation.\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `LazyIterator` of batches of elements, represented as arrays\n   *   of the original element type.\n   */\n  rowMajorBatch(batchSize: number, smallLastBatch = true): LazyIterator<T[]> {\n    return new RowMajorBatchIterator(this, batchSize, smallLastBatch);\n  }\n\n  /**\n   * Groups elements into batches, represented in column-major form.\n   *\n   * We can think of the elements of this iterator as 'rows' (even if they are\n   * nested structures).  By the same token, consecutive values for a given\n   * key within the elements form a 'column'.  This matches the usual sense of\n   * 'row' and 'column' when processing tabular data (e.g., parsing a CSV).\n   *\n   * Thus, \"column-major\" means that the resulting batch is a (potentially\n   * nested) structure representing the columns.  Each column entry, then,\n   * contains a collection of the values found in that column for a range of\n   * input elements.  This representation allows for vectorized computation, in\n   * contrast to the row-major form.\n   *\n   * The inputs should all have the same nested structure (i.e., of arrays and\n   * dicts).  The result is a single object with the same nested structure,\n   * where the leaves are arrays collecting the values of the inputs at that\n   * location (or, optionally, the result of a custom function applied to those\n   * arrays).\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @param zipFn: (optional) A function that expects an array of elements at a\n   *   single node of the object tree, and returns a `DeepMapResult`.  The\n   *   `DeepMapResult` either provides a result value for that node (i.e.,\n   *   representing the subtree), or indicates that the node should be processed\n   *   recursively.  The default zipFn recurses as far as possible and places\n   *   arrays at the leaves.\n   * @returns A `LazyIterator` of batches of elements, represented as an object\n   *   with collections at the leaves.\n   */\n  columnMajorBatch(\n      batchSize: number, smallLastBatch = true,\n      // tslint:disable-next-line:no-any\n      zipFn: (xs: any[]) => DeepMapResult = zipToList):\n      LazyIterator<tf.TensorContainer> {\n    // First collect the desired number of input elements as a row-major batch.\n    const rowBatches = this.rowMajorBatch(batchSize, smallLastBatch);\n    // Now 'rotate' or 'pivot' the data, collecting all values from each column\n    // in the batch (i.e., for each key within the elements) into an array.\n    return rowBatches.map(x => deepZip(x, zipFn));\n  }\n\n  /**\n   * Concatenate this `LazyIterator` with another.\n   *\n   * @param iterator A `LazyIterator` to be concatenated onto this one.\n   * @param baseErrorHandler An optional function that can intercept `Error`s\n   *   raised during a `next()` call on the base stream.  This function can\n   *   decide whether the error should be propagated, whether the error should\n   *   be ignored, or whether the base stream should be terminated.\n   * @returns A `LazyIterator`.\n   */\n  concatenate(\n      iterator: LazyIterator<T>,\n      baseErrorHandler?: (e: Error) => boolean): LazyIterator<T> {\n    return new ChainedIterator(\n        iteratorFromItems([this, iterator]), baseErrorHandler);\n  }\n\n  /**\n   * Limits this stream to return at most `count` items.\n   *\n   * @param count The maximum number of items to provide from the stream. If\n   * a negative or undefined value is given, the entire stream is returned\n   *   unaltered.\n   */\n  take(count: number): LazyIterator<T> {\n    if (count < 0 || count == null) {\n      return this;\n    }\n    return new TakeIterator(this, count);\n  }\n\n  /**\n   * Skips the first `count` items in this stream.\n   *\n   * @param count The number of items to skip.  If a negative or undefined\n   * value is given, the entire stream is returned unaltered.\n   */\n  skip(count: number): LazyIterator<T> {\n    if (count < 0 || count == null) {\n      return this;\n    }\n    return new SkipIterator(this, count);\n  }\n\n  /**\n   * Prefetch the first `bufferSize` items in this stream.\n   *\n   * Note this prefetches Promises, but makes no guarantees about when those\n   * Promises resolve.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   */\n  prefetch(bufferSize: number): LazyIterator<T> {\n    return new PrefetchIterator(this, bufferSize);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  /**\n   * Randomly shuffles the elements of this stream.\n   *\n   * @param bufferSize: An integer specifying the number of elements from\n   * this stream from which the new stream will sample.\n   * @param seed: (Optional.) An integer specifying the random seed that\n   * will be used to create the distribution.\n   */\n  shuffle(windowSize: number, seed?: string): LazyIterator<T> {\n    return new ShuffleIterator(this, windowSize, seed);\n  }\n\n  /**\n   * Force an iterator to execute serially: each next() call will await the\n   * prior one, so that they cannot execute concurrently.\n   */\n  serial(): LazyIterator<T> {\n    return new SerialIterator(this);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on LazyIterator.  Unfortunately they can't be placed in separate files,\n// due to resulting trouble with circular imports.\n// ============================================================================\n\n// Iterators that just extend LazyIterator directly\n// ============================================================================\n\nclass ArrayIterator<T> extends LazyIterator<T> {\n  private trav = 0;\n  constructor(protected items: T[]) {\n    super();\n  }\n\n  summary() {\n    return `Array of ${this.items.length} items`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    if (this.trav >= this.items.length) {\n      return {value: null, done: true};\n    }\n    const item = this.items[this.trav];\n    this.trav++;\n    return {value: deepClone(item), done: false};\n  }\n}\n\nclass FunctionCallIterator<T> extends LazyIterator<T> {\n  constructor(\n      protected nextFn: () => IteratorResult<T>| Promise<IteratorResult<T>>) {\n    super();\n  }\n\n  summary() {\n    return `Function call`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    try {\n      return this.nextFn();\n    } catch (e) {\n      // Modify the error message but leave the stack trace intact\n      e.message =\n          `Error thrown while iterating through a dataset: ${e.message}`;\n      throw e;\n    }\n  }\n}\n\nclass SerialIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  constructor(protected upstream: LazyIterator<T>) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Serial`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    return this.upstream.next();\n  }\n}\n\nclass SkipIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  count = 0;\n\n  constructor(protected upstream: LazyIterator<T>, protected maxCount: number) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Skip`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    // TODO(soergel): consider tradeoffs of reading in parallel, eg.\n    // collecting next() promises in an Array and then waiting for\n    // Promise.all() of those. Benefit: pseudo-parallel execution.  Drawback:\n    // maybe delayed GC.\n    while (this.count++ < this.maxCount) {\n      const skipped = await this.upstream.next();\n      // short-circuit if upstream is already empty\n      if (skipped.done) {\n        return skipped;\n      }\n      tf.dispose(skipped.value as {});\n    }\n    return this.upstream.next();\n  }\n}\n\nclass TakeIterator<T> extends LazyIterator<T> {\n  count = 0;\n  constructor(protected upstream: LazyIterator<T>, protected maxCount: number) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Take`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    if (this.count++ >= this.maxCount) {\n      return {value: null, done: true};\n    }\n    return this.upstream.next();\n  }\n}\n\n// Note this batch just groups items into row-wise element arrays.\n// Rotating these to a column-wise representation happens only at the dataset\n// level.\nclass RowMajorBatchIterator<T> extends LazyIterator<T[]> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T[]>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>, protected batchSize: number,\n      protected enableSmallLastBatch = true) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> RowMajorBatch`;\n  }\n\n  async next(): Promise<IteratorResult<T[]>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T[]>> {\n    const batch: T[] = [];\n    while (batch.length < this.batchSize) {\n      const item = await this.upstream.next();\n      if (item.done) {\n        if (this.enableSmallLastBatch && batch.length > 0) {\n          return {value: batch, done: false};\n        }\n        return {value: null, done: true};\n      }\n      batch.push(item.value);\n    }\n    return {value: batch, done: false};\n  }\n}\n\nclass FilterIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>,\n      protected predicate: (value: T) => boolean) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Filter`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private async serialNext(): Promise<IteratorResult<T>> {\n    while (true) {\n      const item = await this.upstream.next();\n      if (item.done || this.predicate(item.value)) {\n        return item;\n      }\n      tf.dispose(item.value as {});\n    }\n  }\n}\n\nclass MapIterator<I, O> extends LazyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => O) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Map`;\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return {value: null, done: true};\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // That's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying\n    // any intermediate Tensors.  Here we are concerned only about the\n    // inputs.\n    const mapped = this.transform(item.value);\n    const outputTensors = tf.tensor_util.getTensorsInContainer(mapped as {});\n\n    // TODO(soergel) faster intersection\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n    return {value: mapped, done: false};\n  }\n}\n\nclass ErrorHandlingLazyIterator<T> extends LazyIterator<T> {\n  count = 0;\n  constructor(\n      protected upstream: LazyIterator<T>,\n      protected handler: (error: Error) => boolean) {\n    super();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> handleErrors`;\n  }\n\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    while (true) {\n      try {\n        return await this.upstream.next();\n      } catch (e) {\n        if (!this.handler(e)) {\n          return {value: null, done: true};\n        }\n        // If the handler returns true, loop and fetch the next upstream item.\n\n        // If the upstream iterator throws an endless stream of errors, and if\n        // the handler says to ignore them, then we loop forever here.  That is\n        // the correct behavior-- it's up to the handler to decide when to stop.\n      }\n    }\n  }\n}\n\nclass AsyncMapIterator<I, O> extends LazyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => Promise<O>) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> AsyncMap`;\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return {value: null, done: true};\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // That's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying\n    // any intermediate Tensors.  Here we are concerned only about the\n    // inputs.\n    const mapped = await this.transform(item.value);\n    const outputTensors = tf.tensor_util.getTensorsInContainer(mapped as {});\n\n    // TODO(soergel) faster intersection\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n    return {value: mapped, done: false};\n  }\n}\n\n// Iterators that maintain a queue of pending items\n// ============================================================================\n\n/**\n * A base class for transforming streams that operate by maintaining an\n * output queue of elements that are ready to return via next().  This is\n * commonly required when the transformation is 1-to-many:  A call to next()\n * may trigger a call to the underlying stream, which will produce many\n * mapped elements of this stream-- of which we need to return only one, so\n * we have to queue the rest.\n */\nexport abstract class OneToManyIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  protected outputQueue: RingBuffer<T>;\n\n  constructor() {\n    super();\n    this.outputQueue = new GrowingRingBuffer<T>();\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  /**\n   * Read one or more chunks from upstream and process them, possibly\n   * reading or writing a carryover, and adding processed items to the\n   * output queue.  Note it's possible that no items are added to the queue\n   * on a given pump() call, even if the upstream stream is not closed\n   * (e.g., because items are filtered).\n   *\n   * @return `true` if any action was taken, i.e. fetching items from the\n   *   upstream source OR adding items to the output queue.  `false` if the\n   *   upstream source is exhausted AND nothing was added to the queue\n   * (i.e., any remaining carryover).\n   */\n  protected abstract pump(): Promise<boolean>;\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    // Fetch so that the queue contains at least one item if possible.\n    // If the upstream source is exhausted, AND there are no items left in\n    // the output queue, then this stream is also exhausted.\n    while (this.outputQueue.length() === 0) {\n      // TODO(soergel): consider parallel reads.\n      if (!await this.pump()) {\n        return {value: null, done: true};\n      }\n    }\n    return {value: this.outputQueue.shift(), done: false};\n  }\n}\nclass FlatmapIterator<I, O> extends OneToManyIterator<O> {\n  constructor(\n      protected upstream: LazyIterator<I>,\n      protected transform: (value: I) => O[]) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Flatmap`;\n  }\n\n  async pump(): Promise<boolean> {\n    const item = await this.upstream.next();\n    if (item.done) {\n      return false;\n    }\n    const inputTensors = tf.tensor_util.getTensorsInContainer(item.value as {});\n    // Careful: the transform may mutate the item in place.\n    // that's why we have to remember the input Tensors above, and then\n    // below dispose only those that were not passed through to the output.\n    // Note too that the transform function is responsible for tidying any\n    // intermediate Tensors.  Here we are concerned only about the inputs.\n    const mappedArray = this.transform(item.value);\n    const outputTensors =\n        tf.tensor_util.getTensorsInContainer(mappedArray as {});\n    this.outputQueue.pushAll(mappedArray);\n\n    // TODO(soergel) faster intersection, and deduplicate outputTensors\n    // TODO(soergel) move to tf.disposeExcept(in, out)?\n    for (const t of inputTensors) {\n      if (!tf.tensor_util.isTensorInList(t, outputTensors)) {\n        t.dispose();\n      }\n    }\n\n    return true;\n  }\n}\n\n/**\n * Provides a `LazyIterator` that concatenates a stream of underlying\n * streams.\n *\n * Doing this in a concurrency-safe way requires some trickery.  In\n * particular, we want this stream to return the elements from the\n * underlying streams in the correct order according to when next() was\n * called, even if the resulting Promises resolve in a different order.\n */\nexport class ChainedIterator<T> extends LazyIterator<T> {\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>> = null;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  private iterator: LazyIterator<T> = null;\n  private moreIterators: LazyIterator<LazyIterator<T>>;\n\n  constructor(\n      iterators: LazyIterator<LazyIterator<T>>,\n      private readonly baseErrorHandler?: (e: Error) => boolean) {\n    super();\n    this.moreIterators = iterators;\n  }\n\n  summary() {\n    const upstreamSummaries = 'TODO: fill in upstream of chained summaries';\n    return `${upstreamSummaries} -> Chained`;\n  }\n\n  async next(): Promise<IteratorResult<T>> {\n    this.lastRead = this.readFromChain(this.lastRead);\n    return this.lastRead;\n  }\n\n  private async readFromChain(lastRead: Promise<IteratorResult<T>>):\n      Promise<IteratorResult<T>> {\n    // Must await on the previous read since the previous read may have advanced\n    // the stream of streams, from which we need to read.\n    // This is unfortunate since we can't parallelize reads. Which means\n    // prefetching of chained streams is a no-op.\n    // One solution is to prefetch immediately upstream of this.\n    await lastRead;\n    if (this.iterator == null) {\n      const iteratorResult = await this.moreIterators.next();\n      if (iteratorResult.done) {\n        // No more streams to stream from.\n        return {value: null, done: true};\n      }\n      this.iterator = iteratorResult.value;\n      if (this.baseErrorHandler != null) {\n        this.iterator = this.iterator.handleErrors(this.baseErrorHandler);\n      }\n    }\n    const itemResult = await this.iterator.next();\n    if (itemResult.done) {\n      this.iterator = null;\n      return this.readFromChain(lastRead);\n    }\n    return itemResult;\n  }\n}\n\nexport enum ZipMismatchMode {\n  FAIL,      // require zipped streams to have the same length\n  SHORTEST,  // terminate zip when the first stream is exhausted\n  LONGEST    // use nulls for exhausted streams; use up the longest stream.\n}\n\n/**\n * Provides a `LazyIterator` that zips together an array, dict, or nested\n * structure of `LazyIterator`s (and perhaps additional constants).\n *\n * The underlying streams must provide elements in a consistent order such\n * that they correspond.\n *\n * Typically, the underlying streams should have the same number of\n * elements. If they do not, the behavior is determined by the\n * `mismatchMode` argument.\n *\n * The nested structure of the `iterators` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Doing this in a concurrency-safe way requires some trickery.  In\n * particular, we want this stream to return the elements from the\n * underlying streams in the correct order according to when next() was\n * called, even if the resulting Promises resolve in a different order.\n *\n * @param iterators: An array or object containing LazyIterators at the\n * leaves.\n * @param mismatchMode: Determines what to do when one underlying iterator\n * is exhausted before the others.  `ZipMismatchMode.FAIL` (the default)\n * causes an error to be thrown in this case.  `ZipMismatchMode.SHORTEST`\n * causes the zipped iterator to terminate with the furst underlying\n * streams, so elements remaining on the longer streams are ignored.\n * `ZipMismatchMode.LONGEST` causes the zipped stream to continue, filling\n * in nulls for the exhausted streams, until all streams are exhausted.\n */\nclass ZipIterator<O extends tf.TensorContainer> extends LazyIterator<O> {\n  private count = 0;\n  private currentPromise: Promise<IteratorResult<O>> = null;\n\n  constructor(\n      protected readonly iterators: IteratorContainer,\n      protected readonly mismatchMode: ZipMismatchMode = ZipMismatchMode.FAIL) {\n    super();\n  }\n\n  summary() {\n    const upstreamSummaries = 'TODO: fill in upstream of zip summaries';\n    return `{${upstreamSummaries}} -> Zip`;\n  }\n\n  private async nextState(afterState: Promise<IteratorResult<O>>):\n      Promise<IteratorResult<O>> {\n    // This chaining ensures that the underlying next() are not even called\n    // before the previous ones have resolved.\n    await afterState;\n\n    // Collect underlying iterator \"done\" signals as a side effect in\n    // getNext()\n    let numIterators = 0;\n    let iteratorsDone = 0;\n\n    function getNext(container: IteratorContainer): DeepMapAsyncResult {\n      if (container instanceof LazyIterator) {\n        const result = container.next();\n        return {\n          value: result.then(x => {\n            numIterators++;\n            if (x.done) {\n              iteratorsDone++;\n            }\n            return x.value;\n          }),\n          recurse: false\n        };\n      } else {\n        return {value: null, recurse: true};\n      }\n    }\n\n    const mapped: O = await deepMapAndAwaitAll(this.iterators, getNext);\n\n    if (numIterators === iteratorsDone) {\n      // The streams have all ended.\n      return {value: null, done: true};\n    }\n    if (iteratorsDone > 0) {\n      switch (this.mismatchMode) {\n        case ZipMismatchMode.FAIL:\n          throw new Error(\n              'Zipped streams should have the same length. ' +\n              `Mismatched at element ${this.count}.`);\n        case ZipMismatchMode.SHORTEST:\n          return {value: null, done: true};\n        case ZipMismatchMode.LONGEST:\n        default:\n          // Continue.  The exhausted streams already produced value: null.\n      }\n    }\n\n    this.count++;\n    return {value: mapped, done: false};\n  }\n\n  async next(): Promise<IteratorResult<O>> {\n    this.currentPromise = this.nextState(this.currentPromise);\n    return this.currentPromise;\n  }\n}\n\n// Iterators that maintain a ring buffer of pending promises\n// ============================================================================\n\n/**\n * A stream that prefetches a given number of items from an upstream source,\n * returning them in FIFO order.\n *\n * Note this prefetches Promises, but makes no guarantees about when those\n * Promises resolve.\n */\nexport class PrefetchIterator<T> extends LazyIterator<T> {\n  protected buffer: RingBuffer<Promise<IteratorResult<T>>>;\n\n  constructor(\n      protected upstream: LazyIterator<T>, protected bufferSize: number) {\n    super();\n    this.buffer = new RingBuffer<Promise<IteratorResult<T>>>(bufferSize);\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Prefetch`;\n  }\n\n  /**\n   * Refill the prefetch buffer.  Returns only after the buffer is full, or\n   * the upstream source is exhausted.\n   */\n  protected refill() {\n    while (!this.buffer.isFull()) {\n      const v = this.upstream.next();\n      this.buffer.push(v);\n    }\n  }\n\n  next(): Promise<IteratorResult<T>> {\n    this.refill();\n    // This shift will never throw an error because the buffer is always\n    // full after a refill. If the stream is exhausted, the buffer will be\n    // full of Promises that will resolve to the end-of-stream signal.\n    return this.buffer.shift();\n  }\n}\n\n/**\n * A stream that performs a sliding-window random shuffle on an upstream\n * source. This is like a `PrefetchIterator` except that the items are\n * returned in randomized order.  Mixing naturally improves as the buffer\n * size increases.\n */\nexport class ShuffleIterator<T> extends PrefetchIterator<T> {\n  private readonly random: seedrandom.prng;\n\n  // Strict Promise execution order:\n  // a next() call may not even begin until the previous one completes.\n  private lastRead: Promise<IteratorResult<T>>;\n\n  // Local state that should not be clobbered by out-of-order execution.\n  private upstreamExhausted = false;\n\n  constructor(\n    protected override upstream: LazyIterator<T>, protected windowSize: number,\n      seed?: string) {\n    super(upstream, windowSize);\n    this.random = seedrandom.alea(seed || tf.util.now().toString());\n    this.lastRead = Promise.resolve({value: null, done: false});\n  }\n\n  override async next(): Promise<IteratorResult<T>> {\n    // This sets this.lastRead to a new Promise right away, as opposed to\n    // saying `await this.lastRead; this.lastRead = this.serialNext();` which\n    // would not work because this.nextRead would be updated only after the\n    // promise resolves.\n    this.lastRead = this.lastRead.then(() => this.serialNext());\n    return this.lastRead;\n  }\n\n  private randomInt(max: number) {\n    return Math.floor(this.random() * max);\n  }\n\n  protected chooseIndex(): number {\n    return this.randomInt(this.buffer.length());\n  }\n\n  async serialNext(): Promise<IteratorResult<T>> {\n    // TODO(soergel): consider performance\n    if (!this.upstreamExhausted) {\n      this.refill();\n    }\n    while (!this.buffer.isEmpty()) {\n      const chosenIndex = this.chooseIndex();\n      const result = await this.buffer.shuffleExcise(chosenIndex);\n      if (result.done) {\n        this.upstreamExhausted = true;\n      } else {\n        this.refill();\n        return result;\n      }\n    }\n    return {value: null, done: true};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport * as tf from '@tensorflow/tfjs-core';\nimport {TensorContainer, TensorLike} from '@tensorflow/tfjs-core';\nimport * as seedrandom from 'seedrandom';\n\nimport {iteratorFromConcatenated, iteratorFromFunction, iteratorFromItems, iteratorFromZipped, LazyIterator, ZipMismatchMode} from './iterators/lazy_iterator';\nimport {Container} from './types';\nimport {canTensorify, deepMapAndAwaitAll, DeepMapResult, isIterable} from './util/deep_map';\n\n/**\n * A nested structure of Datasets, used as the input to zip().\n */\nexport type DatasetContainer = Container<Dataset<TensorContainer>>;\n\n// TODO(soergel): consider vectorized operations within the pipeline.\n\n/**\n * Represents a potentially large list of independent data elements (typically\n * 'samples' or 'examples').\n *\n * A 'data example' may be a primitive, an array, a map from string keys to\n * values, or any nested structure of these.\n *\n * A `Dataset` represents an ordered collection of elements, together with a\n * chain of transformations to be performed on those elements. Each\n * transformation is a method of `Dataset` that returns another `Dataset`, so\n * these may be chained, e.g.\n * `const processedDataset = rawDataset.filter(...).map(...).batch(...)`.\n *\n * Data loading and transformation is done in a lazy, streaming fashion.  The\n * dataset may be iterated over multiple times; each iteration starts the data\n * loading anew and recapitulates the transformations.\n *\n * A `Dataset` is typically processed as a stream of unbatched examples -- i.e.,\n * its transformations are applied one example at a time. Batching produces a\n * new `Dataset` where each element is a batch. Batching should usually come\n * last in a pipeline, because data transformations are easier to express on a\n * per-example basis than on a per-batch basis.\n *\n * The following code examples are calling `await dataset.forEachAsync(...)` to\n * iterate once over the entire dataset in order to print out the data.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport abstract class Dataset<T extends tf.TensorContainer> {\n  /*\n   * Provide a new stream of elements.  Note this will also start new streams\n   * from any underlying `Dataset`s.\n   *\n   * CAUTION: Any Tensors contained within the elements returned from\n   * this stream *must* be manually disposed to avoid a GPU memory leak.\n   * The tf.tidy() approach cannot be used in an asynchronous context.\n   */\n  abstract iterator(): Promise<LazyIterator<T>>;\n\n  readonly size: number = null;\n\n  // TODO(soergel): Make Datasets report whether repeated iterator() calls\n  // produce the same result (e.g., reading from a file) or different results\n  // (e.g., from the webcam).  Currently we don't make this distinction but it\n  // could be important for the user to know.\n  // abstract isDeterministic(): boolean;\n\n  /**\n   * Groups elements into batches.\n   *\n   * It is assumed that each of the incoming dataset elements has the same\n   * structure -- i.e. the same set of keys at each location in an object\n   * hierarchy.  For each key, the resulting `Dataset` provides a batched\n   * element collecting all of the incoming values for that key.\n   *\n   *  * Incoming primitives are grouped into a 1-D Tensor.\n   *  * Incoming Tensors are grouped into a new Tensor where the 0th axis is\n   *    the batch dimension.\n   *  * Incoming arrays are converted to Tensor and then batched.\n   *  * A nested array is interpreted as an n-D Tensor, so the batched result\n   *    has n+1 dimensions.\n   *  * An array that cannot be converted to Tensor produces an error.\n   *\n   * If an array should not be batched as a unit, it should first be converted\n   * to an object with integer keys.\n   *\n   * Here are a few examples:\n   *\n   * Batch a dataset of numbers:\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8]).batch(4);\n   * await a.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of arrays:\n   * ```js\n   * const b = tf.data.array([[1], [2], [3], [4], [5], [6], [7], [8]]).batch(4);\n   * await b.forEachAsync(e => e.print());\n   * ```\n   *\n   * Batch a dataset of objects:\n   * ```js\n   * const c = tf.data.array([{a: 1, b: 11}, {a: 2, b: 12}, {a: 3, b: 13},\n   *   {a: 4, b: 14}, {a: 5, b: 15}, {a: 6, b: 16}, {a: 7, b: 17},\n   *   {a: 8, b: 18}]).batch(4);\n   * await c.forEachAsync(e => {\n   *   console.log('{');\n   *   for(var key in e) {\n   *     console.log(key+':');\n   *     e[key].print();\n   *   }\n   *   console.log('}');\n   * })\n   * ```\n   *\n   * @param batchSize The number of elements desired per batch.\n   * @param smallLastBatch Whether to emit the final batch when it has fewer\n   *   than batchSize elements. Default true.\n   * @returns A `Dataset`, from which a stream of batches can be obtained.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  batch(batchSize: number, smallLastBatch = true): Dataset<tf.TensorContainer> {\n    const base = this;\n    tf.util.assert(\n        batchSize > 0, () => `batchSize needs to be positive, but it is\n      ${batchSize}`);\n    let size;\n    if (this.size === Infinity || this.size == null) {\n      // If the size of this dataset is infinity or null, the new size keeps the\n      // same.\n      size = this.size;\n    } else if (smallLastBatch) {\n      // If the size of this dataset is known and include small last batch, the\n      // new size is full batch count plus last batch.\n      size = Math.ceil(this.size / batchSize);\n    } else {\n      // If the size of this dataset is known and not include small last batch,\n      // the new size is full batch count.\n      size = Math.floor(this.size / batchSize);\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator())\n          .columnMajorBatch(batchSize, smallLastBatch, deepBatchConcat);\n    }, size);\n  }\n\n  /**\n   * Concatenates this `Dataset` with another.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * const b = tf.data.array([4, 5, 6]);\n   * const c = a.concatenate(b);\n   * await c.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param dataset A `Dataset` to be concatenated onto this one.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  concatenate(dataset: Dataset<T>): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity || dataset.size === Infinity) {\n      // If the size of any of these two dataset is infinity, new size is\n      // infinity.\n      size = Infinity;\n    } else if (this.size != null && dataset.size != null) {\n      // If the size of both datasets are known and not infinity, new size is\n      // sum the size of these two datasets.\n      size = this.size + dataset.size;\n    } else {\n      // If neither of these two datasets has infinite size and any of these two\n      // datasets' size is null, the new size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () =>\n            (await base.iterator()).concatenate(await dataset.iterator()),\n        size);\n  }\n\n  /**\n   * Filters this dataset according to `predicate`.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])\n   *   .filter(x => x%2 === 0);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param predicate A function mapping a dataset element to a boolean or a\n   * `Promise` for one.\n   *\n   * @returns A `Dataset` of elements for which the predicate was true.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  filter(predicate: (value: T) => boolean): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size === Infinity) {\n      // If the size of this dataset is infinity, new size is infinity\n      size = Infinity;\n    } else {\n      // If this dataset has limited elements, new size is null because it might\n      // exhausted randomly.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).filter(x => tf.tidy(() => predicate(x)));\n    }, size);\n  }\n\n  /**\n   * Apply a function to every element of the dataset.\n   *\n   * After the function is applied to a dataset element, any Tensors contained\n   * within that element are disposed.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param f A function to apply to each dataset element.\n   * @returns A `Promise` that resolves after all elements have been processed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async forEachAsync(f: (input: T) => void): Promise<void> {\n    return (await this.iterator()).forEachAsync(f);\n  }\n\n  /**\n   * Maps this dataset through a 1-to-1 transform.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).map(x => x*x);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a transformed\n   *   dataset element.\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  map<O extends tf.TensorContainer>(transform: (value: T) => O): Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).map(x => tf.tidy(() => transform(x)));\n    }, this.size);\n  }\n\n  /**\n   * Maps this dataset through an async 1-to-1 transform.\n   *\n   * ```js\n   * const a =\n   *  tf.data.array([1, 2, 3]).mapAsync(x => new Promise(function(resolve){\n   *    setTimeout(() => {\n   *      resolve(x * x);\n   *    }, Math.random()*1000 + 500);\n   *  }));\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @param transform A function mapping a dataset element to a `Promise` for a\n   *   transformed dataset element.  This transform is responsible for disposing\n   *   any intermediate `Tensor`s, i.e. by wrapping its computation in\n   *   `tf.tidy()`; that cannot be automated here (as it is in the synchronous\n   *   `map()` case).\n   *\n   * @returns A `Dataset` of transformed elements.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  mapAsync<O extends tf.TensorContainer>(transform: (value: T) => Promise<O>):\n      Dataset<O> {\n    const base = this;\n    return datasetFromIteratorFn(async () => {\n      return (await base.iterator()).mapAsync(transform);\n    }, this.size);\n  }\n\n  /**\n   *  Creates a `Dataset` that prefetches elements from this dataset.\n   *\n   * @param bufferSize: An integer specifying the number of elements to be\n   *   prefetched.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  prefetch(bufferSize: number): Dataset<T> {\n    if (bufferSize == null) {\n      throw new RangeError(\n          '`Dataset.prefetch()` requires bufferSize to be specified.');\n    }\n\n    const base = this;\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).prefetch(bufferSize), this.size);\n  }\n\n  /**\n   * Repeats this dataset `count` times.\n   *\n   * NOTE: If this dataset is a function of global state (e.g. a random number\n   * generator), then different repetitions may produce different elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3]).repeat(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: (Optional) An integer, representing the number of times\n   *   the dataset should be repeated. The default behavior (if `count` is\n   *   `undefined` or negative) is for the dataset be repeated indefinitely.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  repeat(count?: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count > 0) {\n      // If this dataset has size and count is positive, new size is current\n      // size multiply count. This also covers the case that current size is\n      // infinity.\n      size = this.size * count;\n    } else if (count === 0) {\n      // If count is 0, new size is 0.\n      size = 0;\n    } else if (this.size != null && (count === undefined || count < 0)) {\n      // If this dataset has size and count is undefined or negative, the\n      // dataset will be repeated indefinitely and new size is infinity.\n      size = Infinity;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(async () => {\n      const iteratorIterator = iteratorFromFunction(\n          async () => ({value: await base.iterator(), done: false}));\n      return iteratorFromConcatenated(iteratorIterator.take(count));\n    }, size);\n  }\n\n  /**\n   * Creates a `Dataset` that skips `count` initial elements from this dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).skip(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be skipped\n   *   to form the new dataset.  If `count` is greater than the size of this\n   *   dataset, the new dataset will contain no elements.  If `count`\n   *   is `undefined` or negative, skips the entire dataset.\n   *\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  skip(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && count >= 0 && this.size >= count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is current size minus skipped size.This also covers the case that\n      // current size is infinity.\n      size = this.size - count;\n    } else if (\n        this.size != null &&\n        (this.size < count || count === undefined || count < 0)) {\n      // If the size of this dataset is smaller than count, or count is\n      // undefined or negative, skips the entire dataset and the new size is 0.\n      size = 0;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).skip(count), size);\n  }\n\n  // TODO(soergel): deep sharded shuffle, where supported\n\n  static readonly MAX_BUFFER_SIZE = 10000;\n\n  /**\n   * Pseudorandomly shuffles the elements of this dataset. This is done in a\n   * streaming manner, by sampling from a given number of prefetched elements.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).shuffle(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param bufferSize: An integer specifying the number of elements from this\n   *   dataset from which the new dataset will sample.\n   * @param seed: (Optional) An integer specifying the random seed that will\n   *   be used to create the distribution.\n   * @param reshuffleEachIteration: (Optional) A boolean, which if true\n   *   indicates that the dataset should be pseudorandomly reshuffled each time\n   *   it is iterated over. If false, elements will be returned in the same\n   *   shuffled order on each iteration. (Defaults to `true`.)\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  shuffle(bufferSize: number, seed?: string, reshuffleEachIteration = true):\n      Dataset<T> {\n    if (bufferSize == null || bufferSize < 0) {\n      if (this.size == null) {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.');\n      } else {\n        throw new RangeError(\n            '`Dataset.shuffle()` requires bufferSize to be specified.  ' +\n            'If your data fits in main memory (for regular JS objects), ' +\n            'and/or GPU memory (for `tf.Tensor`s), consider setting ' +\n            `bufferSize to the dataset size (${this.size} elements)`);\n      }\n    }\n    const base = this;\n    const random = seedrandom.alea(seed || tf.util.now().toString());\n    return datasetFromIteratorFn(async () => {\n      let seed2 = random.int32();\n      if (reshuffleEachIteration) {\n        seed2 += random.int32();\n      }\n      return (await base.iterator()).shuffle(bufferSize, seed2.toString());\n    }, this.size);\n  }\n\n  /**\n   * Creates a `Dataset` with at most `count` initial elements from this\n   * dataset.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]).take(3);\n   * await a.forEachAsync(e => console.log(e));\n   * ```\n   *\n   * @param count: The number of elements of this dataset that should be taken\n   *   to form the new dataset.  If `count` is `undefined` or negative, or if\n   *   `count` is greater than the size of this dataset, the new dataset will\n   *   contain all elements of this dataset.\n   * @returns A `Dataset`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  take(count: number): Dataset<T> {\n    const base = this;\n    let size;\n    if (this.size != null && this.size > count) {\n      // If the size of this dataset is greater than count, the new dataset's\n      // size is count.\n      size = count;\n    } else if (this.size != null && this.size <= count) {\n      // If the size of this dataset is equal or smaller than count, the new\n      // dataset's size is the size of this dataset.\n      size = this.size;\n    } else {\n      // If the size of this dataset is null, the new dataset's size is null.\n      size = null;\n    }\n    return datasetFromIteratorFn(\n        async () => (await base.iterator()).take(count), size);\n  }\n\n  /**\n   * Collect all elements of this dataset into an array.\n   *\n   * Obviously this will succeed only for small datasets that fit in memory.\n   * Useful for testing and generally should be avoided if possible.\n   *\n   * ```js\n   * const a = tf.data.array([1, 2, 3, 4, 5, 6]);\n   * console.log(await a.toArray());\n   * ```\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async toArray() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArray();\n  }\n\n  /**\n   * Collect all elements of this dataset into an array with prefetching 100\n   * elements. This is useful for testing, because the prefetch changes the\n   * order in which the Promises are resolved along the processing pipeline.\n   * This may help expose bugs where results are dependent on the order of\n   * Promise resolution rather than on the logical order of the stream (i.e.,\n   * due to hidden mutable state).\n   *\n   * @returns A Promise for an array of elements, which will resolve\n   *   when a new stream has been obtained and fully consumed.\n   */\n  async toArrayForTest() {\n    if (this.size === Infinity) {\n      throw new Error('Can not convert infinite data stream to array.');\n    }\n    return (await this.iterator()).toArrayForTest();\n  }\n}\n\n/**\n * Create a `Dataset` defined by a provided iterator() function.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const iter = tf.data.iteratorFromFunction(func);\n * const ds = tf.data.datasetFromIteratorFn(iter);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n */\nexport function datasetFromIteratorFn<T extends tf.TensorContainer>(\n    iteratorFn: () => Promise<LazyIterator<T>>,\n    size: number = null): Dataset<T> {\n  return new class extends Dataset<T> {\n    override size = size;\n\n    /*\n     * Provide a new stream of elements.  Note this will also start new streams\n     * from any underlying `Dataset`s.\n     */\n    async iterator(): Promise<LazyIterator<T>> {\n      return iteratorFn();\n    }\n  }\n  ();\n}\n\n/**\n * Create a `Dataset` from an array of elements.\n *\n * Create a Dataset from an array of objects:\n * ```js\n * const a = tf.data.array([{'item': 1}, {'item': 2}, {'item': 3}]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n *\n * Create a Dataset from an array of numbers:\n * ```js\n * const a = tf.data.array([4, 5, 6]);\n * await a.forEachAsync(e => console.log(e));\n * ```\n * @param items An array of elements that will be parsed as items in a dataset.\n *\n * @doc {heading: 'Data', subheading: 'Creation', namespace: 'data'}\n */\nexport function array<T extends tf.TensorContainer>(items: T[]): Dataset<T> {\n  return datasetFromIteratorFn(\n      async () => iteratorFromItems(items), items.length);\n}\n\n/**\n * Create a `Dataset` by zipping together an array, dict, or nested\n * structure of `Dataset`s (and perhaps additional constants).\n * The underlying datasets must provide elements in a consistent order such that\n * they correspond.\n *\n * The number of elements in the resulting dataset is the same as the size of\n * the smallest dataset in datasets.\n *\n * The nested structure of the `datasets` argument determines the\n * structure of elements in the resulting iterator.\n *\n * Note this means that, given an array of two datasets that produce dict\n * elements, the result is a dataset that produces elements that are arrays\n * of two dicts:\n *\n * Zip an array of datasets:\n * ```js\n * console.log('Zip two datasets of objects:');\n * const ds1 = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const ds2 = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const ds3 = tf.data.zip([ds1, ds2]);\n * await ds3.forEachAsync(e => console.log(JSON.stringify(e)));\n *\n * // If the goal is to merge the dicts in order to produce elements like\n * // {a: ..., b: ...}, this requires a second step such as:\n * console.log('Merge the objects:');\n * const ds4 = ds3.map(x => {return {a: x[0].a, b: x[1].b}});\n * await ds4.forEachAsync(e => console.log(e));\n * ```\n *\n * Zip a dict of datasets:\n * ```js\n * const a = tf.data.array([{a: 1}, {a: 2}, {a: 3}]);\n * const b = tf.data.array([{b: 4}, {b: 5}, {b: 6}]);\n * const c = tf.data.zip({c: a, d: b});\n * await c.forEachAsync(e => console.log(JSON.stringify(e)));\n * ```\n *\n * @doc {heading: 'Data', subheading: 'Operations', namespace: 'data'}\n */\nexport function zip<O extends tf.TensorContainer>(datasets: DatasetContainer):\n    Dataset<O> {\n  // manually type-check the argument for JS users\n  if (!isIterable(datasets)) {\n    throw new Error('The argument to zip() must be an object or array.');\n  }\n  let size;\n  if (Array.isArray(datasets)) {\n    for (let i = 0; i < datasets.length; i++) {\n      size = size == null ? (datasets[i] as Dataset<O>).size :\n                            Math.min(size, (datasets[i] as Dataset<O>).size);\n    }\n  } else if (datasets instanceof Object) {\n    for (const ds in datasets) {\n      size = size == null ? (datasets[ds] as Dataset<O>).size :\n                            Math.min(size, (datasets[ds] as Dataset<O>).size);\n    }\n  }\n  return datasetFromIteratorFn<O>(async () => {\n    const streams = await deepMapAndAwaitAll(datasets, d => {\n      if (d instanceof Dataset) {\n        return {value: d.iterator(), recurse: false};\n      } else if (isIterable(d)) {\n        return {value: null, recurse: true};\n      } else {\n        throw new Error(\n            'Leaves of the structure passed to zip() must be Datasets, ' +\n            'not primitives.');\n      }\n    });\n    return iteratorFromZipped<O>(streams, ZipMismatchMode.SHORTEST);\n  }, size);\n}\n\n/**\n * A zip function for use with deepZip, passed via the columnMajorBatch call.\n *\n * Accepts an array of identically-structured nested elements and either batches\n * them (if they are primitives, numeric arrays, or Tensors) or requests\n * recursion (if not).\n */\n// tslint:disable-next-line:no-any\nfunction deepBatchConcat(rows: any[]): DeepMapResult {\n  if (rows === null) {\n    return null;\n  }\n\n  // use the first item to decide whether to recurse or batch here.\n  const exampleRow = rows[0];\n\n  if (canTensorify(exampleRow)) {\n    // rows is an array of primitives, Tensors, or arrays.  Batch them.\n    const value = batchConcat(rows);\n    return {value, recurse: false};\n  }\n\n  // the example row is an object, so recurse into it.\n  return {value: null, recurse: true};\n}\n\n/**\n * Assembles a list of same-shaped numbers, number arrays, or Tensors\n * into a single new Tensor where axis 0 is the batch dimension.\n */\nfunction batchConcat<T extends(TensorLike | tf.Tensor)>(arrays: T[]):\n    tf.Tensor {\n  if (arrays.length === 0) {\n    // We can't return an empty Tensor because we don't know the element shape.\n    throw new Error('Can\\'t make a batch of zero elements.');\n  }\n\n  if (arrays[0] instanceof tf.Tensor) {\n    // Input is an array of Tensors\n    return tf.stack(arrays as tf.Tensor[]);\n  } else {\n    // Input is a possibly-nested array of numbers.\n    return tf.tensor(arrays as TensorLike);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {Dataset} from '../dataset';\nimport {DataSource} from '../datasource';\nimport {LazyIterator} from '../iterators/lazy_iterator';\n\n/**\n * Represents a potentially large collection of text lines.\n *\n * The results are not batched.\n */\nexport class TextLineDataset extends Dataset<string> {\n  /**\n   * Create a `TextLineDataset`.\n   *\n   * @param input A `DataSource` providing a chunked, UTF8-encoded byte stream.\n   */\n  constructor(protected readonly input: DataSource) {\n    super();\n  }\n\n  async iterator(): Promise<LazyIterator<string>> {\n    const inputIterator = await this.input.iterator();\n    const utf8Iterator = inputIterator.decodeUTF8();\n    const lineIterator = utf8Iterator.split('\\n').map(line => {\n      // Windows/DOS format text file has extra line breaker at the end of line.\n      if (line.endsWith('\\r')) {\n        line = line.slice(0, -1);\n      }\n      return line;\n    });\n    return lineIterator;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {TensorContainer, util} from '@tensorflow/tfjs-core';\nimport {Dataset} from '../dataset';\nimport {DataSource} from '../datasource';\nimport {LazyIterator} from '../iterators/lazy_iterator';\nimport {ColumnConfig, CSVConfig} from '../types';\nimport {TextLineDataset} from './text_line_dataset';\n\nconst CODE_QUOTE = '\"';\nconst STATE_OUT = Symbol('out');\nconst STATE_FIELD = Symbol('field');\nconst STATE_QUOTE = Symbol('quote');\nconst STATE_QUOTE_AFTER_QUOTE = Symbol('quoteafterquote');\nconst STATE_WITHIN_QUOTE_IN_QUOTE = Symbol('quoteinquote');\n\n/**\n * Represents a potentially large collection of delimited text records.\n *\n * The produced `TensorContainer`s each contain one key-value pair for\n * every column of the table.  When a field is empty in the incoming data, the\n * resulting value is `undefined`, or throw error if it is required.  Values\n * that can be parsed as numbers are emitted as type `number`, other values\n * are parsed as `string`.\n *\n * The results are not batched.\n *\n * @doc {heading: 'Data', subheading: 'Classes', namespace: 'data'}\n */\nexport class CSVDataset extends Dataset<TensorContainer> {\n  base: TextLineDataset;\n  private hasHeader = true;\n  private fullColumnNames: string[] = null;\n  private columnNamesValidated = false;\n  private columnConfigs: {[key: string]: ColumnConfig} = null;\n  private configuredColumnsOnly = false;\n  private delimiter = ',';\n  private delimWhitespace = false;\n\n  /**\n   * Returns column names of the csv dataset. If `configuredColumnsOnly` is\n   * true, return column names in `columnConfigs`. If `configuredColumnsOnly` is\n   * false and `columnNames` is provided, `columnNames`. If\n   * `configuredColumnsOnly` is false and `columnNames` is not provided, return\n   * all column names parsed from the csv file. For example usage please go to\n   * `tf.data.csv`.\n   *\n   * @doc {heading: 'Data', subheading: 'Classes'}\n   */\n  async columnNames() {\n    if (!this.columnNamesValidated) {\n      await this.setColumnNames();\n    }\n    return this.configuredColumnsOnly ? Object.keys(this.columnConfigs) :\n                                        this.fullColumnNames;\n  }\n\n  /* 1) If `columnNames` is provided as string[], use this string[] as output\n   * keys in corresponding order. The length must match the number of inferred\n   * columns if `hasHeader` is true .\n   * 2) If `columnNames` is not provided, parse header line as `columnNames` if\n   * hasHeader is true. If `hasHeader` is false, throw an error.\n   * 3) If `columnConfigs` is provided, all the keys in `columnConfigs` must\n   * exist in parsed `columnNames`.\n   */\n  private async setColumnNames() {\n    const columnNamesFromFile = await this.maybeReadHeaderLine();\n    if (!this.fullColumnNames && !columnNamesFromFile) {\n      // Throw an error if columnNames is not provided and no header line.\n      throw new Error(\n          'Column names must be provided if there is no header line.');\n    } else if (this.fullColumnNames && columnNamesFromFile) {\n      // Check provided columnNames match header line.\n      util.assert(\n          columnNamesFromFile.length === this.fullColumnNames.length,\n          () => 'The length of provided columnNames (' +\n              this.fullColumnNames.length.toString() +\n              ') does not match the length of the header line read from ' +\n              'file (' + columnNamesFromFile.length.toString() + ').');\n    }\n    if (!this.fullColumnNames) {\n      this.fullColumnNames = columnNamesFromFile;\n    }\n    // Check if there are duplicate column names.\n    const counts: {[key: string]: number} = this.fullColumnNames.reduce(\n        (countAcc: {[key: string]: number}, name) => {\n          countAcc[name] = (countAcc[name] + 1) || 1;\n          return countAcc;\n        },\n        {});\n    const duplicateNames =\n        Object.keys(counts).filter((name) => (counts[name] > 1));\n    util.assert(\n        duplicateNames.length === 0,\n        () => 'Duplicate column names found: ' + duplicateNames.toString());\n    // Check if keys in columnConfigs match columnNames.\n    if (this.columnConfigs) {\n      for (const key of Object.keys(this.columnConfigs)) {\n        const index = this.fullColumnNames.indexOf(key);\n        if (index === -1) {\n          throw new Error(\n              'The key \"' + key +\n              '\" provided in columnConfigs does not match any of the column ' +\n              'names (' + this.fullColumnNames.toString() + ').');\n        }\n      }\n    }\n    this.columnNamesValidated = true;\n  }\n\n  private async maybeReadHeaderLine() {\n    if (this.hasHeader) {\n      const iter = await this.base.iterator();\n      const firstElement = await iter.next();\n      if (firstElement.done) {\n        throw new Error('No data was found for CSV parsing.');\n      }\n      const firstLine: string = firstElement.value;\n      const headers = this.parseRow(firstLine, false);\n      return headers;\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Create a `CSVDataset`.\n   *\n   * @param input A `DataSource` providing a chunked, UTF8-encoded byte stream.\n   * @param csvConfig (Optional) A CSVConfig object that contains configurations\n   *     of reading and decoding from CSV file(s).\n   *\n   *     hasHeader: (Optional) A boolean value that indicates whether the first\n   *     row of provided CSV file is a header line with column names, and should\n   *     not be included in the data. Defaults to `true`.\n   *\n   *     columnNames: (Optional) A list of strings that corresponds to\n   *     the CSV column names, in order. If provided, it ignores the column\n   *     names inferred from the header row. If not provided, infers the column\n   *     names from the first row of the records. If hasHeader is false and\n   *     columnNames is not provided, this method throws an error.\n   *\n   *     columnConfigs: (Optional) A dictionary whose key is column names, value\n   *     is an object stating if this column is required, column's data type,\n   *     default value, and if this column is label. If provided, keys must\n   *     correspond to names provided in columnNames or inferred from the file\n   *     header lines. If isLabel is true any column, returns an array of two\n   *     items: the first item is a dict of features key/value pairs, the second\n   *     item is a dict of labels key/value pairs. If no feature is marked as\n   *     label, returns a dict of features only.\n   *\n   *     configuredColumnsOnly (Optional) If true, only columns provided in\n   *     columnConfigs will be parsed and provided during iteration.\n   *\n   *     delimiter (Optional) The string used to parse each line of the input\n   *     file. Defaults to `,`.\n   */\n  constructor(protected readonly input: DataSource, csvConfig?: CSVConfig) {\n    super();\n    this.base = new TextLineDataset(input);\n    if (!csvConfig) {\n      csvConfig = {};\n    }\n    this.hasHeader = csvConfig.hasHeader === false ? false : true;\n    this.fullColumnNames = csvConfig.columnNames;\n    this.columnConfigs = csvConfig.columnConfigs;\n    this.configuredColumnsOnly = csvConfig.configuredColumnsOnly;\n    if (csvConfig.delimWhitespace) {\n      util.assert(\n          csvConfig.delimiter == null,\n          () =>\n              'Delimiter should not be provided when delimWhitespace is true.');\n      this.delimWhitespace = true;\n      this.delimiter = ' ';\n    } else {\n      this.delimiter = csvConfig.delimiter ? csvConfig.delimiter : ',';\n    }\n  }\n\n  async iterator(): Promise<LazyIterator<TensorContainer>> {\n    if (!this.columnNamesValidated) {\n      await this.setColumnNames();\n    }\n    let lines = await this.base.iterator();\n    if (this.hasHeader) {\n      // We previously read the first line to get the columnNames.\n      // Now that we're providing data, skip it.\n      lines = lines.skip(1);\n    }\n    return lines.map(x => this.makeDataElement(x));\n  }\n\n  makeDataElement(line: string): TensorContainer {\n    const values = this.parseRow(line);\n    const features: {[key: string]: TensorContainer} = {};\n    const labels: {[key: string]: TensorContainer} = {};\n\n    for (let i = 0; i < this.fullColumnNames.length; i++) {\n      const key = this.fullColumnNames[i];\n      const config = this.columnConfigs ? this.columnConfigs[key] : null;\n      if (this.configuredColumnsOnly && !config) {\n        // This column is not selected.\n        continue;\n      } else {\n        const value = values[i];\n        let parsedValue = null;\n        if (value === '') {\n          // If default value is provided, use it. If default value is not\n          // provided, set as undefined.\n          if (config && config.default !== undefined) {\n            parsedValue = config.default;\n          } else if (config && (config.required || config.isLabel)) {\n            throw new Error(\n                `Required column ${key} is empty in this line: ${line}`);\n          } else {\n            parsedValue = undefined;\n          }\n        } else {\n          // A value is present, so parse it based on type\n          const valueAsNum = Number(value);\n          if (isNaN(valueAsNum)) {\n            // The value is a string and this column is declared as boolean\n            // in config, parse it as boolean.\n            if (config && config.dtype === 'bool') {\n              parsedValue = this.getBoolean(value);\n            } else {\n              // Set value as string\n              parsedValue = value;\n            }\n          } else if (!config || !config.dtype) {\n            // If this value is a number and no type config is provided, return\n            // it as number.\n            parsedValue = valueAsNum;\n          } else {\n            // If this value is a number and data type is provided, parse it\n            // according to provided data type.\n            switch (config.dtype) {\n              case 'float32':\n                parsedValue = valueAsNum;\n                break;\n              case 'int32':\n                parsedValue = Math.floor(valueAsNum);\n                break;\n              case 'bool':\n                parsedValue = this.getBoolean(value);\n                break;\n              default:\n                parsedValue = valueAsNum;\n            }\n          }\n        }\n        // Check if this column is label.\n        (config && config.isLabel) ? labels[key] = parsedValue :\n                                     features[key] = parsedValue;\n      }\n    }\n    // If label exists, return an object of features and labels as {xs:features,\n    // ys:labels}, otherwise return features only.\n    if (Object.keys(labels).length === 0) {\n      return features;\n\n    } else {\n      return {xs: features, ys: labels};\n    }\n  }\n\n  private getBoolean(value: string): number {\n    if (value === '1' || value.toLowerCase() === 'true') {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n\n  // adapted from https://beta.observablehq.com/@mbostock/streaming-csv\n  private parseRow(line: string, validateElementCount = true): string[] {\n    const result: string[] = [];\n    let readOffset = 0;\n    const readLength = line.length;\n    let currentState = STATE_OUT;\n    // Goes through the line to parse quote.\n    for (let i = 0; i < readLength; i++) {\n      switch (currentState) {\n        // Before enter a new field\n        case STATE_OUT:\n          switch (line.charAt(i)) {\n            // Enter a quoted field\n            case CODE_QUOTE:\n              readOffset = i + 1;\n              currentState = STATE_QUOTE;\n              break;\n            // Read an empty field\n            case this.delimiter:\n              readOffset = i + 1;\n              // If delimiter is white space and configured to collapse\n              // multiple white spaces, ignore this white space.\n              if (this.delimiter === ' ' && this.delimWhitespace) {\n                break;\n              }\n              result.push('');\n              currentState = STATE_OUT;\n              break;\n            // Enter an unquoted field\n            default:\n              currentState = STATE_FIELD;\n              readOffset = i;\n              break;\n          }\n          break;\n        // In an unquoted field\n        case STATE_FIELD:\n          switch (line.charAt(i)) {\n            // Exit an unquoted field, add it to result\n            case this.delimiter:\n              result.push(line.substring(readOffset, i));\n              currentState = STATE_OUT;\n              readOffset = i + 1;\n              break;\n            default:\n          }\n          break;\n        // In a quoted field\n        case STATE_QUOTE:\n          switch (line.charAt(i)) {\n            // Read a quote after a quote\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE_AFTER_QUOTE;\n              break;\n            default:\n          }\n          break;\n        // This state means it's right after a second quote in a field\n        case STATE_QUOTE_AFTER_QUOTE:\n          switch (line.charAt(i)) {\n            // Finished a quoted field\n            case this.delimiter:\n              result.push(line.substring(readOffset, i - 1));\n              currentState = STATE_OUT;\n              readOffset = i + 1;\n              break;\n            // Finished a quoted part in a quoted field\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE;\n              break;\n            // In a quoted part in a quoted field\n            default:\n              currentState = STATE_WITHIN_QUOTE_IN_QUOTE;\n              break;\n          }\n          break;\n        case STATE_WITHIN_QUOTE_IN_QUOTE:\n          switch (line.charAt(i)) {\n            // Exit a quoted part in a quoted field\n            case CODE_QUOTE:\n              currentState = STATE_QUOTE;\n              break;\n            default:\n          }\n          break;\n        default:\n      }\n    }\n    // Adds last item based on if it is quoted.\n    if (currentState === STATE_QUOTE_AFTER_QUOTE) {\n      result.push(line.substring(readOffset, readLength - 1));\n    } else {\n      result.push(line.substring(readOffset));\n    }\n    // Check if each row has the same number of elements as column names.\n    if (validateElementCount && result.length !== this.fullColumnNames.length) {\n      throw new Error(`Invalid row in csv file. Should have ${\n          this.fullColumnNames.length} elements in a row, but got ${result}`);\n    }\n    return result;\n  }\n}\n\n// TODO(soergel): add more basic datasets for parity with tf.data\n// tf.data.FixedLengthRecordDataset()\n// tf.data.TFRecordDataset()\n", "/**\n * @license\n * Copyright 2019 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env, Tensor, tensor, Tensor2D, Tensor3D, TensorContainer, util} from '@tensorflow/tfjs-core';\nimport {MicrophoneConfig} from '../types';\nimport {LazyIterator} from './lazy_iterator';\n\n/**\n * Provide a stream of tensors from microphone audio stream. The tensors are\n * representing audio data as frequency-domain spectrogram generated with\n * browser's native FFT. Tensors representing time-domain waveform is available\n * based on configuration. Only works in browser environment.\n */\nexport class MicrophoneIterator extends LazyIterator<TensorContainer> {\n  private isClosed = false;\n  private stream: MediaStream;\n  private readonly fftSize: number;\n  private readonly columnTruncateLength: number;\n  private freqData: Float32Array;\n  private timeData: Float32Array;\n  private readonly numFrames: number;\n  private analyser: AnalyserNode;\n  private audioContext: AudioContext;\n  private sampleRateHz: number;\n  private readonly audioTrackConstraints: MediaTrackConstraints;\n  private readonly smoothingTimeConstant: number;\n  private readonly includeSpectrogram: boolean;\n  private readonly includeWaveform: boolean;\n\n  private constructor(protected readonly microphoneConfig: MicrophoneConfig) {\n    super();\n    this.fftSize = microphoneConfig.fftSize || 1024;\n    const fftSizeLog2 = Math.log2(this.fftSize);\n    if (this.fftSize < 0 || fftSizeLog2 < 4 || fftSizeLog2 > 14 ||\n        !Number.isInteger(fftSizeLog2)) {\n      throw new Error(\n          `Invalid fftSize: it must be a power of 2 between ` +\n          `2 to 4 and 2 to 14, but got ${this.fftSize}`);\n    }\n\n    this.numFrames = microphoneConfig.numFramesPerSpectrogram || 43;\n    this.sampleRateHz = microphoneConfig.sampleRateHz;\n    this.columnTruncateLength =\n        microphoneConfig.columnTruncateLength || this.fftSize;\n    this.audioTrackConstraints = microphoneConfig.audioTrackConstraints;\n    this.smoothingTimeConstant = microphoneConfig.smoothingTimeConstant || 0;\n\n    this.includeSpectrogram =\n        microphoneConfig.includeSpectrogram === false ? false : true;\n    this.includeWaveform =\n        microphoneConfig.includeWaveform === true ? true : false;\n    if (!this.includeSpectrogram && !this.includeWaveform) {\n      throw new Error(\n          'Both includeSpectrogram and includeWaveform are false. ' +\n          'At least one type of data should be returned.');\n    }\n  }\n\n  summary() {\n    return `microphone`;\n  }\n\n  // Construct a MicrophoneIterator and start the audio stream.\n  static async create(microphoneConfig: MicrophoneConfig = {}) {\n    if (!env().get('IS_BROWSER')) {\n      throw new Error(\n          'microphone API is only supported in browser environment.');\n    }\n\n    const microphoneIterator = new MicrophoneIterator(microphoneConfig);\n\n    // Call async function start() to initialize the audio stream.\n    await microphoneIterator.start();\n\n    return microphoneIterator;\n  }\n\n  // Start the audio stream and FFT.\n  async start(): Promise<void> {\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        audio: this.audioTrackConstraints == null ? true :\n                                                    this.audioTrackConstraints,\n        video: false\n      });\n    } catch (e) {\n      throw new Error(\n          `Error thrown while initializing video stream: ${e.message}`);\n    }\n\n    if (!this.stream) {\n      throw new Error('Could not obtain audio from microphone.');\n    }\n\n    const ctxConstructor =\n        // tslint:disable-next-line:no-any\n        (window as any).AudioContext || (window as any).webkitAudioContext;\n    this.audioContext = new ctxConstructor();\n\n    if (!this.sampleRateHz) {\n      // If sample rate is not provided, use the available sample rate on\n      // device.\n      this.sampleRateHz = this.audioContext.sampleRate;\n    } else if (this.audioContext.sampleRate !== this.sampleRateHz) {\n      throw new Error(\n          `Mismatch in sampling rate: ` +\n          `Expected: ${this.sampleRateHz}; ` +\n          `Actual: ${this.audioContext.sampleRate}`);\n    }\n\n    const streamSource = this.audioContext.createMediaStreamSource(this.stream);\n    this.analyser = this.audioContext.createAnalyser();\n    this.analyser.fftSize = this.fftSize * 2;\n    this.analyser.smoothingTimeConstant = this.smoothingTimeConstant;\n    streamSource.connect(this.analyser);\n    this.freqData = new Float32Array(this.fftSize);\n    this.timeData = new Float32Array(this.fftSize);\n    return;\n  }\n\n  async next(): Promise<IteratorResult<TensorContainer>> {\n    if (this.isClosed) {\n      return {value: null, done: true};\n    }\n\n    let spectrogramTensor: Tensor;\n    let waveformTensor: Tensor;\n\n    const audioDataQueue = await this.getAudioData();\n    if (this.includeSpectrogram) {\n      const freqData = this.flattenQueue(audioDataQueue.freqDataQueue);\n      spectrogramTensor = this.getTensorFromAudioDataArray(\n          freqData, [this.numFrames, this.columnTruncateLength, 1]);\n    }\n    if (this.includeWaveform) {\n      const timeData = this.flattenQueue(audioDataQueue.timeDataQueue);\n      waveformTensor = this.getTensorFromAudioDataArray(\n          timeData, [this.numFrames * this.fftSize, 1]);\n    }\n\n    return {\n      value: {'spectrogram': spectrogramTensor, 'waveform': waveformTensor},\n      done: false\n    };\n  }\n\n  // Capture one result from the audio stream, and extract the value from\n  // iterator.next() result.\n  async capture(): Promise<{spectrogram: Tensor3D, waveform: Tensor2D}> {\n    return (await this.next()).value as\n        {spectrogram: Tensor3D, waveform: Tensor2D};\n  }\n\n  private async getAudioData():\n      Promise<{freqDataQueue: Float32Array[], timeDataQueue: Float32Array[]}> {\n    const freqDataQueue: Float32Array[] = [];\n    const timeDataQueue: Float32Array[] = [];\n    let currentFrames = 0;\n    return new Promise(resolve => {\n      const intervalID = setInterval(() => {\n        if (this.includeSpectrogram) {\n          this.analyser.getFloatFrequencyData(this.freqData);\n          // If the audio stream is initializing, return empty queue.\n          if (this.freqData[0] === -Infinity) {\n            resolve({freqDataQueue, timeDataQueue});\n          }\n          freqDataQueue.push(this.freqData.slice(0, this.columnTruncateLength));\n        }\n        if (this.includeWaveform) {\n          this.analyser.getFloatTimeDomainData(this.timeData);\n          timeDataQueue.push(this.timeData.slice());\n        }\n\n        // Clean interval and return when all frames have been collected\n        if (++currentFrames === this.numFrames) {\n          clearInterval(intervalID);\n          resolve({freqDataQueue, timeDataQueue});\n        }\n      }, this.fftSize / this.sampleRateHz * 1e3);\n    });\n  }\n\n  // Stop the audio stream and pause the iterator.\n  stop(): void {\n    if (!this.isClosed) {\n      this.isClosed = true;\n      this.analyser.disconnect();\n      this.audioContext.close();\n      if (this.stream != null && this.stream.getTracks().length > 0) {\n        this.stream.getTracks()[0].stop();\n      }\n    }\n  }\n\n  // Override toArray() function to prevent collecting.\n  override toArray(): Promise<Tensor[]> {\n    throw new Error('Can not convert infinite audio stream to array.');\n  }\n\n  // Return audio sampling rate in Hz\n  getSampleRate(): number {\n    return this.sampleRateHz;\n  }\n\n  private flattenQueue(queue: Float32Array[]): Float32Array {\n    const frameSize = queue[0].length;\n    const freqData = new Float32Array(queue.length * frameSize);\n    queue.forEach((data, i) => freqData.set(data, i * frameSize));\n    return freqData;\n  }\n\n  private getTensorFromAudioDataArray(freqData: Float32Array, shape: number[]):\n      Tensor {\n    const vals = new Float32Array(util.sizeFromShape(shape));\n    // If the data is less than the output shape, the rest is padded with zeros.\n    vals.set(freqData, vals.length - freqData.length);\n    return tensor(vals, shape);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {browser, cast, env, expandDims, image, reshape, tensor1d, Tensor1D, tensor2d, Tensor2D, Tensor3D, Tensor4D, tidy, util} from '@tensorflow/tfjs-core';\nimport {WebcamConfig} from '../types';\nimport {LazyIterator} from './lazy_iterator';\n\n/**\n * Provide a stream of image tensors from webcam video stream. Only works in\n * browser environment.\n */\nexport class WebcamIterator extends LazyIterator<Tensor3D> {\n  private isClosed = true;\n  private stream: MediaStream;\n  private resize = false;\n  private cropSize: [number, number];\n  private cropBox: Tensor2D;\n  private cropBoxInd: Tensor1D;\n\n  private constructor(\n      protected readonly webcamVideoElement: HTMLVideoElement,\n      protected readonly webcamConfig: WebcamConfig) {\n    super();\n    if (this.needToResize()) {\n      this.resize = true;\n      this.cropSize =\n          [this.webcamConfig.resizeHeight, this.webcamConfig.resizeWidth];\n      this.cropBoxInd = tensor1d([0], 'int32');\n      if (this.webcamConfig.centerCrop) {\n        // Calculate the box based on resizing shape.\n        const widthCroppingRatio =\n            this.webcamConfig.resizeWidth * 1.0 / this.webcamVideoElement.width;\n        const heightCroppingRatio = this.webcamConfig.resizeHeight * 1.0 /\n            this.webcamVideoElement.height;\n        const widthCropStart = (1 - widthCroppingRatio) / 2;\n        const heightCropStart = (1 - heightCroppingRatio) / 2;\n        const widthCropEnd = widthCropStart + widthCroppingRatio;\n        const heightCropEnd = heightCroppingRatio + heightCropStart;\n        this.cropBox = tensor2d(\n            [heightCropStart, widthCropStart, heightCropEnd, widthCropEnd],\n            [1, 4]);\n      } else {\n        this.cropBox = tensor2d([0, 0, 1, 1], [1, 4]);\n      }\n    }\n  }\n\n  summary() {\n    return `webcam`;\n  }\n\n  // Construct a WebcamIterator and start it's video stream.\n  static async create(\n      webcamVideoElement?: HTMLVideoElement, webcamConfig: WebcamConfig = {}) {\n    if (!env().get('IS_BROWSER')) {\n      throw new Error(\n          'tf.data.webcam is only supported in browser environment.');\n    }\n\n    if (!webcamVideoElement) {\n      // If webcam video element is not provided, create a hidden video element\n      // with provided width and height.\n      webcamVideoElement = document.createElement('video');\n      if (!webcamConfig.resizeWidth || !webcamConfig.resizeHeight) {\n        throw new Error(\n            'Please provide webcam video element, or resizeWidth and ' +\n            'resizeHeight to create a hidden video element.');\n      }\n      webcamVideoElement.width = webcamConfig.resizeWidth;\n      webcamVideoElement.height = webcamConfig.resizeHeight;\n    }\n    const webcamIterator = new WebcamIterator(webcamVideoElement, webcamConfig);\n\n    // Call async function to initialize the video stream.\n    await webcamIterator.start();\n\n    return webcamIterator;\n  }\n\n  // Async function to start video stream.\n  async start(): Promise<void> {\n    if (this.webcamConfig.facingMode) {\n      util.assert(\n          (this.webcamConfig.facingMode === 'user') ||\n              (this.webcamConfig.facingMode === 'environment'),\n          () =>\n              `Invalid webcam facing mode: ${this.webcamConfig.facingMode}. ` +\n              `Please provide 'user' or 'environment'`);\n    }\n\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          deviceId: this.webcamConfig.deviceId,\n          facingMode: this.webcamConfig.facingMode ?\n              this.webcamConfig.facingMode :\n              'user',\n          width: this.webcamVideoElement.width,\n          height: this.webcamVideoElement.height\n        }\n      });\n    } catch (e) {\n      // Modify the error message but leave the stack trace intact\n      e.message = `Error thrown while initializing video stream: ${e.message}`;\n      throw e;\n    }\n\n    if (!this.stream) {\n      throw new Error('Could not obtain video from webcam.');\n    }\n\n    // Older browsers may not have srcObject\n    try {\n      this.webcamVideoElement.srcObject = this.stream;\n    } catch (error) {\n      console.log(error);\n      this.webcamVideoElement.src = window.URL.createObjectURL(\n        this.stream as unknown as MediaSource);\n    }\n    // Start the webcam video stream\n    this.webcamVideoElement.play();\n\n    this.isClosed = false;\n\n    return new Promise<void>(resolve => {\n      // Add event listener to make sure the webcam has been fully initialized.\n      this.webcamVideoElement.onloadedmetadata = () => {\n        resolve();\n      };\n    });\n  }\n\n  async next(): Promise<IteratorResult<Tensor3D>> {\n    if (this.isClosed) {\n      return {value: null, done: true};\n    }\n\n    let img;\n    try {\n      img = browser.fromPixels(this.webcamVideoElement);\n    } catch (e) {\n      throw new Error(\n          `Error thrown converting video to pixels: ${JSON.stringify(e)}`);\n    }\n    if (this.resize) {\n      try {\n        return {value: this.cropAndResizeFrame(img), done: false};\n      } catch (e) {\n        throw new Error(`Error thrown cropping the video: ${e.message}`);\n      } finally {\n        img.dispose();\n      }\n    } else {\n      return {value: img, done: false};\n    }\n  }\n\n  private needToResize() {\n    // If resizeWidth and resizeHeight are provided, and different from the\n    // width and height of original HTMLVideoElement, then resizing and cropping\n    // is required.\n    if (this.webcamConfig.resizeWidth && this.webcamConfig.resizeHeight &&\n        (this.webcamVideoElement.width !== this.webcamConfig.resizeWidth ||\n         this.webcamVideoElement.height !== this.webcamConfig.resizeHeight)) {\n      return true;\n    }\n    return false;\n  }\n\n  // Cropping and resizing each frame based on config\n  cropAndResizeFrame(img: Tensor3D): Tensor3D {\n    return tidy(() => {\n      const expandedImage: Tensor4D = expandDims(cast(img, 'float32'), (0));\n      let resizedImage;\n      resizedImage = image.cropAndResize(\n          expandedImage, this.cropBox, this.cropBoxInd, this.cropSize,\n          'bilinear');\n      // Extract image from batch cropping.\n      const shape = resizedImage.shape;\n      return reshape(resizedImage, shape.slice(1) as [number, number, number]);\n    });\n  }\n\n  // Capture one frame from the video stream, and extract the value from\n  // iterator.next() result.\n  async capture(): Promise<Tensor3D> {\n    return (await this.next()).value;\n  }\n\n  // Stop the video stream and pause webcam iterator.\n  stop(): void {\n    const tracks = this.stream.getTracks();\n\n    tracks.forEach(track => track.stop());\n\n    try {\n      this.webcamVideoElement.srcObject = null;\n    } catch (error) {\n      console.log(error);\n      this.webcamVideoElement.src = null;\n    }\n    this.isClosed = true;\n  }\n\n  // Override toArray() function to prevent collecting.\n  override toArray(): Promise<Tensor3D[]> {\n    throw new Error('Can not convert infinite video stream to array.');\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {ByteChunkIterator} from './iterators/byte_chunk_iterator';\n\n/**\n * Represents a data source readable as a stream of binary data chunks.\n *\n * Because `Dataset`s can be read repeatedly (via `Dataset.iterator()`), this\n * provides a means to repeatedly create streams from the underlying data\n * sources.\n */\nexport abstract class DataSource {\n  /**\n   * Obtain a new stream of binary data chunks.\n   *\n   * Starts the new stream from the beginning of the data source, even if other\n   * streams have been obtained previously.\n   */\n  abstract iterator(): Promise<ByteChunkIterator>;\n\n  // TODO(soergel): consider chainable Dataset construction here\n}\n\n// TODO(soergel): consider convenience factory functions here\n// in combination with chainable source->dataset above, e.g.:\n// tf.data.url(...).asCsvDataset().shuffle().batch()\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {LazyIterator, OneToManyIterator} from './lazy_iterator';\n\nexport abstract class StringIterator extends LazyIterator<string> {\n  /**\n   * Splits a string stream on a given separator.\n   *\n   * It is assumed that the incoming chunk boundaries have no semantic meaning,\n   * so conceptually the incoming stream is treated simply as the concatenation\n   * of its elements.\n   *\n   * The outgoing stream provides chunks corresponding to the results of the\n   * standard string split() operation (even if such a chunk spanned incoming\n   * chunks).  The separators are not included.\n   *\n   * A typical usage is to split a text file (represented as a stream with\n   * arbitrary chunk boundaries) into lines.\n   *\n   * @param upstream A readable stream of strings that can be treated as\n   *   concatenated.\n   * @param separator A character to split on.\n   */\n  split(separator: string): StringIterator {\n    return new SplitIterator(this, separator);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on StringIterator.  Unfortunately they can't be placed in separate files, due\n// to resulting trouble with circular imports.\n// ============================================================================\n\n// We wanted multiple inheritance, e.g.\n//   class SplitIterator extends QueueIterator<string>, StringIterator\n// but the TypeScript mixin approach is a bit hacky, so we take this adapter\n// approach instead.\n\nclass SplitIterator extends StringIterator {\n  private impl: SplitIteratorImpl;\n\n  constructor(protected upstream: LazyIterator<string>, separator: string) {\n    super();\n    this.impl = new SplitIteratorImpl(upstream, separator);\n  }\n\n  summary() {\n    return this.impl.summary();\n  }\n\n  async next() {\n    return this.impl.next();\n  }\n}\n\nclass SplitIteratorImpl extends OneToManyIterator<string> {\n  // A partial string at the end of an upstream chunk\n  carryover = '';\n\n  constructor(\n      protected upstream: LazyIterator<string>, protected separator: string) {\n    super();\n  }\n\n  summary() {\n    return `${this.upstream.summary()} -> Split('${this.separator}')`;\n  }\n\n  async pump(): Promise<boolean> {\n    const chunkResult = await this.upstream.next();\n    if (chunkResult.done) {\n      if (this.carryover === '') {\n        return false;\n      }\n\n      // Pretend that the pump succeeded in order to emit the small last batch.\n      // The next pump() call will actually fail.\n      this.outputQueue.push(this.carryover);\n      this.carryover = '';\n      return true;\n    }\n    const lines = chunkResult.value.split(this.separator) as string[];\n    // Note the behavior: \" ab \".split(' ') === ['', 'ab', '']\n    // Thus the carryover may be '' if the separator falls on a chunk\n    // boundary; this produces the correct result.\n\n    lines[0] = this.carryover + lines[0];\n    for (const line of lines.slice(0, -1)) {\n      this.outputQueue.push(line);\n    }\n    this.carryover = lines[lines.length - 1];\n\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {LazyIterator, OneToManyIterator} from './lazy_iterator';\nimport {StringIterator} from './string_iterator';\n\nexport abstract class ByteChunkIterator extends LazyIterator<Uint8Array> {\n  /**\n   * Decode a stream of UTF8-encoded byte arrays to a stream of strings.\n   *\n   * The byte arrays producetd from the ByteChunkIterator on which this is\n   * called will be interpreted as concatenated.  No assumptions are made about\n   * the boundaries of the incoming chunks, so a multi-byte UTF8 encoding of a\n   * character may span the boundary between chunks.  This naturally happens,\n   * for instance, when reading fixed-size byte arrays from a file.\n   */\n  decodeUTF8(): StringIterator {\n    return new Utf8Iterator(this);\n  }\n}\n\n// ============================================================================\n// The following private classes serve to implement the chainable methods\n// on ByteChunkIterator.  Unfortunately they can't be placed in separate files,\n// due to resulting trouble with circular imports.\n// ============================================================================\n\n// We wanted multiple inheritance, e.g.\n//   class Utf8Iterator extends QueueIterator<string>, StringIterator\n// but the TypeScript mixin approach is a bit hacky, so we take this adapter\n// approach instead.\n\nclass Utf8Iterator extends StringIterator {\n  private impl: Utf8IteratorImpl;\n\n  constructor(protected upstream: LazyIterator<Uint8Array>) {\n    super();\n    this.impl = new Utf8IteratorImpl(upstream);\n  }\n\n  summary() {\n    return this.impl.summary();\n  }\n\n  async next() {\n    return this.impl.next();\n  }\n}\n\n/**\n * Decode a stream of UTF8-encoded byte arrays to a stream of strings.\n *\n * This is tricky because the incoming byte array boundaries may disrupt a\n * multi-byte UTF8 character. Thus any incomplete character data at the end of\n * a chunk must be carried over and prepended to the next chunk before\n * decoding. Luckily with native decoder, TextDecoder in browser and\n * string_decoder in node, byte array boundaries are handled automatically.\n *\n * In the context of an input pipeline for machine learning, UTF8 decoding is\n * needed to parse text files containing training examples or prediction\n * requests (e.g., formatted as CSV or JSON). We cannot use the built-in\n * decoding provided by FileReader.readAsText() because here we are in a\n * streaming context, which FileReader does not support.\n *\n * @param upstream A `LazyIterator` of `Uint8Arrays` containing UTF8-encoded\n *   text, which should be interpreted as concatenated.  No assumptions are\n *   made about the boundaries of the incoming chunks, so a multi-byte UTF8\n *   encoding of a character may span the boundary between chunks.  This\n *   naturally happens, for instance, when reading fixed-size byte arrays from a\n *   file.\n */\nclass Utf8IteratorImpl extends OneToManyIterator<string> {\n  // `decoder` as `any` here to dynamically assign value based on the\n  // environment.\n  // tslint:disable-next-line:no-any\n  decoder: any;\n\n  constructor(protected readonly upstream: LazyIterator<Uint8Array>) {\n    super();\n    if (env().get('IS_BROWSER')) {\n      this.decoder = new TextDecoder('utf-8');\n    } else {\n      // tslint:disable-next-line:no-require-imports\n      const {StringDecoder} = require('string_decoder');\n      this.decoder = new StringDecoder('utf8');\n    }\n  }\n  summary() {\n    return `${this.upstream.summary()} -> Utf8`;\n  }\n\n  async pump(): Promise<boolean> {\n    const chunkResult = await this.upstream.next();\n    let chunk;\n    if (chunkResult.done) {\n      return false;\n    } else {\n      chunk = chunkResult.value;\n    }\n\n    let text: string;\n    if (env().get('IS_BROWSER')) {\n      text = this.decoder.decode(chunk, {stream: true});\n    } else {\n      text = this.decoder.write(Buffer.from(chunk.buffer));\n    }\n    this.outputQueue.push(text);\n    return true;\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// inspired by https://github.com/maxogden/filereader-stream\nimport {env, util} from '@tensorflow/tfjs-core';\nimport {FileElement} from '../types';\nimport {ByteChunkIterator} from './byte_chunk_iterator';\n\nexport interface FileChunkIteratorOptions {\n  /** The byte offset at which to begin reading the File or Blob. Default 0. */\n  offset?: number;\n  /** The number of bytes to read at a time. Default 1MB. */\n  chunkSize?: number;\n}\n\n/**\n * Provide a stream of chunks from a File, Blob, or Uint8Array.\n * @param file The source File, Blob or Uint8Array.\n * @param options Optional settings controlling file reading.\n * @returns a lazy Iterator of Uint8Arrays containing sequential chunks of the\n *   input File, Blob or Uint8Array.\n */\nexport class FileChunkIterator extends ByteChunkIterator {\n  offset: number;\n  chunkSize: number;\n\n  constructor(\n      protected file: FileElement,\n      protected options: FileChunkIteratorOptions = {}) {\n    super();\n    util.assert(\n        (file instanceof Uint8Array) ||\n            (env().get('IS_BROWSER') ?\n                 (file instanceof File || file instanceof Blob) :\n                 false),\n        () => 'FileChunkIterator only supports File, Blob and Uint8Array ' +\n            'right now.');\n    this.offset = options.offset || 0;\n    // default 1MB chunk has tolerable perf on large files\n    this.chunkSize = options.chunkSize || 1024 * 1024;\n  }\n\n  summary() {\n    return `FileChunks ${this.file}`;\n  }\n\n  async next(): Promise<IteratorResult<Uint8Array>> {\n    if (this.offset >= ((this.file instanceof Uint8Array) ?\n                            this.file.byteLength :\n                            this.file.size)) {\n      return {value: null, done: true};\n    }\n    const chunk = new Promise<Uint8Array>((resolve, reject) => {\n      const end = this.offset + this.chunkSize;\n      if (this.file instanceof Uint8Array) {\n        // Note if end > this.uint8Array.byteLength, we just get a small last\n        // chunk.\n        resolve(new Uint8Array(this.file.slice(this.offset, end)));\n      } else {\n        // This branch assumes that this.file type is File or Blob, which\n        // means it is in the browser environment.\n\n        // TODO(soergel): is this a performance issue?\n        const fileReader = new FileReader();\n        fileReader.onload = (event) => {\n          let data: string|ArrayBuffer|Uint8Array = fileReader.result;\n          // Not sure we can trust the return type of\n          // FileReader.readAsArrayBuffer See e.g.\n          // https://github.com/node-file-api/FileReader/issues/2\n          if (data instanceof ArrayBuffer) {\n            data = new Uint8Array(data);\n          }\n          if (!(data instanceof Uint8Array)) {\n            return reject(new TypeError('FileReader returned unknown type.'));\n          }\n          resolve(data);\n        };\n        fileReader.onabort = (event) => {\n          return reject(new Error('Aborted'));\n        };\n        fileReader.onerror = (event) => {\n          return reject(new Error(event.type));\n        };\n        // TODO(soergel): better handle onabort, onerror\n        // Note if end > this.file.size, we just get a small last chunk.\n        const slice = this.file.slice(this.offset, end);\n        // We can't use readAsText here (even if we know the file is text)\n        // because the slice boundary may fall within a multi-byte character.\n        fileReader.readAsArrayBuffer(slice);\n      }\n      this.offset = end;\n    });\n    return {value: (await chunk), done: false};\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {util} from '@tensorflow/tfjs-core';\nimport {FileChunkIterator, FileChunkIteratorOptions} from './file_chunk_iterator';\n\n/**\n * Provide a stream of chunks from a URL.\n *\n * Note this class first downloads the entire file into memory before providing\n * the first element from the stream.  This is because the Fetch API does not\n * yet reliably provide a reader stream for the response body.\n */\nexport async function urlChunkIterator(\n    url: RequestInfo, options: FileChunkIteratorOptions = {},\n    fetchFunc?: Function) {\n  let urlString;\n  let requestInit;\n  if ((typeof url) === 'string') {\n    urlString = url as string;\n  } else {\n    urlString = (url as Request).url;\n    requestInit = getRequestInitFromRequest(url as Request);\n  }\n  const response = await (fetchFunc || util.fetch)(urlString, requestInit);\n  if (response.ok) {\n    const uint8Array = new Uint8Array(await response.arrayBuffer());\n    return new FileChunkIterator(uint8Array, options);\n  } else {\n    throw new Error(response.statusText);\n  }\n}\n\n// Generate RequestInit from Request to match tf.util.fetch signature.\nconst getRequestInitFromRequest = (request: Request) => {\n  const init = {\n    method: request.method,\n    headers: request.headers,\n    body: request.body,\n    mode: request.mode,\n    credentials: request.credentials,\n    cache: request.cache,\n    redirect: request.redirect,\n    referrer: request.referrer,\n    integrity: request.integrity,\n  };\n  return init;\n};\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\n// Skip tslint any type check cause this method is aiming to check type of\n// input.\n// tslint:disable-next-line:no-any\nexport function isLocalPath(source: any): boolean {\n  return (typeof source === 'string') && source.slice(0, 7) === 'file://';\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {env} from '@tensorflow/tfjs-core';\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIterator, FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {FileElement} from '../types';\nimport {isLocalPath} from '../util/source_util';\n\n/**\n * Represents a file, blob, or Uint8Array readable as a stream of binary data\n * chunks.\n */\nexport class FileDataSource extends DataSource {\n  /**\n   * Create a `FileDataSource`.\n   *\n   * @param input Local file path, or `File`/`Blob`/`Uint8Array` object to\n   *     read. Local file only works in node environment.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected input: FileElement|string,\n      protected readonly options: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.input) && env().get('IS_NODE')) {\n      // tslint:disable-next-line:no-require-imports\n      const fs = require('fs');\n      this.input = fs.readFileSync((this.input as string).slice(7));\n    }\n    // TODO(kangyizhang): Add LocalFileChunkIterator to split local streaming\n    // with file in browser.\n    return new FileChunkIterator(this.input as FileElement, this.options);\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {DataSource} from '../datasource';\nimport {ByteChunkIterator} from '../iterators/byte_chunk_iterator';\nimport {FileChunkIteratorOptions} from '../iterators/file_chunk_iterator';\nimport {urlChunkIterator} from '../iterators/url_chunk_iterator';\nimport {isLocalPath} from '../util/source_util';\nimport {FileDataSource} from './file_data_source';\n\n/*\n * Represents a URL readable as a stream of binary data chunks.\n */\nexport class URLDataSource extends DataSource {\n  /**\n   * Create a `URLDataSource`.\n   *\n   * @param url A source URL string, or a `Request` object.\n   * @param options Options passed to the underlying `FileChunkIterator`s,\n   *   such as {chunksize: 1024}.\n   */\n  constructor(\n      protected readonly url: RequestInfo,\n      protected readonly fileOptions: FileChunkIteratorOptions = {}) {\n    super();\n  }\n\n  // TODO(soergel): provide appropriate caching options.  Currently this\n  // will download the URL anew for each call to iterator().  Since we have\n  // to treat the downloaded file as a blob/buffer anyway, we may as well retain\n  // it-- but that raises GC issues.  Also we may want a persistent disk cache.\n  async iterator(): Promise<ByteChunkIterator> {\n    if (isLocalPath(this.url)) {\n      return (new FileDataSource(this.url as string, this.fileOptions))\n          .iterator();\n    } else {\n      return urlChunkIterator(this.url, this.fileOptions);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2018 Google LLC. All Rights Reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * =============================================================================\n */\n\nimport {TensorContainer} from '@tensorflow/tfjs-core';\nimport {Dataset, datasetFromIteratorFn} from './dataset';\nimport {CSVDataset} from './datasets/csv_dataset';\nimport {iteratorFromFunction} from './iterators/lazy_iterator';\nimport {MicrophoneIterator} from './iterators/microphone_iterator';\nimport {WebcamIterator} from './iterators/webcam_iterator';\nimport {URLDataSource} from './sources/url_data_source';\nimport {CSVConfig, MicrophoneConfig, WebcamConfig} from './types';\n\n/**\n * Create a `CSVDataset` by reading and decoding CSV file(s) from provided URL\n * or local path if it's in Node environment.\n *\n * Note: If isLabel in columnConfigs is `true` for at least one column, the\n * element in returned `CSVDataset` will be an object of\n * `{xs:features, ys:labels}`: xs is a dict of features key/value pairs, ys\n * is a dict of labels key/value pairs. If no column is marked as label,\n * returns a dict of features only.\n *\n * ```js\n * const csvUrl =\n * 'https://storage.googleapis.com/tfjs-examples/multivariate-linear-regression/data/boston-housing-train.csv';\n *\n * async function run() {\n *   // We want to predict the column \"medv\", which represents a median value of\n *   // a home (in $1000s), so we mark it as a label.\n *   const csvDataset = tf.data.csv(\n *     csvUrl, {\n *       columnConfigs: {\n *         medv: {\n *           isLabel: true\n *         }\n *       }\n *     });\n *\n *   // Number of features is the number of column names minus one for the label\n *   // column.\n *   const numOfFeatures = (await csvDataset.columnNames()).length - 1;\n *\n *   // Prepare the Dataset for training.\n *   const flattenedDataset =\n *     csvDataset\n *     .map(({xs, ys}) =>\n *       {\n *         // Convert xs(features) and ys(labels) from object form (keyed by\n *         // column name) to array form.\n *         return {xs:Object.values(xs), ys:Object.values(ys)};\n *       })\n *     .batch(10);\n *\n *   // Define the model.\n *   const model = tf.sequential();\n *   model.add(tf.layers.dense({\n *     inputShape: [numOfFeatures],\n *     units: 1\n *   }));\n *   model.compile({\n *     optimizer: tf.train.sgd(0.000001),\n *     loss: 'meanSquaredError'\n *   });\n *\n *   // Fit the model using the prepared Dataset\n *   return model.fitDataset(flattenedDataset, {\n *     epochs: 10,\n *     callbacks: {\n *       onEpochEnd: async (epoch, logs) => {\n *         console.log(epoch + ':' + logs.loss);\n *       }\n *     }\n *   });\n * }\n *\n * await run();\n * ```\n *\n * @param source URL or local path to get CSV file. If it's a local path, it\n * must have prefix `file://` and it only works in node environment.\n * @param csvConfig (Optional) A CSVConfig object that contains configurations\n *     of reading and decoding from CSV file(s).\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   configParamIndices: [1]\n *  }\n */\nexport function csv(\n    source: RequestInfo, csvConfig: CSVConfig = {}): CSVDataset {\n  return new CSVDataset(new URLDataSource(source), csvConfig);\n}\n\n/**\n * Create a `Dataset` that produces each element by calling a provided function.\n *\n * Note that repeated iterations over this `Dataset` may produce different\n * results, because the function will be called anew for each element of each\n * iteration.\n *\n * Also, beware that the sequence of calls to this function may be out of order\n * in time with respect to the logical order of the Dataset. This is due to the\n * asynchronous lazy nature of stream processing, and depends on downstream\n * transformations (e.g. .shuffle()). If the provided function is pure, this is\n * no problem, but if it is a closure over a mutable state (e.g., a traversal\n * pointer), then the order of the produced elements may be scrambled.\n *\n * ```js\n * let i = -1;\n * const func = () =>\n *    ++i < 5 ? {value: i, done: false} : {value: null, done: true};\n * const ds = tf.data.func(func);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * @param f A function that produces one data element on each call.\n */\nexport function func<T extends TensorContainer>(\n    f: () => IteratorResult<T>| Promise<IteratorResult<T>>): Dataset<T> {\n  const iter = iteratorFromFunction(f);\n  return datasetFromIteratorFn(async () => iter);\n}\n\n/**\n * Create a `Dataset` that produces each element from provided JavaScript\n * generator, which is a function that returns a (potentially async) iterator.\n *\n * For more information on iterators and generators, see\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Iterators_and_Generators .\n * For the iterator protocol, see\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols .\n *\n * Example of creating a dataset from an iterator factory:\n * ```js\n * function makeIterator() {\n *   const numElements = 10;\n *   let index = 0;\n *\n *   const iterator = {\n *     next: () => {\n *       let result;\n *       if (index < numElements) {\n *         result = {value: index, done: false};\n *         index++;\n *         return result;\n *       }\n *       return {value: index, done: true};\n *     }\n *   };\n *   return iterator;\n * }\n * const ds = tf.data.generator(makeIterator);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * Example of creating a dataset from a generator:\n * ```js\n * function* dataGenerator() {\n *   const numElements = 10;\n *   let index = 0;\n *   while (index < numElements) {\n *     const x = index;\n *     index++;\n *     yield x;\n *   }\n * }\n *\n * const ds = tf.data.generator(dataGenerator);\n * await ds.forEachAsync(e => console.log(e));\n * ```\n *\n * @param generator A JavaScript function that returns\n *     a (potentially async) JavaScript iterator.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   configParamIndices: [1]\n *  }\n */\nexport function generator<T extends TensorContainer>(\n  generator: () => Iterator<T> | Promise<Iterator<T>> | AsyncIterator<T>,\n): Dataset<T> {\n  return datasetFromIteratorFn(async () => {\n    const gen = await generator();\n    return iteratorFromFunction(() => gen.next());\n  });\n}\n\n/**\n * Create an iterator that generates `Tensor`s from webcam video stream. This\n * API only works in Browser environment when the device has webcam.\n *\n * Note: this code snippet only works when the device has a webcam. It will\n * request permission to open the webcam when running.\n * ```js\n * const videoElement = document.createElement('video');\n * videoElement.width = 100;\n * videoElement.height = 100;\n * const cam = await tf.data.webcam(videoElement);\n * const img = await cam.capture();\n * img.print();\n * cam.stop();\n * ```\n *\n * @param webcamVideoElement A `HTMLVideoElement` used to play video from\n *     webcam. If this element is not provided, a hidden `HTMLVideoElement` will\n *     be created. In that case, `resizeWidth` and `resizeHeight` must be\n *     provided to set the generated tensor shape.\n * @param webcamConfig A `WebcamConfig` object that contains configurations of\n *     reading and manipulating data from webcam video stream.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   ignoreCI: true\n *  }\n */\nexport async function webcam(\n    webcamVideoElement?: HTMLVideoElement,\n    webcamConfig?: WebcamConfig): Promise<WebcamIterator> {\n  return WebcamIterator.create(webcamVideoElement, webcamConfig);\n}\n\n/**\n * Create an iterator that generates frequency-domain spectrogram `Tensor`s from\n * microphone audio stream with browser's native FFT. This API only works in\n * browser environment when the device has microphone.\n *\n * Note: this code snippet only works when the device has a microphone. It will\n * request permission to open the microphone when running.\n * ```js\n * const mic = await tf.data.microphone({\n *   fftSize: 1024,\n *   columnTruncateLength: 232,\n *   numFramesPerSpectrogram: 43,\n *   sampleRateHz:44100,\n *   includeSpectrogram: true,\n *   includeWaveform: true\n * });\n * const audioData = await mic.capture();\n * const spectrogramTensor = audioData.spectrogram;\n * spectrogramTensor.print();\n * const waveformTensor = audioData.waveform;\n * waveformTensor.print();\n * mic.stop();\n * ```\n *\n * @param microphoneConfig A `MicrophoneConfig` object that contains\n *     configurations of reading audio data from microphone.\n *\n * @doc {\n *   heading: 'Data',\n *   subheading: 'Creation',\n *   namespace: 'data',\n *   ignoreCI: true\n *  }\n */\nexport async function microphone(microphoneConfig?: MicrophoneConfig):\n    Promise<MicrophoneIterator> {\n  return MicrophoneIterator.create(microphoneConfig);\n}\n", "/** @license See the LICENSE file. */\n\n// This code is auto-generated, do not modify this file!\nconst version = '4.22.0';\nexport {version};\n"], "names": ["this", "define", "require$$0", "require$$1", "require$$2", "require$$3", "require$$4", "require$$5", "require$$6", "tf", "seedrandom.alea", "util", "env", "tensor", "tensor1d", "tensor2d", "browser", "tidy", "expandDims", "cast", "image", "reshape"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA;CACA;CACA;CACA;AACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;AACA;AACA;CACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;EACA,SAAS,IAAI,CAAC,IAAI,EAAE;IAClB,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,CAAC;AAC/B;CACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;CACvB,KAAI,IAAI,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,sBAAsB,CAAC;CAC5D,KAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;CAClB,KAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;CAClB,KAAI,OAAO,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;CACtC,IAAG,CAAC;AACJ;CACA;CACA,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;IACT,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IAClB,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IAClB,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IAClB,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;CACtB,GAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;IAC9B,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;CACtB,GAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;IAC9B,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;CACtB,GAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;IAC9B,IAAI,GAAG,IAAI,CAAC;GACb;AACD;CACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;CACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;CACd,GAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;CACd,GAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;IACZ,OAAO,CAAC,CAAC;GACV;AACD;CACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;CAC1B,GAAE,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC;CACzB,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;CAChC,OAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;CACrB,GAAE,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,WAAW,IAAI,CAAC,CAAC,GAAE;CACnE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;CAC3B,KAAI,OAAO,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,QAAQ,GAAG,CAAC,IAAI,sBAAsB,CAAC;CACrE,IAAG,CAAC;CACJ,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,IAAI,KAAK,EAAE;CACb,KAAI,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;CACnD,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;KACjD;IACD,OAAO,IAAI,CAAC;GACb;AACD;CACA,CAAA,SAAS,IAAI,GAAG;CAChB,GAAE,IAAI,CAAC,GAAG,UAAU,CAAC;AACrB;CACA,GAAE,IAAI,IAAI,GAAG,SAAS,IAAI,EAAE;CAC5B,KAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;CACxB,KAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;CAC9B,OAAM,IAAI,CAAC,GAAG,mBAAmB,GAAG,CAAC,CAAC;CACtC,OAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACZ,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,IAAI,CAAC,CAAC;CACb,OAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACZ,CAAC,IAAI,CAAC,CAAC;CACb,OAAM,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;OACtB;CACL,KAAI,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC;CAC9C,IAAG,CAAC;AACJ;IACE,OAAO,IAAI,CAAC;GACb;AACD;AACA;CACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;CAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;CACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;IAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;CACtC,EAAC,MAAM;CACP,GAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;GAClB;AACD;GACC;CACD,GAAEA,cAAI;CACN,GAAiC,MAAM;CACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;GACxC,CAAA;;;;;;;;CC/GD;CACA;AACA;CACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;EACA,SAAS,MAAM,CAAC,IAAI,EAAE;IACpB,IAAI,EAAE,GAAG,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC;AAC9B;CACA,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX;CACA;CACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;CACvB,KAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;CAChC,KAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CAChB,KAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CAChB,KAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CAChB,KAAI,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;CACjD,IAAG,CAAC;AACJ;CACA,GAAE,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;CAC3B;CACA,KAAI,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;CAChB,IAAG,MAAM;CACT;MACI,OAAO,IAAI,IAAI,CAAC;KACjB;AACH;CACA;CACA,GAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;CAChD,KAAI,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CACtC,KAAI,EAAE,CAAC,IAAI,EAAE,CAAC;KACX;GACF;AACD;CACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;CACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,OAAO,CAAC,CAAC;GACV;AACD;CACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;CAC1B,GAAE,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC;CAC3B,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;CAChC,OAAM,IAAI,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;CACpE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;CAC3B,KAAI,GAAG;QACD,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE;YACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW;YACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;CAC3C,MAAK,QAAQ,MAAM,KAAK,CAAC,EAAE;MACvB,OAAO,MAAM,CAAC;CAClB,IAAG,CAAC;CACJ,GAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;CACvB,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,IAAI,KAAK,EAAE;CACb,KAAI,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;CACnD,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;KACjD;IACD,OAAO,IAAI,CAAC;GACb;AACD;CACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;CAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;CACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;IAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;CACtC,EAAC,MAAM;CACP,GAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;GACpB;AACD;GACC;CACD,GAAED,cAAI;CACN,GAAiC,MAAM;CACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;GACxC,CAAA;;;;;;;;CC9ED;CACA;AACA;CACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;EACA,SAAS,MAAM,CAAC,IAAI,EAAE;IACpB,IAAI,EAAE,GAAG,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC;AAC9B;CACA;CACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;CACvB,KAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;CAClC,KAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CACvD,KAAI,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;UAC9B,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CAC1D,IAAG,CAAC;AACJ;CACA,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACX;CACA,GAAE,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;CAC3B;CACA,KAAI,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;CAChB,IAAG,MAAM;CACT;MACI,OAAO,IAAI,IAAI,CAAC;KACjB;AACH;CACA;CACA,GAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;CAChD,KAAI,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CACtC,KAAI,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE;CAC7B,OAAM,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;OAChC;CACL,KAAI,EAAE,CAAC,IAAI,EAAE,CAAC;KACX;GACF;AACD;CACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;CACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,OAAO,CAAC,CAAC;GACV;AACD;CACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;CAC1B,GAAE,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC;CAC3B,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;CAChC,OAAM,IAAI,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;CACpE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;CAC3B,KAAI,GAAG;QACD,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE;YACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW;YACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;CAC3C,MAAK,QAAQ,MAAM,KAAK,CAAC,EAAE;MACvB,OAAO,MAAM,CAAC;CAClB,IAAG,CAAC;CACJ,GAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;CACvB,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,IAAI,KAAK,EAAE;CACb,KAAI,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;CACnD,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;KACjD;IACD,OAAO,IAAI,CAAC;GACb;AACD;CACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;CAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;CACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;IAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;CACtC,EAAC,MAAM;CACP,GAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;GACpB;AACD;GACC;CACD,GAAED,cAAI;CACN,GAAiC,MAAM;CACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;GACxC,CAAA;;;;;;;;CCnFD;CACA;CACA;CACA;AACA;CACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;EACA,SAAS,MAAM,CAAC,IAAI,EAAE;CACtB,GAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AAChB;CACA;CACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;CACvB;CACA,SAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAE,CAAA,CAAC,EAAE,CAAC,CAAI;MAChC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;MAC5C,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;MACxC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;MACvC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CAC1C,KAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CAC7D,KAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MACT,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;MACnB,OAAO,CAAC,CAAC;CACb,IAAG,CAAC;AACJ;CACA,GAAE,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE;MACtB,IAAI,CAAC,CAAE,CAAG,CAAC,GAAG,GAAG;AACrB;CACA,KAAI,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;CAC7B;QACU,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;CACtB,MAAK,MAAM;CACX;CACA,OAAM,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC;CACvB,OAAM,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;CACxC,SAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;CAClC,cAAa,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;SACjD;OACF;CACL;CACA,KAAI,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACnC,KAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACtC,IAAI,CAAC,IAAI,CAAC,EAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C;CACA,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACb,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACb;CACA;MACI,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;CAC9B,OAAM,EAAE,CAAC,IAAI,EAAE,CAAC;OACX;KACF;AACH;CACA,GAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;GAChB;AACD;CACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;CACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,OAAO,CAAC,CAAC;GACV;AACD;CACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;IACxB,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,CAAC;CACvC,GAAE,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC;CAC3B,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;CAChC,OAAM,IAAI,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;CACpE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;CAC3B,KAAI,GAAG;QACD,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE;YACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW;YACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;CAC3C,MAAK,QAAQ,MAAM,KAAK,CAAC,EAAE;MACvB,OAAO,MAAM,CAAC;CAClB,IAAG,CAAC;CACJ,GAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;CACvB,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,IAAI,KAAK,EAAE;MACT,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;CACjC,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;KACjD;IACD,OAAO,IAAI,CAAC;GACb;AACD;CACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;CAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;CACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;IAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;CACtC,EAAC,MAAM;CACP,GAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;GACvB;AACD;GACC;CACD,GAAED,cAAI;CACN,GAAiC,MAAM;CACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;GACxC,CAAA;;;;;;;;CC/FD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;EACA,SAAS,MAAM,CAAC,IAAI,EAAE;CACtB,GAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AAChB;CACA;CACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;CACvB,KAAI,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;CAChB,SAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CACjC;CACA,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC;CACpC;MACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;CAC1B,KAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;CAC/B,KAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;CACjB,KAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;CACjB,KAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;CAClB,KAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;CAClB;MACI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACrB,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACb;CACA,KAAI,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;CACtC,IAAG,CAAC;AACJ;CACA,GAAE,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE;CAC1B,KAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC;CAC3C,KAAI,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE;CAC7B;QACM,CAAC,GAAG,IAAI,CAAC;QACT,IAAI,GAAG,IAAI,CAAC;CAClB,MAAK,MAAM;CACX;CACA,OAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC,GAAG,CAAC,CAAC;CACZ,OAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;OACtC;CACL;CACA,KAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;CACzC;CACA,OAAM,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;CAC7D;QACM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;CACzB,OAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;CACnB,OAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;CACpB,OAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAClB,OAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;CACpB,OAAM,IAAI,CAAC,IAAI,CAAC,EAAE;UACV,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC;CACjC,SAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACpC,SAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC1B;OACF;CACL;CACA,KAAI,IAAI,CAAC,IAAI,GAAG,EAAE;CAClB,OAAM,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;OAC1C;CACL;CACA;CACA;MACI,CAAC,GAAG,GAAG,CAAC;CACZ,KAAI,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;QAC5B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,CAAC;CAC5B,OAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;CACjC,OAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;CACnB,OAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;CACnB,OAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;CACpB,OAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;OACd;CACL;CACA,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACb,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACb,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;KACV;AACH;CACA,GAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;GAChB;AACD;CACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;CACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IAClB,OAAO,CAAC,CAAC;CACX,EACA;CACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;IACxB,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,CAAC;CACvC,GAAE,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC;CAC3B,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;CAChC,OAAM,IAAI,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;CACpE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;CAC3B,KAAI,GAAG;QACD,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE;YACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW;YACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;CAC3C,MAAK,QAAQ,MAAM,KAAK,CAAC,EAAE;MACvB,OAAO,MAAM,CAAC;CAClB,IAAG,CAAC;CACJ,GAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;CACvB,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,IAAI,KAAK,EAAE;MACT,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;CACjC,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;KACjD;IACD,OAAO,IAAI,CAAC;GACb;AACD;CACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;CAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;CACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;IAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;CACtC,EAAC,MAAM;CACP,GAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;GACrB;AACD;GACC;CACD,GAAED,cAAI;CACN,GAAiC,MAAM;CACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;GACxC,CAAA;;;;;;;;CCjJD;CACA;CACA;AACA;CACA,CAAA,CAAC,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAClC;EACA,SAAS,MAAM,CAAC,IAAI,EAAE;IACpB,IAAI,EAAE,GAAG,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC;AAC9B;CACA;CACA,GAAE,EAAE,CAAC,IAAI,GAAG,WAAW;MACnB,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CAC/C,KAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;MAC9B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CACpB,KAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;MAC9B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CACpB,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;CAC1C,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CAC3B,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;MAClC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CAC9B,IAAG,CAAC;AACJ;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACX,GAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;CACX,GAAE,EAAE,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;CACxB,GAAE,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC;AACpB;IACE,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;CACjC;MACI,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,CAAC;CACpC,KAAI,EAAE,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;CACpB,IAAG,MAAM;CACT;MACI,OAAO,IAAI,IAAI,CAAC;KACjB;AACH;CACA;CACA,GAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;CAChD,KAAI,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CACtC,KAAI,EAAE,CAAC,IAAI,EAAE,CAAC;KACX;GACF;AACD;CACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;CACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,OAAO,CAAC,CAAC;CACX,EACA;CACA,CAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;CAC1B,GAAE,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC;CAC3B,OAAM,KAAK,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;CAChC,OAAM,IAAI,GAAG,WAAW,EAAE,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC;CACpE,GAAE,IAAI,CAAC,MAAM,GAAG,WAAW;CAC3B,KAAI,GAAG;QACD,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE;YACtB,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,WAAW;YACrC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;CAC3C,MAAK,QAAQ,MAAM,KAAK,CAAC,EAAE;MACvB,OAAO,MAAM,CAAC;CAClB,IAAG,CAAC;CACJ,GAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC;CACvB,GAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,IAAI,KAAK,EAAE;CACb,KAAI,IAAI,OAAO,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;CACnD,KAAI,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,GAAE;KACjD;IACD,OAAO,IAAI,CAAC;GACb;AACD;CACA,CAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;CAC9B,GAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;CACxB,EAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE;IAC/B,MAAM,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;CACtC,EAAC,MAAM;CACP,GAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;GACpB;AACD;GACC;CACD,GAAED,cAAI;CACN,GAAiC,MAAM;CACvC,GAAE,CAAC,OAAOC,SAAM,KAAK,UAAU,CAAU;GACxC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CC5ED,CAAA,CAAC,UAAU,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE;CAC/B;CACA;CACA;AACA;EACA,IAAI,KAAK,GAAG,GAAG;MACX,MAAM,GAAG,CAAC;MACV,MAAM,GAAG,EAAE;MACX,OAAO,GAAG,QAAQ;MAClB,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;MACpC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;CACtC,KAAI,QAAQ,GAAG,YAAY,GAAG,CAAC;CAC/B,KAAI,IAAI,GAAG,KAAK,GAAG,CAAC;CACpB,KAAI,UAAU,CAAC;AACf;CACA;CACA;CACA;CACA;CACA,CAAA,SAAS,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE;CAC7C,GAAE,IAAI,GAAG,GAAG,EAAE,CAAC;CACf,GAAE,OAAO,GAAG,CAAC,OAAO,IAAI,IAAI,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,OAAO,IAAI,EAAE,CAAC,CAAC;AACpE;CACA;CACA,GAAE,IAAI,SAAS,GAAG,MAAM,CAAC,OAAO;MAC5B,OAAO,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;CAC5C,KAAI,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACjD;CACA;IACE,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B;CACA;CACA;IACE,IAAI,IAAI,GAAG,WAAW;MACpB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;UAClB,CAAC,GAAG,UAAU;UACd,CAAC,GAAG,CAAC,CAAC;CACd,KAAI,OAAO,CAAC,GAAG,YAAY,EAAE;QACvB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC;QACpB,CAAC,IAAI,KAAK,CAAC;QACX,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACf;CACL,KAAI,OAAO,CAAC,IAAI,QAAQ,EAAE;QACpB,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,IAAI,CAAC,CAAC;QACP,CAAC,MAAM,CAAC,CAAC;OACV;CACL,KAAI,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;CACvB,IAAG,CAAC;AACJ;CACA,GAAE,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAE;CACnD,GAAE,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,GAAE;CAC7D,GAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACrB;CACA;IACE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACjC;CACA;CACA,GAAE,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ;QAC5B,SAAS,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE;UACxC,IAAI,KAAK,EAAE;CACnB;CACA,WAAU,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE;CAC7C;CACA,WAAU,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,GAAE;WACnD;AACT;CACA;CACA;CACA,SAAQ,IAAI,YAAY,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,EAAE;AAChE;CACA;CACA;eACa,OAAO,IAAI,CAAC;SAClB;CACP,GAAE,IAAI;CACN,GAAE,SAAS;IACT,QAAQ,IAAI,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC;CACvD,GAAE,OAAO,CAAC,KAAK,CAAC,CAAC;GAChB;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;EACA,SAAS,IAAI,CAAC,GAAG,EAAE;CACnB,GAAE,IAAI,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM;QACtB,EAAE,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;AAC3D;CACA;CACA,GAAE,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;AACpC;CACA;CACA,GAAE,OAAO,CAAC,GAAG,KAAK,EAAE;CACpB,KAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;KACZ;IACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;CAC9B,KAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAC5D,KAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACV;AACH;CACA;CACA,GAAE,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,KAAK,EAAE;CAC1B;CACA,KAAI,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC;CAChB,SAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;MACjC,OAAO,KAAK,EAAE,EAAE;CACpB,OAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAChC,OAAM,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;OACzE;CACL,KAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;MACnB,OAAO,CAAC,CAAC;CACb;CACA;CACA;KACG,EAAE,KAAK,CAAC,CAAC;GACX;AACD;CACA;CACA;CACA;CACA;CACA,CAAA,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;CACpB,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACZ,GAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IAClB,OAAO,CAAC,CAAC;CACX,EACA;CACA;CACA;CACA;CACA;CACA,CAAA,SAAS,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE;CAC7B,GAAE,IAAI,MAAM,GAAG,EAAE,EAAE,GAAG,IAAI,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC;CAC5C,GAAE,IAAI,KAAK,IAAI,GAAG,IAAI,QAAQ,EAAE;CAChC,KAAI,KAAK,IAAI,IAAI,GAAG,EAAE;QAChB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE;OACjE;KACF;CACH,GAAE,QAAQ,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,GAAG,IAAI,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE;GACtE;AACD;CACA;CACA;CACA;CACA;CACA;CACA,CAAA,SAAS,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;CAC3B,GAAE,IAAI,UAAU,GAAG,IAAI,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;CAC3C,GAAE,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE;CAChC,KAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;QACX,IAAI,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACvE;CACH,GAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;GACtB;AACD;CACA;CACA;CACA;CACA;CACA;CACA,CAAA,SAAS,QAAQ,GAAG;CACpB,GAAE,IAAI;MACF,IAAI,GAAG,CAAC;MACR,IAAI,UAAU,KAAK,GAAG,GAAG,UAAU,CAAC,WAAW,CAAC,EAAE;CACtD;CACA,OAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;CACvB,MAAK,MAAM;CACX,OAAM,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;CAClC,OAAM,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC;OACzD;CACL,KAAI,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;KACtB,CAAC,OAAO,CAAC,EAAE;CACd,KAAI,IAAI,OAAO,GAAG,MAAM,CAAC,SAAS;CAClC,SAAQ,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;CAC7C,KAAI,OAAO,CAAC,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;KACpE;GACF;AACD;CACA;CACA;CACA;CACA;EACA,SAAS,QAAQ,CAAC,CAAC,EAAE;IACnB,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;GACxC;AACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;EACA,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;AAC5B;CACA;CACA;CACA;CACA;EACA,IAAmC,MAAM,CAAC,OAAO,EAAE;IACjD,MAAA,CAAA,OAAA,GAAiB,UAAU,CAAC;CAC9B;CACA,GAAE,IAAI;MACF,UAAU,GAAG,UAAiB,CAAC;CACnC,IAAG,CAAC,OAAO,EAAE,EAAE,EAAE;GAChB,MAEM;CACP;IACE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC;GACrC;AACD;AACA;CACA;GACC;CACD;CACA;IACE,CAAC,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,GAAGD,cAAI;CAC7C,GAAE,EAAE;CACJ,GAAE,IAAI;GACL,CAAA;;;;;CC5PD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;AACA;CACA;CACA;CACA;CACA,IAAI,IAAI,GAAGE,WAAqB,CAAC;AACjC;CACA;CACA;CACA;CACA,IAAI,MAAM,GAAGC,aAAuB,CAAC;AACrC;CACA;CACA;CACA;CACA,IAAI,MAAM,GAAGC,aAAuB,CAAC;AACrC;CACA;CACA;CACA;CACA;CACA;CACA;CACA,IAAI,SAAS,GAAGC,gBAA0B,CAAC;AAC3C;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,IAAI,OAAO,GAAGC,cAAwB,CAAC;AACvC;CACA;CACA;CACA;CACA;CACA;CACA,IAAI,MAAM,GAAGC,aAAuB,CAAC;AACrC;CACA;CACA;CACA,IAAI,EAAE,GAAGC,iBAAuB,CAAC;AACjC;CACA,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;CACf,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;CACnB,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;CACnB,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC;CACzB,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC;CACrB,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;AACnB;CACA,IAAA,UAAc,GAAG,EAAE;;CC3DnB;;;;;;;;;;;;;;;;CAgBG;CAiBH;;;;;;;;;;;;;;;;CAgBG;CACa,SAAA,OAAO,CAAC,KAAU,EAAE,KAAgC,EAAA;CAElE,IAAA,OAAO,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CACvC,CAAC;CAED;;;;;CAKG;CACH,SAAS,eAAe,CACpB,KAAU,EAAE,KAAgC,EAC5C,IAAA,GAAsB,IAAI,GAAG,EAAE,EAAE,WAAuB,GAAA,IAAI,GAAG,EAAE,EAAA;KAEnE,IAAI,KAAK,IAAI,IAAI,EAAE;CACjB,QAAA,OAAO,IAAI,CAAC;CACb,KAAA;KACD,IAAI,OAAO,IAAI,KAAK,UAAU,IAAI,KAAK,YAAY,IAAI,EAAE;CACvD,QAAA,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;CACtB,KAAA;CAED,IAAA,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;CAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;CAC3D,KAAA;CACD,IAAA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;CACnB,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CACxB,KAAA;CACD,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;KAE5B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;CAC3C,QAAA,MAAM,IAAI,KAAK,CACX,mEAAmE,CAAC,CAAC;CAC1E,KAAA;CAED,IAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;SACnB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;SAC9B,OAAO,MAAM,CAAC,KAAK,CAAC;CACrB,KAAA;CAAM,SAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;;CAE5B,QAAA,MAAM,cAAc,GAAc,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;CACjE,QAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CACvB,QAAA,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;CACrB,YAAA,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;CACvB,YAAA,MAAM,WAAW,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;CACrE,YAAA,cAAc,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;CACjC,SAAA;CACD,QAAA,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC1B,IAAI,KAAK,CAAC,SAAS,EAAE;CACnB,YAAA,cAAc,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;CAC5C,SAAA;CACD,QAAA,OAAO,cAAc,CAAC;CACvB,KAAA;CAAM,SAAA;CACL,QAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,KAAK,CAAA,CAAE,CAAC,CAAC;CACnE,KAAA;CACH,CAAC;CAED;CACA;CAEA;;;;;;;;;;;;;;;;;;;;;CAqBG;UACa,OAAO,CACnB,MAAa,EAAE,QAAsC,SAAS,EAAA;CAChE,IAAA,OAAO,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;CACxC,CAAC;CAED;;;CAGG;CACH,SAAS,eAAe,CACpB,MAAa,EAAE,KAAmC,EAClD,WAAA,GAAuB,IAAI,GAAG,EAAE,EAAA;;;CAGlC,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;CACxB,IAAA,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;CAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;CAC3D,KAAA;CACD,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;KAE7B,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;CAC3C,QAAA,MAAM,IAAI,KAAK,CACX,mEAAmE,CAAC,CAAC;CAC1E,KAAA;CAED,IAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;SACnB,OAAO,MAAM,CAAC,KAAK,CAAC;CACrB,KAAA;CAAM,SAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;;CAE5B,QAAA,MAAM,cAAc,GAAc,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;CACjE,QAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CACvB,QAAA,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;CACrB,YAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACvC,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;CAClE,YAAA,cAAc,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC;CACjC,SAAA;CACD,QAAA,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CAC1B,QAAA,OAAO,cAAc,CAAC;CACvB,KAAA;CAAM,SAAA;CACL,QAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,KAAK,CAAA,CAAE,CAAC,CAAC;CACnE,KAAA;CACH,CAAC;CAED;CACM,SAAU,SAAS,CAAC,CAAQ,EAAA;KAChC,IAAI,CAAC,KAAK,IAAI,EAAE;CACd,QAAA,OAAO,IAAI,CAAC;CACb,KAAA;;CAGD,IAAA,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SACpB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;CACrC,KAAA;CAAM,SAAA;SACL,OAAO,EAAC,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;CACnC,KAAA;CACH,CAAC;CAaD;;;;;;;;;;;;;;;;;;;;;CAqBG;CACI,eAAe,kBAAkB,CACpC,KAAU,EAAE,KAAqC,EAAA;CACnD,IAAA,MAAM,IAAI,GAAkB,IAAI,GAAG,EAAE,CAAC;;CAGtC,IAAA,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;;;;;CAMpC,IAAA,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE;SACzC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC5B,IAAIC,aAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;CAC5B,YAAA,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC;CAChC,YAAA,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;CAC5B,SAAA;CACF,KAAA;;;;KAKD,MAAM,MAAM,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;CACnD,IAAA,OAAO,MAAM,CAAC;CAChB,CAAC;CAED;;;;CAIG;CACH;CACM,SAAU,UAAU,CAAC,GAAQ,EAAA;KACjC,IAAI,aAAa,GAAG,KAAK,CAAC;KAC1B,IAAIA,aAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;CAC9B,QAAA,aAAa,GAAG,GAAG,YAAY,WAAW,CAAC;CAC5C,KAAA;CAAM,SAAA;;SAEL,MAAM,EAAC,aAAa,EAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;CAClD,QAAA,aAAa,GAAG,GAAG,YAAY,aAAa,CAAC;CAC9C,KAAA;CACD,IAAA,OAAO,GAAG,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;CAC5C,SAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;CAClB,aAAC,OAAO,GAAG,KAAK,QAAQ,IAAI,EAAE,GAAG,YAAYA,aAAE,CAAC,MAAM,CAAC;iBACtD,EAAE,GAAG,YAAY,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;CACtD,CAAC;CAED;;;;;;;CAOG;CACH;CACM,SAAU,YAAY,CAAC,GAAQ,EAAA;CACnC,IAAA,OAAO,GAAG,IAAI,IAAI,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC;CACxD,SAAC,OAAO,GAAG,KAAK,QAAQ,KAAK,GAAG,YAAYA,aAAE,CAAC,MAAM,CAAC,CAAC;CACvD,QAAAA,aAAE,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;CAChC,CAAC;CAED;;;CAGG;CACH,SAAS,WAAW,CAAC,KAAU,EAAA;KAC7B,QACI,KAAK,KAAK,IAAI;UACb,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,CAAC,EAAE;CAClE;;CCpSA;;;;;;;;;;;;;;;;CAgBG;CAKG,SAAU,SAAS,CAAI,SAAY,EAAA;CACvC,IAAA,OAAO,OAAO,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;CAC3C,CAAC;CAED;CACA,SAAS,aAAa,CAAC,IAAS,EAAA;CAC9B,IAAA,IAAI,IAAI,YAAYA,aAAE,CAAC,MAAM,EAAE;CAC7B,QAAA,QAAQ,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,EAAC,EAAE;CAChD,KAAA;CAAM,SAAA,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;SAC3B,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;CACrC,KAAA;CAAM,SAAA;SACL,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;CACtC,KAAA;CACH;;CClCA;;;;;;;;;;;;;;;;CAgBG;CAEH;;CAEG;OACU,UAAU,CAAA;CAUrB;;;CAGG;CACH,IAAA,WAAA,CAAmB,QAAgB,EAAA;SAAhB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;;;;CAVzB,QAAA,IAAA,CAAA,KAAK,GAAG,CAAC,CAAC;CACV,QAAA,IAAA,CAAA,GAAG,GAAG,CAAC,CAAC;SAUhB,IAAI,QAAQ,IAAI,IAAI,EAAE;CACpB,YAAA,MAAM,IAAI,UAAU,CAAC,kDAAkD,CAAC,CAAC;CAC1E,SAAA;SACD,IAAI,QAAQ,GAAG,CAAC,EAAE;CAChB,YAAA,MAAM,IAAI,UAAU,CAAC,4CAA4C,CAAC,CAAC;CACpE,SAAA;SACD,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,CAAI,QAAQ,CAAC,CAAC;CACnC,QAAA,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,QAAQ,CAAC;MACrC;CAED;;CAEG;CACO,IAAA,IAAI,CAAC,KAAa,EAAA;;SAE1B,OAAO,KAAK,GAAG,CAAC,EAAE;CAChB,YAAA,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC;CAC/B,SAAA;CACD,QAAA,OAAO,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;MACrC;CAES,IAAA,GAAG,CAAC,KAAa,EAAA;SACzB,IAAI,KAAK,GAAG,CAAC,EAAE;CACb,YAAA,MAAM,IAAI,UAAU,CAAC,sCAAsC,CAAC,CAAC;CAC9D,SAAA;SACD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;MACzC;KAES,GAAG,CAAC,KAAa,EAAE,KAAQ,EAAA;SACnC,IAAI,KAAK,GAAG,CAAC,EAAE;CACb,YAAA,MAAM,IAAI,UAAU,CAAC,sCAAsC,CAAC,CAAC;CAC9D,SAAA;SACD,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;MAC1C;CAED;;CAEG;KACH,MAAM,GAAA;SACJ,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;SACnC,IAAI,MAAM,GAAG,CAAC,EAAE;CACd,YAAA,MAAM,GAAG,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;CACxC,SAAA;CACD,QAAA,OAAO,MAAM,CAAC;MACf;CAED;;;;CAIG;KACH,MAAM,GAAA;SACJ,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;MACxC;CAED;;;;CAIG;KACH,OAAO,GAAA;CACL,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;MAC5B;CAED;;CAEG;CACH,IAAA,IAAI,CAAC,KAAQ,EAAA;CACX,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;CACjB,YAAA,MAAM,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC;CAC9C,SAAA;SACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;CAC1B,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;MACpC;CAED;;CAEG;CACH,IAAA,OAAO,CAAC,MAAW,EAAA;CACjB,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;CAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAClB,SAAA;MACF;CAED;;CAEG;KACH,GAAG,GAAA;CACD,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;CAClB,YAAA,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;CAC/C,SAAA;CACD,QAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACnC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;CAC9B,QAAA,OAAO,MAAM,CAAC;MACf;CAED;;CAEG;CACH,IAAA,OAAO,CAAC,KAAQ,EAAA;CACd,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;CACjB,YAAA,MAAM,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC;CAC9C,SAAA;CACD,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;SACvC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;MAC7B;CAED;;CAEG;KACH,KAAK,GAAA;CACH,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;CAClB,YAAA,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;CAC/C,SAAA;SACD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACpC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;CAChC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;CACvC,QAAA,OAAO,MAAM,CAAC;MACf;CAED;;;;;;;;CAQG;CACH,IAAA,aAAa,CAAC,aAAqB,EAAA;CACjC,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;CAClB,YAAA,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;CAC/C,SAAA;CACD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,CAAC;SACpD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;SAC/B,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;CAC5B,QAAA,OAAO,MAAM,CAAC;MACf;CACF;;CC/KD;;;;;;;;;;;;;;;;CAgBG;CAIH,MAAa,iBAAqB,SAAQ,UAAa,CAAA;CAGrD;;CAEG;CACH,IAAA,WAAA,GAAA;CACE,QAAA,KAAK,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;MAC3C;KAEQ,MAAM,GAAA;CACb,QAAA,OAAO,KAAK,CAAC;MACd;CAEQ,IAAA,IAAI,CAAC,KAAQ,EAAA;CACpB,QAAA,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE;aAClB,IAAI,CAAC,MAAM,EAAE,CAAC;CACf,SAAA;CACD,QAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;MACnB;CAEQ,IAAA,OAAO,CAAC,KAAQ,EAAA;CACvB,QAAA,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE;aAClB,IAAI,CAAC,MAAM,EAAE,CAAC;CACf,SAAA;CACD,QAAA,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;MACtB;CAED;;CAEG;KACK,MAAM,GAAA;CACZ,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;CACtC,QAAA,MAAM,OAAO,GAAG,IAAI,KAAK,CAAI,WAAW,CAAC,CAAC;CAC1C,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;;;SAI1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;CAC5B,YAAA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;CAClD,SAAA;CAED,QAAA,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;CACpB,QAAA,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;SAC5B,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;CACzC,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;CACf,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;MAChB;;CA9Cc,iBAAgB,CAAA,gBAAA,GAAG,EAAE;;CCrBtC;;;;;;;;;;;;;;;;CAgBG;CAgBH;CACA;CACA;CAEA;;CAEG;CACG,SAAU,iBAAiB,CAAI,KAAU,EAAA;CAC7C,IAAA,OAAO,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;CAClC,CAAC;CAUD;;;;;;;;;;;;CAYG;CACG,SAAU,oBAAoB,CAChC,IACiD,EAAA;CACnD,IAAA,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;CACxC,CAAC;CAED;;;;;;;;;;;CAWG;CACa,SAAA,wBAAwB,CACpC,aAA4C,EAC5C,gBAAwC,EAAA;CAC1C,IAAA,OAAO,IAAI,eAAe,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;CAC9D,CAAC;CAyBD;;;;;;;;;;;;;;;;;;;;;;;CAuBG;CACG,SAAU,kBAAkB,CAC9B,SAA4B,EAC5B,YAAgC,GAAA,eAAe,CAAC,IAAI,EAAA;CACtD,IAAA,OAAO,IAAI,WAAW,CAAI,SAAS,EAAE,YAAY,CAAC,CAAC;CACrD,CAAC;CAED;;;;;;CAMG;OACmB,YAAY,CAAA;CAgBhC;;;;;;;CAOG;CACH,IAAA,MAAM,OAAO,GAAA;SACX,MAAM,MAAM,GAAQ,EAAE,CAAC;CACvB,QAAA,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;CAC1B,QAAA,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE;CACd,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;CACrB,YAAA,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;CACvB,SAAA;CACD,QAAA,OAAO,MAAM,CAAC;MACf;CAED;;;;;;;;;;CAUG;CACH,IAAA,MAAM,cAAc,GAAA;SAClB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;SAClC,MAAM,MAAM,GAAQ,EAAE,CAAC;CACvB,QAAA,IAAI,CAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;CAC5B,QAAA,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE;CACd,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;CACrB,YAAA,CAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;CACzB,SAAA;CACD,QAAA,OAAO,MAAM,CAAC;MACf;CAED;;;;;;CAMG;CACH,IAAA,MAAM,YAAY,GAAA;CAChB,QAAA,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;CAC1B,QAAA,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE;CACd,YAAA,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;CACvB,SAAA;MACF;CAED;;;;;;CAMG;KACH,MAAM,YAAY,CAAC,SAA4B,EAAA;CAC7C,QAAA,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;SAC1B,IAAI,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SACxC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,EAAE;CAClC,YAAA,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;CACtB,YAAA,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;CACrC,SAAA;MACF;CAED;;;;;;;;;;;CAWG;CACH,IAAA,YAAY,CAAC,OAAkC,EAAA;CAC7C,QAAA,OAAO,IAAI,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;MACrD;;CAID;;;;;;;CAOG;CACH,IAAA,MAAM,CAAC,SAAgC,EAAA;CACrC,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;MAC5C;CAED;;;;;;;CAOG;CACH,IAAA,GAAG,CAAI,SAA0B,EAAA;CAC/B,QAAA,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;MACzC;CAED;;;;;;;CAOG;CACH,IAAA,QAAQ,CAAI,SAAmC,EAAA;CAC7C,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;MAC9C;CAED;;;;;;;CAOG;CACH,IAAA,cAAc,CAAI,SAAmC,EAAA;SACnD,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;MACvD;CAED;;;;;;;CAOG;CACH,IAAA,OAAO,CAAI,SAA4B,EAAA;CACrC,QAAA,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;MAC7C;CAED;;;;CAIG;KACH,MAAM,YAAY,CAAC,CAAqB,EAAA;SACtC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;MACnC;CAED;;;;;;CAMG;KACH,MAAM,aAAa,CAAC,CAAiC,EAAA;CACnD,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;MAC/D;CAED;;;;;;;;;;;;;;;;;CAiBG;CACH,IAAA,aAAa,CAAC,SAAiB,EAAE,cAAc,GAAG,IAAI,EAAA;SACpD,OAAO,IAAI,qBAAqB,CAAC,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;MACnE;CAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BG;CACH,IAAA,gBAAgB,CACZ,SAAiB,EAAE,cAAc,GAAG,IAAI;;CAExC,IAAA,KAAA,GAAsC,SAAS,EAAA;;SAGjD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;;;CAGjE,QAAA,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;MAC/C;CAED;;;;;;;;;CASG;KACH,WAAW,CACP,QAAyB,EACzB,gBAAwC,EAAA;CAC1C,QAAA,OAAO,IAAI,eAAe,CACtB,iBAAiB,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;MAC5D;CAED;;;;;;CAMG;CACH,IAAA,IAAI,CAAC,KAAa,EAAA;CAChB,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE;CAC9B,YAAA,OAAO,IAAI,CAAC;CACb,SAAA;CACD,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;MACtC;CAED;;;;;CAKG;CACH,IAAA,IAAI,CAAC,KAAa,EAAA;CAChB,QAAA,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE;CAC9B,YAAA,OAAO,IAAI,CAAC;CACb,SAAA;CACD,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;MACtC;CAED;;;;;;;;CAQG;CACH,IAAA,QAAQ,CAAC,UAAkB,EAAA;CACzB,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;MAC/C;;CAID;;;;;;;CAOG;KACH,OAAO,CAAC,UAAkB,EAAE,IAAa,EAAA;SACvC,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;MACpD;CAED;;;CAGG;KACH,MAAM,GAAA;CACJ,QAAA,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;MACjC;CACF,CAAA;CAED;CACA;CACA;CACA;CACA;CAEA;CACA;CAEA,MAAM,aAAiB,SAAQ,YAAe,CAAA;CAE5C,IAAA,WAAA,CAAsB,KAAU,EAAA;CAC9B,QAAA,KAAK,EAAE,CAAC;SADY,IAAK,CAAA,KAAA,GAAL,KAAK,CAAK;SADxB,IAAI,CAAA,IAAA,GAAG,CAAC,CAAC;MAGhB;KAED,OAAO,GAAA;CACL,QAAA,OAAO,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC;MAC9C;CAED,IAAA,MAAM,IAAI,GAAA;SACR,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;aAClC,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,SAAA;SACD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACnC,IAAI,CAAC,IAAI,EAAE,CAAC;CACZ,QAAA,OAAO,EAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;MAC9C;CACF,CAAA;CAED,MAAM,oBAAwB,SAAQ,YAAe,CAAA;CACnD,IAAA,WAAA,CACc,MAA2D,EAAA;CACvE,QAAA,KAAK,EAAE,CAAC;SADI,IAAM,CAAA,MAAA,GAAN,MAAM,CAAqD;MAExE;KAED,OAAO,GAAA;CACL,QAAA,OAAO,eAAe,CAAC;MACxB;CAED,IAAA,MAAM,IAAI,GAAA;SACR,IAAI;CACF,YAAA,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;CACtB,SAAA;CAAC,QAAA,OAAO,CAAC,EAAE;;CAEV,YAAA,CAAC,CAAC,OAAO;CACL,gBAAA,CAAA,gDAAA,EAAmD,CAAC,CAAC,OAAO,CAAA,CAAE,CAAC;CACnE,YAAA,MAAM,CAAC,CAAC;CACT,SAAA;MACF;CACF,CAAA;CAED,MAAM,cAAkB,SAAQ,YAAe,CAAA;CAK7C,IAAA,WAAA,CAAsB,QAAyB,EAAA;CAC7C,QAAA,KAAK,EAAE,CAAC;SADY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;CAE7C,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;MAC7D;KAED,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC;MAC/C;CAED,IAAA,MAAM,IAAI,GAAA;;;;;CAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;MACtB;CAEO,IAAA,MAAM,UAAU,GAAA;CACtB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;MAC7B;CACF,CAAA;CAED,MAAM,YAAgB,SAAQ,YAAe,CAAA;KAQ3C,WAAsB,CAAA,QAAyB,EAAY,QAAgB,EAAA;CACzE,QAAA,KAAK,EAAE,CAAC;SADY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;SAAY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;;SAF3E,IAAK,CAAA,KAAA,GAAG,CAAC,CAAC;CAIR,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;MAC7D;KAED,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;MAC7C;CAED,IAAA,MAAM,IAAI,GAAA;;;;;CAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;MACtB;CAEO,IAAA,MAAM,UAAU,GAAA;;;;;SAKtB,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE;aACnC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;;aAE3C,IAAI,OAAO,CAAC,IAAI,EAAE;CAChB,gBAAA,OAAO,OAAO,CAAC;CAChB,aAAA;CACD,YAAAA,aAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAW,CAAC,CAAC;CACjC,SAAA;CACD,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;MAC7B;CACF,CAAA;CAED,MAAM,YAAgB,SAAQ,YAAe,CAAA;KAE3C,WAAsB,CAAA,QAAyB,EAAY,QAAgB,EAAA;CACzE,QAAA,KAAK,EAAE,CAAC;SADY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;SAAY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;SAD3E,IAAK,CAAA,KAAA,GAAG,CAAC,CAAC;MAGT;KAED,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;MAC7C;CAED,IAAA,MAAM,IAAI,GAAA;SACR,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE;aACjC,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,SAAA;CACD,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;MAC7B;CACF,CAAA;CAED;CACA;CACA;CACA,MAAM,qBAAyB,SAAQ,YAAiB,CAAA;CAKtD,IAAA,WAAA,CACc,QAAyB,EAAY,SAAiB,EACtD,uBAAuB,IAAI,EAAA;CACvC,QAAA,KAAK,EAAE,CAAC;SAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;SAAY,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;SACtD,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAAO;CAEvC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;MAC7D;KAED,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAC;MACtD;CAED,IAAA,MAAM,IAAI,GAAA;;;;;CAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;MACtB;CAEO,IAAA,MAAM,UAAU,GAAA;SACtB,MAAM,KAAK,GAAQ,EAAE,CAAC;CACtB,QAAA,OAAO,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;aACpC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;aACxC,IAAI,IAAI,CAAC,IAAI,EAAE;iBACb,IAAI,IAAI,CAAC,oBAAoB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;qBACjD,OAAO,EAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;CACpC,iBAAA;iBACD,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,aAAA;CACD,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CACxB,SAAA;SACD,OAAO,EAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;MACpC;CACF,CAAA;CAED,MAAM,cAAkB,SAAQ,YAAe,CAAA;KAK7C,WACc,CAAA,QAAyB,EACzB,SAAgC,EAAA;CAC5C,QAAA,KAAK,EAAE,CAAC;SAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;SACzB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAuB;CAE5C,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;MAC7D;KAED,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC;MAC/C;CAED,IAAA,MAAM,IAAI,GAAA;;;;;CAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;MACtB;CAEO,IAAA,MAAM,UAAU,GAAA;CACtB,QAAA,OAAO,IAAI,EAAE;aACX,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;CACxC,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;CAC3C,gBAAA,OAAO,IAAI,CAAC;CACb,aAAA;CACD,YAAAA,aAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;CAC9B,SAAA;MACF;CACF,CAAA;CAED,MAAM,WAAkB,SAAQ,YAAe,CAAA;KAC7C,WACc,CAAA,QAAyB,EACzB,SAA0B,EAAA;CACtC,QAAA,KAAK,EAAE,CAAC;SAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;SACzB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAiB;MAEvC;KAED,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,SAAS,CAAC;MAC5C;CAED,IAAA,MAAM,IAAI,GAAA;SACR,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;SACxC,IAAI,IAAI,CAAC,IAAI,EAAE;aACb,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,SAAA;CACD,QAAA,MAAM,YAAY,GAAGA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;;;;;;;SAO5E,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1C,MAAM,aAAa,GAAGA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAY,CAAC,CAAC;;;CAIzE,QAAA,KAAK,MAAM,CAAC,IAAI,YAAY,EAAE;aAC5B,IAAI,CAACA,aAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;iBACpD,CAAC,CAAC,OAAO,EAAE,CAAC;CACb,aAAA;CACF,SAAA;SACD,OAAO,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;MACrC;CACF,CAAA;CAED,MAAM,yBAA6B,SAAQ,YAAe,CAAA;KAExD,WACc,CAAA,QAAyB,EACzB,OAAkC,EAAA;CAC9C,QAAA,KAAK,EAAE,CAAC;SAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;SACzB,IAAO,CAAA,OAAA,GAAP,OAAO,CAA2B;SAHhD,IAAK,CAAA,KAAA,GAAG,CAAC,CAAC;CAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;MAC7D;KAED,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,kBAAkB,CAAC;MACrD;CAMD,IAAA,MAAM,IAAI,GAAA;;;;;CAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;MACtB;CAED,IAAA,MAAM,UAAU,GAAA;CACd,QAAA,OAAO,IAAI,EAAE;aACX,IAAI;CACF,gBAAA,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;CACnC,aAAA;CAAC,YAAA,OAAO,CAAC,EAAE;CACV,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;qBACpB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,iBAAA;;;;;CAMF,aAAA;CACF,SAAA;MACF;CACF,CAAA;CAED,MAAM,gBAAuB,SAAQ,YAAe,CAAA;KAClD,WACc,CAAA,QAAyB,EACzB,SAAmC,EAAA;CAC/C,QAAA,KAAK,EAAE,CAAC;SAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;SACzB,IAAS,CAAA,SAAA,GAAT,SAAS,CAA0B;MAEhD;KAED,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,cAAc,CAAC;MACjD;CAED,IAAA,MAAM,IAAI,GAAA;SACR,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;SACxC,IAAI,IAAI,CAAC,IAAI,EAAE;aACb,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,SAAA;CACD,QAAA,MAAM,YAAY,GAAGA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;;;;;;;SAO5E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAChD,MAAM,aAAa,GAAGA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,MAAY,CAAC,CAAC;;;CAIzE,QAAA,KAAK,MAAM,CAAC,IAAI,YAAY,EAAE;aAC5B,IAAI,CAACA,aAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;iBACpD,CAAC,CAAC,OAAO,EAAE,CAAC;CACb,aAAA;CACF,SAAA;SACD,OAAO,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;MACrC;CACF,CAAA;CAED;CACA;CAEA;;;;;;;CAOG;CACG,MAAgB,iBAAqB,SAAQ,YAAe,CAAA;CAQhE,IAAA,WAAA,GAAA;CACE,QAAA,KAAK,EAAE,CAAC;CACR,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAiB,EAAK,CAAC;CAC9C,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;MAC7D;CAED,IAAA,MAAM,IAAI,GAAA;;;;;CAKR,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;MACtB;CAgBD,IAAA,MAAM,UAAU,GAAA;;;;SAId,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;;CAEtC,YAAA,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE;iBACtB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,aAAA;CACF,SAAA;CACD,QAAA,OAAO,EAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;MACvD;CACF,CAAA;CACD,MAAM,eAAsB,SAAQ,iBAAoB,CAAA;KACtD,WACc,CAAA,QAAyB,EACzB,SAA4B,EAAA;CACxC,QAAA,KAAK,EAAE,CAAC;SAFI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;SACzB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAmB;MAEzC;KAED,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,aAAa,CAAC;MAChD;CAED,IAAA,MAAM,IAAI,GAAA;SACR,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;SACxC,IAAI,IAAI,CAAC,IAAI,EAAE;CACb,YAAA,OAAO,KAAK,CAAC;CACd,SAAA;CACD,QAAA,MAAM,YAAY,GAAGA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAW,CAAC,CAAC;;;;;;SAM5E,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC/C,MAAM,aAAa,GACfA,aAAE,CAAC,WAAW,CAAC,qBAAqB,CAAC,WAAiB,CAAC,CAAC;CAC5D,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;;;CAItC,QAAA,KAAK,MAAM,CAAC,IAAI,YAAY,EAAE;aAC5B,IAAI,CAACA,aAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE;iBACpD,CAAC,CAAC,OAAO,EAAE,CAAC;CACb,aAAA;CACF,SAAA;CAED,QAAA,OAAO,IAAI,CAAC;MACb;CACF,CAAA;CAED;;;;;;;;CAQG;CACG,MAAO,eAAmB,SAAQ,YAAe,CAAA;KASrD,WACI,CAAA,SAAwC,EACvB,gBAAwC,EAAA;CAC3D,QAAA,KAAK,EAAE,CAAC;SADW,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAwB;;;SARrD,IAAQ,CAAA,QAAA,GAA+B,IAAI,CAAC;;SAG5C,IAAQ,CAAA,QAAA,GAAoB,IAAI,CAAC;CAOvC,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;MAChC;KAED,OAAO,GAAA;SACL,MAAM,iBAAiB,GAAG,6CAA6C,CAAC;SACxE,OAAO,CAAA,EAAG,iBAAiB,CAAA,WAAA,CAAa,CAAC;MAC1C;CAED,IAAA,MAAM,IAAI,GAAA;SACR,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAClD,OAAO,IAAI,CAAC,QAAQ,CAAC;MACtB;KAEO,MAAM,aAAa,CAAC,QAAoC,EAAA;;;;;;CAO9D,QAAA,MAAM,QAAQ,CAAC;CACf,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;aACzB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;aACvD,IAAI,cAAc,CAAC,IAAI,EAAE;;iBAEvB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,aAAA;CACD,YAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC;CACrC,YAAA,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE;CACjC,gBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;CACnE,aAAA;CACF,SAAA;SACD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;SAC9C,IAAI,UAAU,CAAC,IAAI,EAAE;CACnB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;CACrB,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;CACrC,SAAA;CACD,QAAA,OAAO,UAAU,CAAC;MACnB;CACF,CAAA;CAED,IAAY,eAIX,CAAA;CAJD,CAAA,UAAY,eAAe,EAAA;CACzB,IAAA,eAAA,CAAA,eAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;CACJ,IAAA,eAAA,CAAA,eAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;KACR,eAAO,CAAA,eAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;CACT,CAAC,EAJW,eAAe,KAAf,eAAe,GAI1B,EAAA,CAAA,CAAA,CAAA;CAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BG;CACH,MAAM,WAA0C,SAAQ,YAAe,CAAA;CAIrE,IAAA,WAAA,CACuB,SAA4B,EAC5B,YAAgC,GAAA,eAAe,CAAC,IAAI,EAAA;CACzE,QAAA,KAAK,EAAE,CAAC;SAFa,IAAS,CAAA,SAAA,GAAT,SAAS,CAAmB;SAC5B,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAwC;SALnE,IAAK,CAAA,KAAA,GAAG,CAAC,CAAC;SACV,IAAc,CAAA,cAAA,GAA+B,IAAI,CAAC;MAMzD;KAED,OAAO,GAAA;SACL,MAAM,iBAAiB,GAAG,yCAAyC,CAAC;SACpE,OAAO,CAAA,CAAA,EAAI,iBAAiB,CAAA,QAAA,CAAU,CAAC;MACxC;KAEO,MAAM,SAAS,CAAC,UAAsC,EAAA;;;CAI5D,QAAA,MAAM,UAAU,CAAC;;;SAIjB,IAAI,YAAY,GAAG,CAAC,CAAC;SACrB,IAAI,aAAa,GAAG,CAAC,CAAC;SAEtB,SAAS,OAAO,CAAC,SAA4B,EAAA;aAC3C,IAAI,SAAS,YAAY,YAAY,EAAE;CACrC,gBAAA,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;iBAChC,OAAO;CACL,oBAAA,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAG;CACrB,wBAAA,YAAY,EAAE,CAAC;yBACf,IAAI,CAAC,CAAC,IAAI,EAAE;CACV,4BAAA,aAAa,EAAE,CAAC;CACjB,yBAAA;yBACD,OAAO,CAAC,CAAC,KAAK,CAAC;CACjB,qBAAC,CAAC;CACF,oBAAA,OAAO,EAAE,KAAK;kBACf,CAAC;CACH,aAAA;CAAM,iBAAA;iBACL,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;CACrC,aAAA;UACF;SAED,MAAM,MAAM,GAAM,MAAM,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SAEpE,IAAI,YAAY,KAAK,aAAa,EAAE;;aAElC,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,SAAA;SACD,IAAI,aAAa,GAAG,CAAC,EAAE;aACrB,QAAQ,IAAI,CAAC,YAAY;iBACvB,KAAK,eAAe,CAAC,IAAI;qBACvB,MAAM,IAAI,KAAK,CACX,8CAA8C;CAC9C,wBAAA,CAAA,sBAAA,EAAyB,IAAI,CAAC,KAAK,CAAA,CAAA,CAAG,CAAC,CAAC;iBAC9C,KAAK,eAAe,CAAC,QAAQ;qBAC3B,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;iBACnC,KAAK,eAAe,CAAC,OAAO,CAAC;;CAG9B,aAAA;CACF,SAAA;SAED,IAAI,CAAC,KAAK,EAAE,CAAC;SACb,OAAO,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;MACrC;CAED,IAAA,MAAM,IAAI,GAAA;SACR,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC1D,OAAO,IAAI,CAAC,cAAc,CAAC;MAC5B;CACF,CAAA;CAED;CACA;CAEA;;;;;;CAMG;CACG,MAAO,gBAAoB,SAAQ,YAAe,CAAA;KAGtD,WACc,CAAA,QAAyB,EAAY,UAAkB,EAAA;CACnE,QAAA,KAAK,EAAE,CAAC;SADI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;SAAY,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;SAEnE,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAA6B,UAAU,CAAC,CAAC;MACtE;KAED,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,cAAc,CAAC;MACjD;CAED;;;CAGG;KACO,MAAM,GAAA;CACd,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE;aAC5B,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;CAC/B,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACrB,SAAA;MACF;KAED,IAAI,GAAA;SACF,IAAI,CAAC,MAAM,EAAE,CAAC;;;;CAId,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;MAC5B;CACF,CAAA;CAED;;;;;CAKG;CACG,MAAO,eAAmB,SAAQ,gBAAmB,CAAA;CAUzD,IAAA,WAAA,CACqB,QAAyB,EAAY,UAAkB,EACxE,IAAa,EAAA;CACf,QAAA,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;SAFT,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAiB;SAAY,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;;SAHpE,IAAiB,CAAA,iBAAA,GAAG,KAAK,CAAC;CAMhC,QAAA,IAAI,CAAC,MAAM,GAAGC,eAAe,CAAC,IAAI,IAAID,aAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;CAChE,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;MAC7D;CAEQ,IAAA,MAAM,IAAI,GAAA;;;;;CAKjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;SAC5D,OAAO,IAAI,CAAC,QAAQ,CAAC;MACtB;CAEO,IAAA,SAAS,CAAC,GAAW,EAAA;SAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;MACxC;KAES,WAAW,GAAA;SACnB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;MAC7C;CAED,IAAA,MAAM,UAAU,GAAA;;CAEd,QAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;aAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;CACf,SAAA;CACD,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;CAC7B,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;aACvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;aAC5D,IAAI,MAAM,CAAC,IAAI,EAAE;CACf,gBAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;CAC/B,aAAA;CAAM,iBAAA;iBACL,IAAI,CAAC,MAAM,EAAE,CAAC;CACd,gBAAA,OAAO,MAAM,CAAC;CACf,aAAA;CACF,SAAA;SACD,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;MAClC;CACF;;CCrqCD;;;;;;;;;;;;;;;;CAgBG;CAeH;CAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BG;CACH,MAAsB,OAAO,CAAA;CAA7B,IAAA,WAAA,GAAA;SAWW,IAAI,CAAA,IAAA,GAAW,IAAI,CAAC;MA2c9B;;;;;;CAncC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsDG;CACH,IAAA,KAAK,CAAC,SAAiB,EAAE,cAAc,GAAG,IAAI,EAAA;SAC5C,MAAM,IAAI,GAAG,IAAI,CAAC;SAClBA,aAAE,CAAC,IAAI,CAAC,MAAM,CACV,SAAS,GAAG,CAAC,EAAE,MAAM,CAAA;QACrB,SAAS,CAAA,CAAE,CAAC,CAAC;CACjB,QAAA,IAAI,IAAI,CAAC;SACT,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;;;CAG/C,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CAClB,SAAA;CAAM,aAAA,IAAI,cAAc,EAAE;;;aAGzB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;CACzC,SAAA;CAAM,aAAA;;;aAGL,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;CAC1C,SAAA;CACD,QAAA,OAAO,qBAAqB,CAAC,YAAW;CACtC,YAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE;CACxB,iBAAA,gBAAgB,CAAC,SAAS,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;UACnE,EAAE,IAAI,CAAC,CAAC;MACV;CAED;;;;;;;;;;;;;;CAcG;CACH,IAAA,WAAW,CAAC,OAAmB,EAAA;SAC7B,MAAM,IAAI,GAAG,IAAI,CAAC;CAClB,QAAA,IAAI,IAAI,CAAC;SACT,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;;;aAGvD,IAAI,GAAG,QAAQ,CAAC;CACjB,SAAA;cAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE;;;aAGpD,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;CACjC,SAAA;CAAM,aAAA;;;aAGL,IAAI,GAAG,IAAI,CAAC;CACb,SAAA;SACD,OAAO,qBAAqB,CACxB,YACI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,WAAW,CAAC,MAAM,OAAO,CAAC,QAAQ,EAAE,CAAC,EACjE,IAAI,CAAC,CAAC;MACX;CAED;;;;;;;;;;;;;;;CAeG;CACH,IAAA,MAAM,CAAC,SAAgC,EAAA;SACrC,MAAM,IAAI,GAAG,IAAI,CAAC;CAClB,QAAA,IAAI,IAAI,CAAC;CACT,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;;aAE1B,IAAI,GAAG,QAAQ,CAAC;CACjB,SAAA;CAAM,aAAA;;;aAGL,IAAI,GAAG,IAAI,CAAC;CACb,SAAA;CACD,QAAA,OAAO,qBAAqB,CAAC,YAAW;aACtC,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC,IAAIA,aAAE,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzE,EAAE,IAAI,CAAC,CAAC;MACV;CAED;;;;;;;;;;;;;;;CAeG;KACH,MAAM,YAAY,CAAC,CAAqB,EAAA;CACtC,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;MAChD;CAED;;;;;;;;;;;;;;CAcG;CACH,IAAA,GAAG,CAA+B,SAA0B,EAAA;SAC1D,MAAM,IAAI,GAAG,IAAI,CAAC;CAClB,QAAA,OAAO,qBAAqB,CAAC,YAAW;aACtC,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,IAAIA,aAAE,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACvE,SAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;MACf;CAED;;;;;;;;;;;;;;;;;;;;;;CAsBG;CACH,IAAA,QAAQ,CAA+B,SAAmC,EAAA;SAExE,MAAM,IAAI,GAAG,IAAI,CAAC;CAClB,QAAA,OAAO,qBAAqB,CAAC,YAAW;CACtC,YAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;CACrD,SAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;MACf;CAED;;;;;;;;CAQG;CACH,IAAA,QAAQ,CAAC,UAAkB,EAAA;SACzB,IAAI,UAAU,IAAI,IAAI,EAAE;CACtB,YAAA,MAAM,IAAI,UAAU,CAChB,2DAA2D,CAAC,CAAC;CAClE,SAAA;SAED,MAAM,IAAI,GAAG,IAAI,CAAC;SAClB,OAAO,qBAAqB,CACxB,YAAY,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1E;CAED;;;;;;;;;;;;;;;;;CAiBG;CACH,IAAA,MAAM,CAAC,KAAc,EAAA;SACnB,MAAM,IAAI,GAAG,IAAI,CAAC;CAClB,QAAA,IAAI,IAAI,CAAC;SACT,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE;;;;CAIlC,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;CAC1B,SAAA;cAAM,IAAI,KAAK,KAAK,CAAC,EAAE;;aAEtB,IAAI,GAAG,CAAC,CAAC;CACV,SAAA;CAAM,aAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;;;aAGlE,IAAI,GAAG,QAAQ,CAAC;CACjB,SAAA;CAAM,aAAA;;aAEL,IAAI,GAAG,IAAI,CAAC;CACb,SAAA;CACD,QAAA,OAAO,qBAAqB,CAAC,YAAW;aACtC,MAAM,gBAAgB,GAAG,oBAAoB,CACzC,aAAa,EAAC,KAAK,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC,CAAC;aAC/D,OAAO,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;UAC/D,EAAE,IAAI,CAAC,CAAC;MACV;CAED;;;;;;;;;;;;;;;;CAgBG;CACH,IAAA,IAAI,CAAC,KAAa,EAAA;SAChB,MAAM,IAAI,GAAG,IAAI,CAAC;CAClB,QAAA,IAAI,IAAI,CAAC;CACT,QAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE;;;;CAIzD,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;CAC1B,SAAA;CAAM,aAAA,IACH,IAAI,CAAC,IAAI,IAAI,IAAI;CACjB,aAAC,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;;;aAG3D,IAAI,GAAG,CAAC,CAAC;CACV,SAAA;CAAM,aAAA;;aAEL,IAAI,GAAG,IAAI,CAAC;CACb,SAAA;SACD,OAAO,qBAAqB,CACxB,YAAY,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;MAC5D;CAMD;;;;;;;;;;;;;;;;;;;;CAoBG;CACH,IAAA,OAAO,CAAC,UAAkB,EAAE,IAAa,EAAE,sBAAsB,GAAG,IAAI,EAAA;CAEtE,QAAA,IAAI,UAAU,IAAI,IAAI,IAAI,UAAU,GAAG,CAAC,EAAE;CACxC,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;CACrB,gBAAA,MAAM,IAAI,UAAU,CAChB,0DAA0D,CAAC,CAAC;CACjE,aAAA;CAAM,iBAAA;iBACL,MAAM,IAAI,UAAU,CAChB,4DAA4D;qBAC5D,6DAA6D;qBAC7D,yDAAyD;CACzD,oBAAA,CAAA,gCAAA,EAAmC,IAAI,CAAC,IAAI,CAAA,UAAA,CAAY,CAAC,CAAC;CAC/D,aAAA;CACF,SAAA;SACD,MAAM,IAAI,GAAG,IAAI,CAAC;CAClB,QAAA,MAAM,MAAM,GAAGC,eAAe,CAAC,IAAI,IAAID,aAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;CACjE,QAAA,OAAO,qBAAqB,CAAC,YAAW;CACtC,YAAA,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;CAC3B,YAAA,IAAI,sBAAsB,EAAE;CAC1B,gBAAA,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;CACzB,aAAA;CACD,YAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;CACvE,SAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;MACf;CAED;;;;;;;;;;;;;;;;CAgBG;CACH,IAAA,IAAI,CAAC,KAAa,EAAA;SAChB,MAAM,IAAI,GAAG,IAAI,CAAC;CAClB,QAAA,IAAI,IAAI,CAAC;SACT,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE;;;aAG1C,IAAI,GAAG,KAAK,CAAC;CACd,SAAA;cAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE;;;CAGlD,YAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;CAClB,SAAA;CAAM,aAAA;;aAEL,IAAI,GAAG,IAAI,CAAC;CACb,SAAA;SACD,OAAO,qBAAqB,CACxB,YAAY,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;MAC5D;CAED;;;;;;;;;;;;;;;CAeG;CACH,IAAA,MAAM,OAAO,GAAA;CACX,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;CAC1B,YAAA,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;CACnE,SAAA;SACD,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC;MAC1C;CAED;;;;;;;;;;CAUG;CACH,IAAA,MAAM,cAAc,GAAA;CAClB,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;CAC1B,YAAA,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;CACnE,SAAA;SACD,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,cAAc,EAAE,CAAC;MACjD;;CA7HD;CAEgB,OAAe,CAAA,eAAA,GAAG,KAAH,CAAS;CA8H1C;;;;;;;;;;;CAWG;UACa,qBAAqB,CACjC,UAA0C,EAC1C,OAAe,IAAI,EAAA;KACrB,OAAO,IAAI,cAAc,OAAU,CAAA;CAAxB,QAAA,WAAA,GAAA;;aACA,IAAI,CAAA,IAAA,GAAG,IAAI,CAAC;UAStB;CAPC;;;CAGG;CACH,QAAA,MAAM,QAAQ,GAAA;aACZ,OAAO,UAAU,EAAE,CAAC;UACrB;CACF,KAAA,EACC,CAAC;CACL,CAAC;CAED;;;;;;;;;;;;;;;;;CAiBG;CACG,SAAU,KAAK,CAA+B,KAAU,EAAA;CAC5D,IAAA,OAAO,qBAAqB,CACxB,YAAY,iBAAiB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;CAC1D,CAAC;CAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwCG;CACG,SAAU,GAAG,CAA+B,QAA0B,EAAA;;CAG1E,IAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;CACzB,QAAA,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;CACtE,KAAA;CACD,IAAA,IAAI,IAAI,CAAC;CACT,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;CAC3B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACxC,YAAA,IAAI,GAAG,IAAI,IAAI,IAAI,GAAI,QAAQ,CAAC,CAAC,CAAgB,CAAC,IAAI;CAChC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAG,QAAQ,CAAC,CAAC,CAAgB,CAAC,IAAI,CAAC,CAAC;CACxE,SAAA;CACF,KAAA;UAAM,IAAI,QAAQ,YAAY,MAAM,EAAE;CACrC,QAAA,KAAK,MAAM,EAAE,IAAI,QAAQ,EAAE;CACzB,YAAA,IAAI,GAAG,IAAI,IAAI,IAAI,GAAI,QAAQ,CAAC,EAAE,CAAgB,CAAC,IAAI;CACjC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAG,QAAQ,CAAC,EAAE,CAAgB,CAAC,IAAI,CAAC,CAAC;CACzE,SAAA;CACF,KAAA;CACD,IAAA,OAAO,qBAAqB,CAAI,YAAW;SACzC,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,QAAQ,EAAE,CAAC,IAAG;aACrD,IAAI,CAAC,YAAY,OAAO,EAAE;CACxB,gBAAA,OAAO,EAAC,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;CAC9C,aAAA;CAAM,iBAAA,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;iBACxB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;CACrC,aAAA;CAAM,iBAAA;iBACL,MAAM,IAAI,KAAK,CACX,4DAA4D;CAC5D,oBAAA,iBAAiB,CAAC,CAAC;CACxB,aAAA;CACH,SAAC,CAAC,CAAC;SACH,OAAO,kBAAkB,CAAI,OAAO,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;MACjE,EAAE,IAAI,CAAC,CAAC;CACX,CAAC;CAED;;;;;;CAMG;CACH;CACA,SAAS,eAAe,CAAC,IAAW,EAAA;KAClC,IAAI,IAAI,KAAK,IAAI,EAAE;CACjB,QAAA,OAAO,IAAI,CAAC;CACb,KAAA;;CAGD,IAAA,MAAM,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;CAE3B,IAAA,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE;;CAE5B,QAAA,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;CAChC,QAAA,OAAO,EAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAC,CAAC;CAChC,KAAA;;KAGD,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAC,CAAC;CACtC,CAAC;CAED;;;CAGG;CACH,SAAS,WAAW,CAAoC,MAAW,EAAA;CAEjE,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;;CAEvB,QAAA,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;CAC1D,KAAA;KAED,IAAI,MAAM,CAAC,CAAC,CAAC,YAAYA,aAAE,CAAC,MAAM,EAAE;;CAElC,QAAA,OAAOA,aAAE,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;CACxC,KAAA;CAAM,SAAA;;CAEL,QAAA,OAAOA,aAAE,CAAC,MAAM,CAAC,MAAoB,CAAC,CAAC;CACxC,KAAA;CACH;;CChsBA;;;;;;;;;;;;;;;;CAgBG;CAMH;;;;CAIG;CACG,MAAO,eAAgB,SAAQ,OAAe,CAAA;CAClD;;;;CAIG;CACH,IAAA,WAAA,CAA+B,KAAiB,EAAA;CAC9C,QAAA,KAAK,EAAE,CAAC;SADqB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAY;MAE/C;CAED,IAAA,MAAM,QAAQ,GAAA;SACZ,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;CAClD,QAAA,MAAM,YAAY,GAAG,aAAa,CAAC,UAAU,EAAE,CAAC;CAChD,QAAA,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAG;;CAEvD,YAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;iBACvB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CAC1B,aAAA;CACD,YAAA,OAAO,IAAI,CAAC;CACd,SAAC,CAAC,CAAC;CACH,QAAA,OAAO,YAAY,CAAC;MACrB;CACF;;CCjDD;;;;;;;;;;;;;;;;CAgBG;CASH,MAAM,UAAU,GAAG,GAAG,CAAC;CACvB,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;CAChC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;CACpC,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;CACpC,MAAM,uBAAuB,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;CAC1D,MAAM,2BAA2B,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;CAE3D;;;;;;;;;;;;CAYG;CACG,MAAO,UAAW,SAAQ,OAAwB,CAAA;CAUtD;;;;;;;;;CASG;CACH,IAAA,MAAM,WAAW,GAAA;CACf,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;CAC9B,YAAA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;CAC7B,SAAA;CACD,QAAA,OAAO,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;aAC/B,IAAI,CAAC,eAAe,CAAC;MAC1D;CAED;;;;;;;CAOG;CACK,IAAA,MAAM,cAAc,GAAA;CAC1B,QAAA,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;CAC7D,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,mBAAmB,EAAE;;CAEjD,YAAA,MAAM,IAAI,KAAK,CACX,2DAA2D,CAAC,CAAC;CAClE,SAAA;CAAM,aAAA,IAAI,IAAI,CAAC,eAAe,IAAI,mBAAmB,EAAE;;CAEtD,YAAAE,OAAI,CAAC,MAAM,CACP,mBAAmB,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,EAC1D,MAAM,sCAAsC;CACxC,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,EAAE;iBACtC,2DAA2D;iBAC3D,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC;CAClE,SAAA;CACD,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;CACzB,YAAA,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC;CAC5C,SAAA;;CAED,QAAA,MAAM,MAAM,GAA4B,IAAI,CAAC,eAAe,CAAC,MAAM,CAC/D,CAAC,QAAiC,EAAE,IAAI,KAAI;CAC1C,YAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAC3C,YAAA,OAAO,QAAQ,CAAC;UACjB,EACD,EAAE,CAAC,CAAC;SACR,MAAM,cAAc,GAChB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAC7D,QAAAA,OAAI,CAAC,MAAM,CACP,cAAc,CAAC,MAAM,KAAK,CAAC,EAC3B,MAAM,gCAAgC,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC,CAAC;;SAExE,IAAI,IAAI,CAAC,aAAa,EAAE;aACtB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;iBACjD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;CAChD,gBAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;CAChB,oBAAA,MAAM,IAAI,KAAK,CACX,WAAW,GAAG,GAAG;yBACjB,+DAA+D;yBAC/D,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC;CACzD,iBAAA;CACF,aAAA;CACF,SAAA;CACD,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;MAClC;CAEO,IAAA,MAAM,mBAAmB,GAAA;SAC/B,IAAI,IAAI,CAAC,SAAS,EAAE;aAClB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;CACxC,YAAA,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;aACvC,IAAI,YAAY,CAAC,IAAI,EAAE;CACrB,gBAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;CACvD,aAAA;CACD,YAAA,MAAM,SAAS,GAAW,YAAY,CAAC,KAAK,CAAC;aAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;CAChD,YAAA,OAAO,OAAO,CAAC;CAChB,SAAA;CAAM,aAAA;CACL,YAAA,OAAO,IAAI,CAAC;CACb,SAAA;MACF;CAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+BG;KACH,WAA+B,CAAA,KAAiB,EAAE,SAAqB,EAAA;CACrE,QAAA,KAAK,EAAE,CAAC;SADqB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAY;SA9HxC,IAAS,CAAA,SAAA,GAAG,IAAI,CAAC;SACjB,IAAe,CAAA,eAAA,GAAa,IAAI,CAAC;SACjC,IAAoB,CAAA,oBAAA,GAAG,KAAK,CAAC;SAC7B,IAAa,CAAA,aAAA,GAAkC,IAAI,CAAC;SACpD,IAAqB,CAAA,qBAAA,GAAG,KAAK,CAAC;SAC9B,IAAS,CAAA,SAAA,GAAG,GAAG,CAAC;SAChB,IAAe,CAAA,eAAA,GAAG,KAAK,CAAC;SA0H9B,IAAI,CAAC,IAAI,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;SACvC,IAAI,CAAC,SAAS,EAAE;aACd,SAAS,GAAG,EAAE,CAAC;CAChB,SAAA;CACD,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;CAC9D,QAAA,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,WAAW,CAAC;CAC7C,QAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;CAC7C,QAAA,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC,qBAAqB,CAAC;SAC7D,IAAI,SAAS,CAAC,eAAe,EAAE;CAC7B,YAAAA,OAAI,CAAC,MAAM,CACP,SAAS,CAAC,SAAS,IAAI,IAAI,EAC3B,MACI,gEAAgE,CAAC,CAAC;CAC1E,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;CAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;CACtB,SAAA;CAAM,aAAA;CACL,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC;CAClE,SAAA;MACF;CAED,IAAA,MAAM,QAAQ,GAAA;CACZ,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;CAC9B,YAAA,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;CAC7B,SAAA;SACD,IAAI,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;SACvC,IAAI,IAAI,CAAC,SAAS,EAAE;;;CAGlB,YAAA,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACvB,SAAA;CACD,QAAA,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD;CAED,IAAA,eAAe,CAAC,IAAY,EAAA;SAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACnC,MAAM,QAAQ,GAAqC,EAAE,CAAC;SACtD,MAAM,MAAM,GAAqC,EAAE,CAAC;CAEpD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;aACpD,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;CACpC,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;CACnE,YAAA,IAAI,IAAI,CAAC,qBAAqB,IAAI,CAAC,MAAM,EAAE;;iBAEzC,SAAS;CACV,aAAA;CAAM,iBAAA;CACL,gBAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;iBACxB,IAAI,WAAW,GAAG,IAAI,CAAC;iBACvB,IAAI,KAAK,KAAK,EAAE,EAAE;;;CAGhB,oBAAA,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE;CAC1C,wBAAA,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;CAC9B,qBAAA;0BAAM,IAAI,MAAM,KAAK,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE;yBACxD,MAAM,IAAI,KAAK,CACX,CAAA,gBAAA,EAAmB,GAAG,CAA2B,wBAAA,EAAA,IAAI,CAAE,CAAA,CAAC,CAAC;CAC9D,qBAAA;CAAM,yBAAA;yBACL,WAAW,GAAG,SAAS,CAAC;CACzB,qBAAA;CACF,iBAAA;CAAM,qBAAA;;CAEL,oBAAA,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;CACjC,oBAAA,IAAI,KAAK,CAAC,UAAU,CAAC,EAAE;;;CAGrB,wBAAA,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM,EAAE;CACrC,4BAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;CACtC,yBAAA;CAAM,6BAAA;;6BAEL,WAAW,GAAG,KAAK,CAAC;CACrB,yBAAA;CACF,qBAAA;CAAM,yBAAA,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;;;yBAGnC,WAAW,GAAG,UAAU,CAAC;CAC1B,qBAAA;CAAM,yBAAA;;;yBAGL,QAAQ,MAAM,CAAC,KAAK;CAClB,4BAAA,KAAK,SAAS;iCACZ,WAAW,GAAG,UAAU,CAAC;iCACzB,MAAM;CACR,4BAAA,KAAK,OAAO;CACV,gCAAA,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;iCACrC,MAAM;CACR,4BAAA,KAAK,MAAM;CACT,gCAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;iCACrC,MAAM;CACR,4BAAA;iCACE,WAAW,GAAG,UAAU,CAAC;CAC5B,yBAAA;CACF,qBAAA;CACF,iBAAA;;CAED,gBAAA,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,WAAW;CACzB,oBAAA,QAAQ,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;CAC1D,aAAA;CACF,SAAA;;;SAGD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;CACpC,YAAA,OAAO,QAAQ,CAAC;CAEjB,SAAA;CAAM,aAAA;aACL,OAAO,EAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAC,CAAC;CACnC,SAAA;MACF;CAEO,IAAA,UAAU,CAAC,KAAa,EAAA;SAC9B,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;CACnD,YAAA,OAAO,CAAC,CAAC;CACV,SAAA;CAAM,aAAA;CACL,YAAA,OAAO,CAAC,CAAC;CACV,SAAA;MACF;;CAGO,IAAA,QAAQ,CAAC,IAAY,EAAE,oBAAoB,GAAG,IAAI,EAAA;SACxD,MAAM,MAAM,GAAa,EAAE,CAAC;SAC5B,IAAI,UAAU,GAAG,CAAC,CAAC;CACnB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;SAC/B,IAAI,YAAY,GAAG,SAAS,CAAC;;SAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;CACnC,YAAA,QAAQ,YAAY;;CAElB,gBAAA,KAAK,SAAS;CACZ,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;CAEpB,wBAAA,KAAK,UAAU;CACb,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;6BACnB,YAAY,GAAG,WAAW,CAAC;6BAC3B,MAAM;;yBAER,KAAK,IAAI,CAAC,SAAS;CACjB,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;;;6BAGnB,IAAI,IAAI,CAAC,SAAS,KAAK,GAAG,IAAI,IAAI,CAAC,eAAe,EAAE;iCAClD,MAAM;CACP,6BAAA;CACD,4BAAA,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;6BAChB,YAAY,GAAG,SAAS,CAAC;6BACzB,MAAM;;CAER,wBAAA;6BACE,YAAY,GAAG,WAAW,CAAC;6BAC3B,UAAU,GAAG,CAAC,CAAC;6BACf,MAAM;CACT,qBAAA;qBACD,MAAM;;CAER,gBAAA,KAAK,WAAW;CACd,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;yBAEpB,KAAK,IAAI,CAAC,SAAS;CACjB,4BAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;6BAC3C,YAAY,GAAG,SAAS,CAAC;CACzB,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;6BACnB,MAAM;CAET,qBAAA;qBACD,MAAM;;CAER,gBAAA,KAAK,WAAW;CACd,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;CAEpB,wBAAA,KAAK,UAAU;6BACb,YAAY,GAAG,uBAAuB,CAAC;6BACvC,MAAM;CAET,qBAAA;qBACD,MAAM;;CAER,gBAAA,KAAK,uBAAuB;CAC1B,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;yBAEpB,KAAK,IAAI,CAAC,SAAS;CACjB,4BAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;6BAC/C,YAAY,GAAG,SAAS,CAAC;CACzB,4BAAA,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;6BACnB,MAAM;;CAER,wBAAA,KAAK,UAAU;6BACb,YAAY,GAAG,WAAW,CAAC;6BAC3B,MAAM;;CAER,wBAAA;6BACE,YAAY,GAAG,2BAA2B,CAAC;6BAC3C,MAAM;CACT,qBAAA;qBACD,MAAM;CACR,gBAAA,KAAK,2BAA2B;CAC9B,oBAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;;CAEpB,wBAAA,KAAK,UAAU;6BACb,YAAY,GAAG,WAAW,CAAC;6BAC3B,MAAM;CAET,qBAAA;qBACD,MAAM;CAET,aAAA;CACF,SAAA;;SAED,IAAI,YAAY,KAAK,uBAAuB,EAAE;CAC5C,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;CACzD,SAAA;CAAM,aAAA;aACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;CACzC,SAAA;;SAED,IAAI,oBAAoB,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;CACzE,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,qCAAA,EACZ,IAAI,CAAC,eAAe,CAAC,MAAM,CAAA,4BAAA,EAA+B,MAAM,CAAA,CAAE,CAAC,CAAC;CACzE,SAAA;CACD,QAAA,OAAO,MAAM,CAAC;MACf;CACF,CAAA;CAED;CACA;CACA;;CC3YA;;;;;;;;;;;;;;;;CAgBG;CAMH;;;;;CAKG;CACG,MAAO,kBAAmB,SAAQ,YAA6B,CAAA;CAgBnE,IAAA,WAAA,CAAuC,gBAAkC,EAAA;CACvE,QAAA,KAAK,EAAE,CAAC;SAD6B,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;SAfjE,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;SAiBvB,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,IAAI,IAAI,CAAC;SAChD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;CAC5C,QAAA,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,EAAE;CACvD,YAAA,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE;aAClC,MAAM,IAAI,KAAK,CACX,CAAmD,iDAAA,CAAA;CACnD,gBAAA,CAAA,4BAAA,EAA+B,IAAI,CAAC,OAAO,CAAA,CAAE,CAAC,CAAC;CACpD,SAAA;SAED,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC,uBAAuB,IAAI,EAAE,CAAC;CAChE,QAAA,IAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC,YAAY,CAAC;CAClD,QAAA,IAAI,CAAC,oBAAoB;CACrB,YAAA,gBAAgB,CAAC,oBAAoB,IAAI,IAAI,CAAC,OAAO,CAAC;CAC1D,QAAA,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC,qBAAqB,CAAC;SACpE,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,CAAC,qBAAqB,IAAI,CAAC,CAAC;CAEzE,QAAA,IAAI,CAAC,kBAAkB;CACnB,YAAA,gBAAgB,CAAC,kBAAkB,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;CACjE,QAAA,IAAI,CAAC,eAAe;CAChB,YAAA,gBAAgB,CAAC,eAAe,KAAK,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;SAC7D,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;aACrD,MAAM,IAAI,KAAK,CACX,yDAAyD;CACzD,gBAAA,+CAA+C,CAAC,CAAC;CACtD,SAAA;MACF;KAED,OAAO,GAAA;CACL,QAAA,OAAO,YAAY,CAAC;MACrB;;CAGD,IAAA,aAAa,MAAM,CAAC,mBAAqC,EAAE,EAAA;SACzD,IAAI,CAACC,MAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;CAC5B,YAAA,MAAM,IAAI,KAAK,CACX,0DAA0D,CAAC,CAAC;CACjE,SAAA;CAED,QAAA,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;;CAGpE,QAAA,MAAM,kBAAkB,CAAC,KAAK,EAAE,CAAC;CAEjC,QAAA,OAAO,kBAAkB,CAAC;MAC3B;;CAGD,IAAA,MAAM,KAAK,GAAA;SACT,IAAI;aACF,IAAI,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;iBACtD,KAAK,EAAE,IAAI,CAAC,qBAAqB,IAAI,IAAI,GAAG,IAAI;CACJ,oBAAA,IAAI,CAAC,qBAAqB;CACtE,gBAAA,KAAK,EAAE,KAAK;CACb,aAAA,CAAC,CAAC;CACJ,SAAA;CAAC,QAAA,OAAO,CAAC,EAAE;aACV,MAAM,IAAI,KAAK,CACX,CAAA,8CAAA,EAAiD,CAAC,CAAC,OAAO,CAAE,CAAA,CAAC,CAAC;CACnE,SAAA;CAED,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;CAChB,YAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;CAC5D,SAAA;CAED,QAAA,MAAM,cAAc;;CAEf,QAAA,MAAc,CAAC,YAAY,IAAK,MAAc,CAAC,kBAAkB,CAAC;CACvE,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,cAAc,EAAE,CAAC;CAEzC,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;;;aAGtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;CAClD,SAAA;cAAM,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,IAAI,CAAC,YAAY,EAAE;aAC7D,MAAM,IAAI,KAAK,CACX,CAA6B,2BAAA,CAAA;iBAC7B,CAAa,UAAA,EAAA,IAAI,CAAC,YAAY,CAAI,EAAA,CAAA;CAClC,gBAAA,CAAA,QAAA,EAAW,IAAI,CAAC,YAAY,CAAC,UAAU,CAAA,CAAE,CAAC,CAAC;CAChD,SAAA;CAED,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC5E,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;SACnD,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;SACzC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;CACjE,QAAA,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC/C,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC/C,OAAO;MACR;CAED,IAAA,MAAM,IAAI,GAAA;SACR,IAAI,IAAI,CAAC,QAAQ,EAAE;aACjB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,SAAA;CAED,QAAA,IAAI,iBAAyB,CAAC;CAC9B,QAAA,IAAI,cAAsB,CAAC;CAE3B,QAAA,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;SACjD,IAAI,IAAI,CAAC,kBAAkB,EAAE;aAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;CACjE,YAAA,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,CAChD,QAAQ,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC;CAC/D,SAAA;SACD,IAAI,IAAI,CAAC,eAAe,EAAE;aACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;CACjE,YAAA,cAAc,GAAG,IAAI,CAAC,2BAA2B,CAC7C,QAAQ,EAAE,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;CACnD,SAAA;SAED,OAAO;aACL,KAAK,EAAE,EAAC,aAAa,EAAE,iBAAiB,EAAE,UAAU,EAAE,cAAc,EAAC;CACrE,YAAA,IAAI,EAAE,KAAK;UACZ,CAAC;MACH;;;CAID,IAAA,MAAM,OAAO,GAAA;SACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KACoB,CAAC;MACjD;CAEO,IAAA,MAAM,YAAY,GAAA;SAExB,MAAM,aAAa,GAAmB,EAAE,CAAC;SACzC,MAAM,aAAa,GAAmB,EAAE,CAAC;SACzC,IAAI,aAAa,GAAG,CAAC,CAAC;CACtB,QAAA,OAAO,IAAI,OAAO,CAAC,OAAO,IAAG;CAC3B,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,MAAK;iBAClC,IAAI,IAAI,CAAC,kBAAkB,EAAE;qBAC3B,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;qBAEnD,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE;CAClC,wBAAA,OAAO,CAAC,EAAC,aAAa,EAAE,aAAa,EAAC,CAAC,CAAC;CACzC,qBAAA;CACD,oBAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;CACvE,iBAAA;iBACD,IAAI,IAAI,CAAC,eAAe,EAAE;qBACxB,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;qBACpD,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;CAC3C,iBAAA;;CAGD,gBAAA,IAAI,EAAE,aAAa,KAAK,IAAI,CAAC,SAAS,EAAE;qBACtC,aAAa,CAAC,UAAU,CAAC,CAAC;CAC1B,oBAAA,OAAO,CAAC,EAAC,aAAa,EAAE,aAAa,EAAC,CAAC,CAAC;CACzC,iBAAA;cACF,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;CAC7C,SAAC,CAAC,CAAC;MACJ;;KAGD,IAAI,GAAA;CACF,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;CAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;CACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;CAC3B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;CAC1B,YAAA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;iBAC7D,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;CACnC,aAAA;CACF,SAAA;MACF;;KAGQ,OAAO,GAAA;CACd,QAAA,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;MACpE;;KAGD,aAAa,GAAA;SACX,OAAO,IAAI,CAAC,YAAY,CAAC;MAC1B;CAEO,IAAA,YAAY,CAAC,KAAqB,EAAA;SACxC,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;SAClC,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;SAC5D,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;CAC9D,QAAA,OAAO,QAAQ,CAAC;MACjB;KAEO,2BAA2B,CAAC,QAAsB,EAAE,KAAe,EAAA;CAEzE,QAAA,MAAM,IAAI,GAAG,IAAI,YAAY,CAACD,OAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;CAEzD,QAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;CAClD,QAAA,OAAOE,SAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;MAC5B;CACF;;CCzOD;;;;;;;;;;;;;;;;CAgBG;CAMH;;;CAGG;CACG,MAAO,cAAe,SAAQ,YAAsB,CAAA;KAQxD,WACuB,CAAA,kBAAoC,EACpC,YAA0B,EAAA;CAC/C,QAAA,KAAK,EAAE,CAAC;SAFa,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAkB;SACpC,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAc;SATzC,IAAQ,CAAA,QAAA,GAAG,IAAI,CAAC;SAEhB,IAAM,CAAA,MAAA,GAAG,KAAK,CAAC;CASrB,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;CACvB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;CACnB,YAAA,IAAI,CAAC,QAAQ;CACT,gBAAA,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;aACpE,IAAI,CAAC,UAAU,GAAGC,WAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;CACzC,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;;CAEhC,gBAAA,MAAM,kBAAkB,GACpB,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;iBACxE,MAAM,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,GAAG;CAC5D,oBAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;iBACnC,MAAM,cAAc,GAAG,CAAC,CAAC,GAAG,kBAAkB,IAAI,CAAC,CAAC;iBACpD,MAAM,eAAe,GAAG,CAAC,CAAC,GAAG,mBAAmB,IAAI,CAAC,CAAC;CACtD,gBAAA,MAAM,YAAY,GAAG,cAAc,GAAG,kBAAkB,CAAC;CACzD,gBAAA,MAAM,aAAa,GAAG,mBAAmB,GAAG,eAAe,CAAC;iBAC5D,IAAI,CAAC,OAAO,GAAGC,WAAQ,CACnB,CAAC,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,CAAC,EAC9D,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CACb,aAAA;CAAM,iBAAA;iBACL,IAAI,CAAC,OAAO,GAAGA,WAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;CAC/C,aAAA;CACF,SAAA;MACF;KAED,OAAO,GAAA;CACL,QAAA,OAAO,QAAQ,CAAC;MACjB;;KAGD,aAAa,MAAM,CACf,kBAAqC,EAAE,eAA6B,EAAE,EAAA;SACxE,IAAI,CAACH,MAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;CAC5B,YAAA,MAAM,IAAI,KAAK,CACX,0DAA0D,CAAC,CAAC;CACjE,SAAA;SAED,IAAI,CAAC,kBAAkB,EAAE;;;CAGvB,YAAA,kBAAkB,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;aACrD,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;iBAC3D,MAAM,IAAI,KAAK,CACX,0DAA0D;CAC1D,oBAAA,gDAAgD,CAAC,CAAC;CACvD,aAAA;CACD,YAAA,kBAAkB,CAAC,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC;CACpD,YAAA,kBAAkB,CAAC,MAAM,GAAG,YAAY,CAAC,YAAY,CAAC;CACvD,SAAA;SACD,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;;CAG5E,QAAA,MAAM,cAAc,CAAC,KAAK,EAAE,CAAC;CAE7B,QAAA,OAAO,cAAc,CAAC;MACvB;;CAGD,IAAA,MAAM,KAAK,GAAA;CACT,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;aAChCD,OAAI,CAAC,MAAM,CACP,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,MAAM;CACpC,iBAAC,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,aAAa,CAAC,EACpD,MACI,CAA+B,4BAAA,EAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAI,EAAA,CAAA;CAC/D,gBAAA,CAAA,sCAAA,CAAwC,CAAC,CAAC;CACnD,SAAA;SAED,IAAI;aACF,IAAI,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;CACtD,gBAAA,KAAK,EAAE;CACL,oBAAA,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ;CACpC,oBAAA,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU;CACpC,wBAAA,IAAI,CAAC,YAAY,CAAC,UAAU;yBAC5B,MAAM;CACV,oBAAA,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK;CACpC,oBAAA,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM;CACvC,iBAAA;CACF,aAAA,CAAC,CAAC;CACJ,SAAA;CAAC,QAAA,OAAO,CAAC,EAAE;;aAEV,CAAC,CAAC,OAAO,GAAG,CAAA,8CAAA,EAAiD,CAAC,CAAC,OAAO,EAAE,CAAC;CACzE,YAAA,MAAM,CAAC,CAAC;CACT,SAAA;CAED,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;CAChB,YAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;CACxD,SAAA;;SAGD,IAAI;aACF,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;CACjD,SAAA;CAAC,QAAA,OAAO,KAAK,EAAE;CACd,YAAA,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CACnB,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CACtD,IAAI,CAAC,MAAgC,CAAC,CAAC;CAC1C,SAAA;;CAED,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;CAE/B,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;CAEtB,QAAA,OAAO,IAAI,OAAO,CAAO,OAAO,IAAG;;CAEjC,YAAA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,MAAK;CAC9C,gBAAA,OAAO,EAAE,CAAC;CACZ,aAAC,CAAC;CACJ,SAAC,CAAC,CAAC;MACJ;CAED,IAAA,MAAM,IAAI,GAAA;SACR,IAAI,IAAI,CAAC,QAAQ,EAAE;aACjB,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,SAAA;CAED,QAAA,IAAI,GAAG,CAAC;SACR,IAAI;aACF,GAAG,GAAGK,UAAO,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;CACnD,SAAA;CAAC,QAAA,OAAO,CAAC,EAAE;CACV,YAAA,MAAM,IAAI,KAAK,CACX,CAAA,yCAAA,EAA4C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAE,CAAA,CAAC,CAAC;CACtE,SAAA;SACD,IAAI,IAAI,CAAC,MAAM,EAAE;aACf,IAAI;CACF,gBAAA,OAAO,EAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;CAC3D,aAAA;CAAC,YAAA,OAAO,CAAC,EAAE;iBACV,MAAM,IAAI,KAAK,CAAC,CAAA,iCAAA,EAAoC,CAAC,CAAC,OAAO,CAAE,CAAA,CAAC,CAAC;CAClE,aAAA;CAAS,oBAAA;iBACR,GAAG,CAAC,OAAO,EAAE,CAAC;CACf,aAAA;CACF,SAAA;CAAM,aAAA;aACL,OAAO,EAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;CAClC,SAAA;MACF;KAEO,YAAY,GAAA;;;;SAIlB,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY;cAC9D,IAAI,CAAC,kBAAkB,CAAC,KAAK,KAAK,IAAI,CAAC,YAAY,CAAC,WAAW;iBAC/D,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;CACvE,YAAA,OAAO,IAAI,CAAC;CACb,SAAA;CACD,QAAA,OAAO,KAAK,CAAC;MACd;;CAGD,IAAA,kBAAkB,CAAC,GAAa,EAAA;SAC9B,OAAOC,OAAI,CAAC,MAAK;CACf,YAAA,MAAM,aAAa,GAAaC,aAAU,CAACC,OAAI,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;CACtE,YAAA,IAAI,YAAY,CAAC;aACjB,YAAY,GAAGC,QAAK,CAAC,aAAa,CAC9B,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAC3D,UAAU,CAAC,CAAC;;CAEhB,YAAA,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;aACjC,OAAOC,UAAO,CAAC,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAA6B,CAAC,CAAC;CAC3E,SAAC,CAAC,CAAC;MACJ;;;CAID,IAAA,MAAM,OAAO,GAAA;SACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC;MAClC;;KAGD,IAAI,GAAA;SACF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;CAEvC,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;SAEtC,IAAI;CACF,YAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;CAC1C,SAAA;CAAC,QAAA,OAAO,KAAK,EAAE;CACd,YAAA,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CACnB,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG,IAAI,CAAC;CACpC,SAAA;CACD,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;MACtB;;KAGQ,OAAO,GAAA;CACd,QAAA,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;MACpE;CACF;;CC/ND;;;;;;;;;;;;;;;;CAgBG;CAIH;;;;;;CAMG;OACmB,UAAU,CAAA;CAU/B,CAAA;CAED;CACA;CACA;;CCzCA;;;;;;;;;;;;;;;;CAgBG;CAIG,MAAgB,cAAe,SAAQ,YAAoB,CAAA;CAC/D;;;;;;;;;;;;;;;;;CAiBG;CACH,IAAA,KAAK,CAAC,SAAiB,EAAA;CACrB,QAAA,OAAO,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;MAC3C;CACF,CAAA;CAED;CACA;CACA;CACA;CACA;CAEA;CACA;CACA;CACA;CAEA,MAAM,aAAc,SAAQ,cAAc,CAAA;KAGxC,WAAsB,CAAA,QAA8B,EAAE,SAAiB,EAAA;CACrE,QAAA,KAAK,EAAE,CAAC;SADY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;SAElD,IAAI,CAAC,IAAI,GAAG,IAAI,iBAAiB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;MACxD;KAED,OAAO,GAAA;CACL,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;MAC5B;CAED,IAAA,MAAM,IAAI,GAAA;CACR,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;MACzB;CACF,CAAA;CAED,MAAM,iBAAkB,SAAQ,iBAAyB,CAAA;KAIvD,WACc,CAAA,QAA8B,EAAY,SAAiB,EAAA;CACvE,QAAA,KAAK,EAAE,CAAC;SADI,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAsB;SAAY,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;;SAHzE,IAAS,CAAA,SAAA,GAAG,EAAE,CAAC;MAKd;KAED,OAAO,GAAA;CACL,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA,WAAA,EAAc,IAAI,CAAC,SAAS,CAAA,EAAA,CAAI,CAAC;MACnE;CAED,IAAA,MAAM,IAAI,GAAA;SACR,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;SAC/C,IAAI,WAAW,CAAC,IAAI,EAAE;CACpB,YAAA,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE;CACzB,gBAAA,OAAO,KAAK,CAAC;CACd,aAAA;;;aAID,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;CACtC,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;CACpB,YAAA,OAAO,IAAI,CAAC;CACb,SAAA;CACD,QAAA,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAa,CAAC;;;;CAKlE,QAAA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;CACrC,QAAA,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;CACrC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAC7B,SAAA;SACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;CAEzC,QAAA,OAAO,IAAI,CAAC;MACb;CACF;;CC/GD;;;;;;;;;;;;;;;;CAgBG;CAMG,MAAgB,iBAAkB,SAAQ,YAAwB,CAAA;CACtE;;;;;;;;CAQG;KACH,UAAU,GAAA;CACR,QAAA,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;MAC/B;CACF,CAAA;CAED;CACA;CACA;CACA;CACA;CAEA;CACA;CACA;CACA;CAEA,MAAM,YAAa,SAAQ,cAAc,CAAA;CAGvC,IAAA,WAAA,CAAsB,QAAkC,EAAA;CACtD,QAAA,KAAK,EAAE,CAAC;SADY,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA0B;SAEtD,IAAI,CAAC,IAAI,GAAG,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC;MAC5C;KAED,OAAO,GAAA;CACL,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;MAC5B;CAED,IAAA,MAAM,IAAI,GAAA;CACR,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;MACzB;CACF,CAAA;CAED;;;;;;;;;;;;;;;;;;;;;CAqBG;CACH,MAAM,gBAAiB,SAAQ,iBAAyB,CAAA;CAMtD,IAAA,WAAA,CAA+B,QAAkC,EAAA;CAC/D,QAAA,KAAK,EAAE,CAAC;SADqB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAA0B;CAE/D,QAAA,IAAIT,MAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;aAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;CACzC,SAAA;CAAM,aAAA;;aAEL,MAAM,EAAC,aAAa,EAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;aAClD,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;CAC1C,SAAA;MACF;KACD,OAAO,GAAA;SACL,OAAO,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;MAC7C;CAED,IAAA,MAAM,IAAI,GAAA;SACR,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;CAC/C,QAAA,IAAI,KAAK,CAAC;SACV,IAAI,WAAW,CAAC,IAAI,EAAE;CACpB,YAAA,OAAO,KAAK,CAAC;CACd,SAAA;CAAM,aAAA;CACL,YAAA,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;CAC3B,SAAA;CAED,QAAA,IAAI,IAAY,CAAC;CACjB,QAAA,IAAIA,MAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;CAC3B,YAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC;CACnD,SAAA;CAAM,aAAA;CACL,YAAA,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;CACtD,SAAA;CACD,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAC5B,QAAA,OAAO,IAAI,CAAC;MACb;CACF;;CC7HD;;;;;;;;;;;;;;;;CAgBG;CAcH;;;;;;CAMG;CACG,MAAO,iBAAkB,SAAQ,iBAAiB,CAAA;KAItD,WACc,CAAA,IAAiB,EACjB,OAAA,GAAoC,EAAE,EAAA;CAClD,QAAA,KAAK,EAAE,CAAC;SAFI,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAa;SACjB,IAAO,CAAA,OAAA,GAAP,OAAO,CAA+B;CAElD,QAAAD,OAAI,CAAC,MAAM,CACP,CAAC,IAAI,YAAY,UAAU;cACtBC,MAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC;kBAClB,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI;CAC7C,gBAAA,KAAK,CAAC,EACf,MAAM,4DAA4D;CAC9D,YAAA,YAAY,CAAC,CAAC;SACtB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;;SAElC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,GAAG,IAAI,CAAC;MACnD;KAED,OAAO,GAAA;CACL,QAAA,OAAO,CAAc,WAAA,EAAA,IAAI,CAAC,IAAI,EAAE,CAAC;MAClC;CAED,IAAA,MAAM,IAAI,GAAA;CACR,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,UAAU;CAC5B,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU;CACpB,YAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;aACvC,OAAO,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAC,CAAC;CAClC,SAAA;SACD,MAAM,KAAK,GAAG,IAAI,OAAO,CAAa,CAAC,OAAO,EAAE,MAAM,KAAI;aACxD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;CACzC,YAAA,IAAI,IAAI,CAAC,IAAI,YAAY,UAAU,EAAE;;;CAGnC,gBAAA,OAAO,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;CAC5D,aAAA;CAAM,iBAAA;;;;CAKL,gBAAA,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;CACpC,gBAAA,UAAU,CAAC,MAAM,GAAG,CAAC,KAAK,KAAI;CAC5B,oBAAA,IAAI,IAAI,GAAkC,UAAU,CAAC,MAAM,CAAC;;;;qBAI5D,IAAI,IAAI,YAAY,WAAW,EAAE;CAC/B,wBAAA,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;CAC7B,qBAAA;CACD,oBAAA,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC,EAAE;yBACjC,OAAO,MAAM,CAAC,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC,CAAC;CACnE,qBAAA;qBACD,OAAO,CAAC,IAAI,CAAC,CAAC;CAChB,iBAAC,CAAC;CACF,gBAAA,UAAU,CAAC,OAAO,GAAG,CAAC,KAAK,KAAI;qBAC7B,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;CACtC,iBAAC,CAAC;CACF,gBAAA,UAAU,CAAC,OAAO,GAAG,CAAC,KAAK,KAAI;qBAC7B,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;CACvC,iBAAC,CAAC;;;CAGF,gBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;;;CAGhD,gBAAA,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;CACrC,aAAA;CACD,YAAA,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;CACpB,SAAC,CAAC,CAAC;CACH,QAAA,OAAO,EAAC,KAAK,GAAG,MAAM,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC;MAC5C;CACF;;CC7GD;;;;;;;;;;;;;;;;CAgBG;CAKH;;;;;;CAMG;CACI,eAAe,gBAAgB,CAClC,GAAgB,EAAE,OAAA,GAAoC,EAAE,EACxD,SAAoB,EAAA;CACtB,IAAA,IAAI,SAAS,CAAC;CACd,IAAA,IAAI,WAAW,CAAC;CAChB,IAAA,IAAI,CAAC,OAAO,GAAG,MAAM,QAAQ,EAAE;SAC7B,SAAS,GAAG,GAAa,CAAC;CAC3B,KAAA;CAAM,SAAA;CACL,QAAA,SAAS,GAAI,GAAe,CAAC,GAAG,CAAC;CACjC,QAAA,WAAW,GAAG,yBAAyB,CAAC,GAAc,CAAC,CAAC;CACzD,KAAA;CACD,IAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,IAAID,OAAI,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;KACzE,IAAI,QAAQ,CAAC,EAAE,EAAE;SACf,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;CAChE,QAAA,OAAO,IAAI,iBAAiB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;CACnD,KAAA;CAAM,SAAA;CACL,QAAA,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;CACtC,KAAA;CACH,CAAC;CAED;CACA,MAAM,yBAAyB,GAAG,CAAC,OAAgB,KAAI;CACrD,IAAA,MAAM,IAAI,GAAG;SACX,MAAM,EAAE,OAAO,CAAC,MAAM;SACtB,OAAO,EAAE,OAAO,CAAC,OAAO;SACxB,IAAI,EAAE,OAAO,CAAC,IAAI;SAClB,IAAI,EAAE,OAAO,CAAC,IAAI;SAClB,WAAW,EAAE,OAAO,CAAC,WAAW;SAChC,KAAK,EAAE,OAAO,CAAC,KAAK;SACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;MAC7B,CAAC;CACF,IAAA,OAAO,IAAI,CAAC;CACd,CAAC;;CC9DD;;;;;;;;;;;;;;;;CAgBG;CAEH;CACA;CACA;CACM,SAAU,WAAW,CAAC,MAAW,EAAA;CACrC,IAAA,OAAO,CAAC,OAAO,MAAM,KAAK,QAAQ,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC;CAC1E;;CCvBA;;;;;;;;;;;;;;;;CAgBG;CASH;;;CAGG;CACG,MAAO,cAAe,SAAQ,UAAU,CAAA;CAC5C;;;;;;;CAOG;KACH,WACc,CAAA,KAAyB,EAChB,OAAA,GAAoC,EAAE,EAAA;CAC3D,QAAA,KAAK,EAAE,CAAC;SAFI,IAAK,CAAA,KAAA,GAAL,KAAK,CAAoB;SAChB,IAAO,CAAA,OAAA,GAAP,OAAO,CAA+B;MAE5D;CAED,IAAA,MAAM,QAAQ,GAAA;CACZ,QAAA,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAIC,MAAG,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;;CAEnD,YAAA,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CACzB,YAAA,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,YAAY,CAAE,IAAI,CAAC,KAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CAC/D,SAAA;;;SAGD,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;MACvE;CACF;;CCtDD;;;;;;;;;;;;;;;;CAgBG;CASH;;CAEG;CACG,MAAO,aAAc,SAAQ,UAAU,CAAA;CAC3C;;;;;;CAMG;KACH,WACuB,CAAA,GAAgB,EAChB,WAAA,GAAwC,EAAE,EAAA;CAC/D,QAAA,KAAK,EAAE,CAAC;SAFa,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;SAChB,IAAW,CAAA,WAAA,GAAX,WAAW,CAA+B;MAEhE;;;;;CAMD,IAAA,MAAM,QAAQ,GAAA;CACZ,QAAA,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;CACzB,YAAA,OAAO,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,GAAa,EAAE,IAAI,CAAC,WAAW,CAAC;CAC3D,iBAAA,QAAQ,EAAE,CAAC;CACjB,SAAA;CAAM,aAAA;aACL,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;CACrD,SAAA;MACF;CACF;;CCtDD;;;;;;;;;;;;;;;;CAgBG;CAWH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6EG;UACa,GAAG,CACf,MAAmB,EAAE,YAAuB,EAAE,EAAA;KAChD,OAAO,IAAI,UAAU,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;CAC9D,CAAC;CAED;;;;;;;;;;;;;;;;;;;;;;;CAuBG;CACG,SAAU,IAAI,CAChB,CAAsD,EAAA;CACxD,IAAA,MAAM,IAAI,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;KACrC,OAAO,qBAAqB,CAAC,YAAY,IAAI,CAAC,CAAC;CACjD,CAAC;CAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAyDG;CACG,SAAU,SAAS,CACvB,SAAsE,EAAA;CAEtE,IAAA,OAAO,qBAAqB,CAAC,YAAW;CACtC,QAAA,MAAM,GAAG,GAAG,MAAM,SAAS,EAAE,CAAC;SAC9B,OAAO,oBAAoB,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;CAChD,KAAC,CAAC,CAAC;CACL,CAAC;CAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6BG;CACI,eAAe,MAAM,CACxB,kBAAqC,EACrC,YAA2B,EAAA;KAC7B,OAAO,cAAc,CAAC,MAAM,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;CACjE,CAAC;CAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiCG;CACI,eAAe,UAAU,CAAC,gBAAmC,EAAA;CAElE,IAAA,OAAO,kBAAkB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;CACrD;;CCxRA;CAEA;AACM,OAAA,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}