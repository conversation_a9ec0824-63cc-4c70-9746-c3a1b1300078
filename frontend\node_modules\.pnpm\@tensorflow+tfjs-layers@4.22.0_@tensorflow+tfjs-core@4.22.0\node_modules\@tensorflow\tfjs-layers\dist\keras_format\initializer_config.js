/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
export const VALID_FAN_MODE_VALUES = ['fanIn', 'fanOut', 'fanAvg'];
export const VALID_DISTRIBUTION_VALUES = ['normal', 'uniform', 'truncatedNormal'];
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid
// and that we have the right number of them.
/**
 * A string array of valid Initializer class names.
 *
 * This is guaranteed to match the `InitializerClassName` union type.
 */
export const initializerClassNames = [
    'Zeros', 'Ones', 'Constant', 'RandomNormal', 'RandomUniform',
    'TruncatedNormal', 'VarianceScaling', 'Orthogonal', 'Identity'
];
//# sourceMappingURL=data:application/json;base64,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