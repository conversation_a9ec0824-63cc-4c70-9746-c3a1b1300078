/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import {Bincount, BincountAttrs, BincountInputs, KernelConfig, scalar} from '@tensorflow/tfjs';

import {createTensorsTypeOpAttr, NodeJSKernelBackend} from '../nodejs_kernel_backend';

export const bincountConfig: KernelConfig = {
  kernelName: Bincount,
  backendName: 'tensorflow',
  kernelFunc: ({inputs, backend, attrs}) => {
    const {x, weights} = inputs as BincountInputs;
    const {size} = attrs as unknown as BincountAttrs;
    const nodeBackend = backend as NodeJSKernelBackend;

    const $size = scalar(size, 'int32');

    const opAttrs = [createTensorsTypeOpAttr('T', weights.dtype)];

    const result =
        nodeBackend.executeSingleOutput(Bincount, opAttrs, [x, $size, weights]);

    $size.dispose();

    return result;
  }
};
