"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.resizeNearestNeighborConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.resizeNearestNeighborConfig = {
    kernelName: tfjs_1.ResizeNearestNeighbor,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var images = args.inputs.images;
        var backend = args.backend;
        var _a = args.attrs, alignCorners = _a.alignCorners, halfPixelCenters = _a.halfPixelCenters, size = _a.size;
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', images.dtype),
            {
                name: 'align_corners',
                type: backend.binding.TF_ATTR_BOOL,
                value: alignCorners
            },
            {
                name: 'half_pixel_centers',
                type: backend.binding.TF_ATTR_BOOL,
                value: halfPixelCenters
            },
        ];
        var newHeight = size[0], newWidth = size[1];
        var sizeTensor = (0, tfjs_1.tensor1d)([newHeight, newWidth], 'int32');
        var res = backend.executeSingleOutput(tfjs_1.ResizeNearestNeighbor, opAttrs, [images, sizeTensor]);
        sizeTensor.dispose();
        return res;
    }
};
