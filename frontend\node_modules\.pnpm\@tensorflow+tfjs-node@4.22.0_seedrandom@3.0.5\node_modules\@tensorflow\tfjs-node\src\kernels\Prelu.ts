/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import {abs, KernelConfig, Prelu, PreluInputs, relu, Tensor, tidy} from '@tensorflow/tfjs';

export const preluConfig: KernelConfig = {
  kernelName: Prelu,
  backendName: 'tensorflow',
  kernelFunc: (args) => {
    const inputs = args.inputs as PreluInputs;
    const x = inputs.x as Tensor;
    const alpha = inputs.alpha as Tensor;

    return tidy(() => {
      const pos = relu(x);
      const neg = alpha.mul(x.sub(abs(x))).mul(0.5);
      return pos.add(neg);
    });
  }
};
