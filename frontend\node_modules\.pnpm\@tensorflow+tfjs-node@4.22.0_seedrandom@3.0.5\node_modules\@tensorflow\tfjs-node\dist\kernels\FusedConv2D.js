"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.fusedConv2DConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var Conv2D_1 = require("./Conv2D");
exports.fusedConv2DConfig = {
    kernelName: tfjs_1.FusedConv2D,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var _a = args.inputs, x = _a.x, filter = _a.filter, bias = _a.bias, preluActivationWeights = _a.preluActivationWeights;
        var backend = args.backend;
        var _b = args.attrs, strides = _b.strides, pad = _b.pad, dataFormat = _b.dataFormat, dilations = _b.dilations, dimRoundingMode = _b.dimRoundingMode, activation = _b.activation, leakyreluAlpha = _b.leakyreluAlpha;
        if (dataFormat !== 'NHWC') {
            throw new Error("Node backend FusedConv2D does not support dataFormat:'" +
                "".concat(dataFormat, "'. Please use 'NHWC'."));
        }
        var $dataFormat = tfjs_1.backend_util.convertConv2DDataFormat(dataFormat);
        var convInfo = tfjs_1.backend_util.computeConv2DInfo(x.shape, filter.shape, strides, dilations, pad, dimRoundingMode, false /* depthwise */, $dataFormat);
        var result = (0, Conv2D_1.conv2dImpl)(x, filter, convInfo, backend);
        var toDispose = [];
        if (bias != null) {
            toDispose.push(result);
            result = (0, tfjs_1.add)(result, bias);
        }
        var temp = result;
        result = backend.applyActivation(result, activation, preluActivationWeights, leakyreluAlpha);
        if (temp !== result) {
            toDispose.push(temp);
        }
        toDispose.forEach(function (t) { return t.dispose(); });
        return result;
    }
};
