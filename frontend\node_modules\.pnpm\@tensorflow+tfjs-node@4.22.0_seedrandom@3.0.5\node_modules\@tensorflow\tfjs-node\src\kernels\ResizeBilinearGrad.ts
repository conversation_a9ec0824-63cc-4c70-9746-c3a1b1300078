/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import {KernelConfig, ResizeBilinearGrad, ResizeBilinearGradAttrs, ResizeBilinearGradInputs} from '@tensorflow/tfjs';

import {createTensorsTypeOpAttr, NodeJSKernelBackend} from '../nodejs_kernel_backend';

export const resizeBilinearGradConfig: KernelConfig = {
  kernelName: ResizeBilinearGrad,
  backendName: 'tensorflow',
  kernelFunc: (args) => {
    const {images, dy} = args.inputs as ResizeBilinearGradInputs;
    const backend = args.backend as NodeJSKernelBackend;
    const {alignCorners} = args.attrs as unknown as ResizeBilinearGradAttrs;

    const opAttrs = [
      createTensorsTypeOpAttr('T', images.dtype), {
        name: 'align_corners',
        type: backend.binding.TF_ATTR_BOOL,
        value: alignCorners
      }
    ];
    return backend.executeSingleOutput(
        ResizeBilinearGrad, opAttrs, [dy, images]);
  }
};
