"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.avgPoolConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.avgPoolConfig = {
    kernelName: tfjs_1.AvgPool,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var x = args.inputs.x;
        var backend = args.backend;
        var _a = args.attrs, filterSize = _a.filterSize, strides = _a.strides, pad = _a.pad, dimRoundingMode = _a.dimRoundingMode;
        var convInfo = tfjs_1.backend_util.computePool2DInfo(x.shape, filterSize, strides, 1 /* dilations */, pad, dimRoundingMode);
        if (convInfo.padInfo.type !== 'VALID' && convInfo.padInfo.type !== 'SAME') {
            throw new Error("TF Backend supports only 'valid' and 'same' padding " +
                "while padding was ".concat(convInfo.padInfo.type));
        }
        var ksize = [1, convInfo.filterHeight, convInfo.filterWidth, 1];
        var $strides = [1, convInfo.strideHeight, convInfo.strideWidth, 1];
        var padding = convInfo.padInfo.type;
        var dataFormat = convInfo.dataFormat === 'channelsLast' ? 'NHWC' : 'NCHW';
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', x.dtype),
            { name: 'ksize', type: backend.binding.TF_ATTR_INT, value: ksize },
            { name: 'strides', type: backend.binding.TF_ATTR_INT, value: $strides },
            { name: 'padding', type: backend.binding.TF_ATTR_STRING, value: padding },
            {
                name: 'data_format',
                type: backend.binding.TF_ATTR_STRING,
                value: dataFormat
            },
        ];
        return backend.executeSingleOutput(tfjs_1.AvgPool, opAttrs, [x]);
    }
};
