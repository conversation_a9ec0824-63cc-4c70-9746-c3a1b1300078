/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid.
/**
 * A string array of valid RecurrentLayer class names.
 *
 * This is guaranteed to match the `RecurrentLayerClassName` union type.
 */
export const recurrentLayerClassNames = [
    'GRU',
    'LSTM',
    'SimpleRNN',
];
//# sourceMappingURL=data:application/json;base64,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