"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.prodConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.prodConfig = {
    kernelName: tfjs_1.Prod,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var x = args.inputs.x;
        var backend = args.backend;
        var _a = args.attrs, axis = _a.axis, keepDims = _a.keepDims;
        var axes = tfjs_1.util.parseAxisParam(axis, x.shape);
        var axesTensor = (0, tfjs_1.tensor1d)(axes, 'int32');
        var opAttrs = [
            { name: 'keep_dims', type: backend.binding.TF_ATTR_BOOL, value: keepDims },
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', x.dtype),
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('Tidx', 'int32')
        ];
        var res = backend.executeSingleOutput(tfjs_1.Prod, opAttrs, [x, axesTensor]);
        axesTensor.dispose();
        return res;
    }
};
