/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
export {};
//# sourceMappingURL=data:application/json;base64,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