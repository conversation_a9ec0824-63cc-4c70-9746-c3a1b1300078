"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.dilation2dBackpropInputConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.dilation2dBackpropInputConfig = {
    kernelName: tfjs_1.Dilation2DBackpropInput,
    backendName: 'tensorflow',
    kernelFunc: function (_a) {
        var inputs = _a.inputs, backend = _a.backend, attrs = _a.attrs;
        var _b = inputs, x = _b.x, filter = _b.filter, dy = _b.dy;
        var _c = attrs, strides = _c.strides, pad = _c.pad, dilations = _c.dilations;
        var _d = tfjs_1.backend_util.computeDilation2DInfo(x.shape, filter.shape, strides, pad, 'NHWC' /* dataFormat */, dilations), dilationHeight = _d.dilationHeight, dilationWidth = _d.dilationWidth, padInfo = _d.padInfo, strideHeight = _d.strideHeight, strideWidth = _d.strideWidth;
        var $strides = [1, strideHeight, strideWidth, 1];
        var $dilations = [1, dilationHeight, dilationWidth, 1];
        var nodeBackend = backend;
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', x.dtype),
            { name: 'strides', type: nodeBackend.binding.TF_ATTR_INT, value: $strides },
            { name: 'rates', type: nodeBackend.binding.TF_ATTR_INT, value: $dilations },
            {
                name: 'padding',
                type: nodeBackend.binding.TF_ATTR_STRING,
                value: padInfo.type
            }
        ];
        return nodeBackend.executeSingleOutput(tfjs_1.Dilation2DBackpropInput, opAttrs, [x, filter, dy]);
    }
};
