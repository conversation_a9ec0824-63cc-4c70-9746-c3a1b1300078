/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
export {};
//# sourceMappingURL=data:application/json;base64,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