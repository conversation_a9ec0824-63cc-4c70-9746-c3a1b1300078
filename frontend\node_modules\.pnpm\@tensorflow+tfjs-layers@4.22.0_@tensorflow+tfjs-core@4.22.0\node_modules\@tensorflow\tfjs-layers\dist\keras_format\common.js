/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
export const VALID_DATA_FORMAT_VALUES = ['channelsFirst', 'channelsLast'];
export const VALID_INTERPOLATION_FORMAT_VALUES = ['nearest', 'bilinear'];
export const VALID_PADDING_MODE_VALUES = ['valid', 'same', 'causal'];
export const VALID_POOL_MODE_VALUES = ['max', 'avg'];
export const VALID_BIDIRECTIONAL_MERGE_MODES = ['sum', 'mul', 'concat', 'ave'];
export const VALID_SAMPLE_WEIGHT_MODES = ['temporal'];
//# sourceMappingURL=data:application/json;base64,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