{"name": "@tensorflow/tfjs-layers", "version": "4.22.0", "description": "TensorFlow layers API in JavaScript", "license": "Apache-2.0 AND MIT", "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs.git", "directory": "tfjs-layers"}, "private": false, "main": "dist/tf-layers.node.js", "types": "dist/index.d.ts", "jsnext:main": "dist/index.js", "module": "dist/index.js", "jsdelivr": "dist/tf-layers.min.js", "unpkg": "dist/tf-layers.min.js", "miniprogram": "dist/miniprogram", "devDependencies": {"@bazel/bazelisk": "^1.12.0", "clang-format": "~1.8.0"}, "scripts": {"build": "bazel build :tfjs-layers_pkg", "format": "./tools/clang_format_ts.sh", "publish-npm": "bazel run :tfjs-layers_pkg.publish", "test": "bazel test :tests --test_output=all", "test-dev": "ibazel test :tests --test_output=streamed", "test-debug": "yarn test-webgl2-debug", "test-browser": "yarn test-webgl2", "test-browser-debug": "yarn test-webgl2-debug", "test-webgl2": "bazel test :tfjs-layers_webgl2_test --test_output=streamed", "test-webgl2-debug": "bazel run :tfjs-layers_webgl2_test --test_output=streamed", "test-webgl2-dev": "ibazel test :tfjs-layers_webgl2_test --test_output=streamed", "test-webgl1": "bazel test :tfjs-layers_webgl1_test --test_output=streamed", "test-webgl1-debug": "bazel run :tfjs-layers_webgl1_test --test_output=streamed", "test-webgl1-dev": "ibazel test :tfjs-layers_webgl1_test --test_output=streamed", "run-browserstack": "bazel test :browserstack_bs_chrome_mac_tfjs-layers_webgl2_test"}, "peerDependencies": {"@tensorflow/tfjs-core": "4.22.0"}}