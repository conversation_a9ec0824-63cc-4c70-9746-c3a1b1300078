/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
import { inputLayerClassNames } from '../input_config';
import { advancedActivationLayerClassNames } from './advanced_activation_serialization';
import { convolutionalDepthwiseLayerClassNames } from './convolutional_depthwise_serialization';
import { convolutionalLayerClassNames } from './convolutional_serialization';
import { coreLayerClassNames } from './core_serialization';
import { embeddingLayerClassNames } from './embeddings_serialization';
import { mergeLayerClassNames } from './merge_serialization';
import { normalizationLayerClassNames } from './normalization_serialization';
import { paddingLayerClassNames } from './padding_serialization';
import { poolingLayerClassNames } from './pooling_serialization';
import { recurrentLayerClassNames } from './recurrent_serialization';
/**
 * A string array of valid Layer class names.
 *
 * This is guaranteed to match the `LayerClassName` union type.
 */
export const layerClassNames = [
    ...advancedActivationLayerClassNames,
    ...convolutionalDepthwiseLayerClassNames, ...convolutionalLayerClassNames,
    ...coreLayerClassNames, ...embeddingLayerClassNames, ...mergeLayerClassNames,
    ...normalizationLayerClassNames, ...paddingLayerClassNames,
    ...poolingLayerClassNames, ...recurrentLayerClassNames,
    ...inputLayerClassNames
];
//# sourceMappingURL=data:application/json;base64,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