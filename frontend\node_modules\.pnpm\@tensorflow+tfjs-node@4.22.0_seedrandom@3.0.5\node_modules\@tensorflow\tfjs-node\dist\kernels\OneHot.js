"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.oneHotConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.oneHotConfig = {
    kernelName: tfjs_1.OneHot,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var indices = args.inputs.indices;
        var backend = args.backend;
        var _a = args.attrs, dtype = _a.dtype, depth = _a.depth, onValue = _a.onValue, offValue = _a.offValue;
        var depthTensor = (0, tfjs_1.scalar)(depth, 'int32');
        var onValueTensor = (0, tfjs_1.scalar)(onValue, dtype);
        var offValueTensor = (0, tfjs_1.scalar)(offValue, dtype);
        var opAttrs = [
            { name: 'axis', type: backend.binding.TF_ATTR_INT, value: -1 },
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', dtype),
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('TI', indices.dtype)
        ];
        var res = backend.executeSingleOutput(tfjs_1.OneHot, opAttrs, [indices, depthTensor, onValueTensor, offValueTensor]);
        depthTensor.dispose();
        onValueTensor.dispose();
        offValueTensor.dispose();
        return res;
    }
};
