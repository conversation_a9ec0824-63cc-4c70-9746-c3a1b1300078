"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.sparseToDenseConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.sparseToDenseConfig = {
    kernelName: tfjs_1.SparseToDense,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var _a = args.inputs, sparseIndices = _a.sparseIndices, sparseValues = _a.sparseValues, defaultValue = _a.defaultValue;
        var backend = args.backend;
        var outputShape = args.attrs.outputShape;
        var opAttrs = [
            {
                name: 'validate_indices',
                type: backend.binding.TF_ATTR_BOOL,
                value: true
            },
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', sparseValues.dtype),
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('Tindices', sparseIndices.dtype)
        ];
        var outputShapeTensor = (0, tfjs_1.tensor1d)(outputShape, 'int32');
        var res = backend.executeSingleOutput(tfjs_1.SparseToDense, opAttrs, [sparseIndices, outputShapeTensor, sparseValues, defaultValue]);
        outputShapeTensor.dispose();
        return res;
    }
};
