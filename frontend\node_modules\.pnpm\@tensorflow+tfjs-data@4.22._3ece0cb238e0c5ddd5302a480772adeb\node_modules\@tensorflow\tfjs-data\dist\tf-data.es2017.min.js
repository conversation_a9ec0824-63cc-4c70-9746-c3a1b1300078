/**
 * @license
 * Copyright 2024 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@tensorflow/tfjs-core")):"function"==typeof define&&define.amd?define(["exports","@tensorflow/tfjs-core"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).tf=t.tf||{},t.tf)}(this,(function(t,e){"use strict";function r(t){var e=Object.create(null);return t&&Object.keys(t).forEach((function(r){if("default"!==r){var s=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,s.get?s:{enumerable:!0,get:function(){return t[r]}})}})),e.default=t,e}var s=r(e),n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function i(t){if(t.__esModule)return t;var e=t.default;if("function"==typeof e){var r=function t(){if(this instanceof t){var r=[null];r.push.apply(r,arguments);var s=Function.bind.apply(e,r);return new s}return e.apply(this,arguments)};r.prototype=e.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(t).forEach((function(e){var s=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(r,e,s.get?s:{enumerable:!0,get:function(){return t[e]}})})),r}var a={exports:{}};!function(t){!function(t,e,r){function s(t){var e,r=this,s=(e=4022871197,function(t){t=String(t);for(var r=0;r<t.length;r++){var s=.02519603282416938*(e+=t.charCodeAt(r));s-=e=s>>>0,e=(s*=e)>>>0,e+=4294967296*(s-=e)}return 2.3283064365386963e-10*(e>>>0)});r.next=function(){var t=2091639*r.s0+2.3283064365386963e-10*r.c;return r.s0=r.s1,r.s1=r.s2,r.s2=t-(r.c=0|t)},r.c=1,r.s0=s(" "),r.s1=s(" "),r.s2=s(" "),r.s0-=s(t),r.s0<0&&(r.s0+=1),r.s1-=s(t),r.s1<0&&(r.s1+=1),r.s2-=s(t),r.s2<0&&(r.s2+=1),s=null}function n(t,e){return e.c=t.c,e.s0=t.s0,e.s1=t.s1,e.s2=t.s2,e}function i(t,e){var r=new s(t),i=e&&e.state,a=r.next;return a.int32=function(){return 4294967296*r.next()|0},a.double=function(){return a()+11102230246251565e-32*(2097152*a()|0)},a.quick=a,i&&("object"==typeof i&&n(i,r),a.state=function(){return n(r,{})}),a}e&&e.exports?e.exports=i:r&&r.amd?r((function(){return i})):this.alea=i}(0,t,!1)}(a);var o=a.exports,u={exports:{}};!function(t){!function(t,e,r){function s(t){var e=this,r="";e.x=0,e.y=0,e.z=0,e.w=0,e.next=function(){var t=e.x^e.x<<11;return e.x=e.y,e.y=e.z,e.z=e.w,e.w^=e.w>>>19^t^t>>>8},t===(0|t)?e.x=t:r+=t;for(var s=0;s<r.length+64;s++)e.x^=0|r.charCodeAt(s),e.next()}function n(t,e){return e.x=t.x,e.y=t.y,e.z=t.z,e.w=t.w,e}function i(t,e){var r=new s(t),i=e&&e.state,a=function(){return(r.next()>>>0)/4294967296};return a.double=function(){do{var t=((r.next()>>>11)+(r.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},a.int32=r.next,a.quick=a,i&&("object"==typeof i&&n(i,r),a.state=function(){return n(r,{})}),a}e&&e.exports?e.exports=i:r&&r.amd?r((function(){return i})):this.xor128=i}(0,t,!1)}(u);var h=u.exports,l={exports:{}};!function(t){!function(t,e,r){function s(t){var e=this,r="";e.next=function(){var t=e.x^e.x>>>2;return e.x=e.y,e.y=e.z,e.z=e.w,e.w=e.v,(e.d=e.d+362437|0)+(e.v=e.v^e.v<<4^t^t<<1)|0},e.x=0,e.y=0,e.z=0,e.w=0,e.v=0,t===(0|t)?e.x=t:r+=t;for(var s=0;s<r.length+64;s++)e.x^=0|r.charCodeAt(s),s==r.length&&(e.d=e.x<<10^e.x>>>4),e.next()}function n(t,e){return e.x=t.x,e.y=t.y,e.z=t.z,e.w=t.w,e.v=t.v,e.d=t.d,e}function i(t,e){var r=new s(t),i=e&&e.state,a=function(){return(r.next()>>>0)/4294967296};return a.double=function(){do{var t=((r.next()>>>11)+(r.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},a.int32=r.next,a.quick=a,i&&("object"==typeof i&&n(i,r),a.state=function(){return n(r,{})}),a}e&&e.exports?e.exports=i:r&&r.amd?r((function(){return i})):this.xorwow=i}(0,t,!1)}(l);var c=l.exports,f={exports:{}};!function(t){!function(t,e,r){function s(t){var e=this;e.next=function(){var t,r,s=e.x,n=e.i;return t=s[n],r=(t^=t>>>7)^t<<24,r^=(t=s[n+1&7])^t>>>10,r^=(t=s[n+3&7])^t>>>3,r^=(t=s[n+4&7])^t<<7,t=s[n+7&7],r^=(t^=t<<13)^t<<9,s[n]=r,e.i=n+1&7,r},function(t,e){var r,s=[];if(e===(0|e))s[0]=e;else for(e=""+e,r=0;r<e.length;++r)s[7&r]=s[7&r]<<15^e.charCodeAt(r)+s[r+1&7]<<13;for(;s.length<8;)s.push(0);for(r=0;r<8&&0===s[r];++r);for(8==r?s[7]=-1:s[r],t.x=s,t.i=0,r=256;r>0;--r)t.next()}(e,t)}function n(t,e){return e.x=t.x.slice(),e.i=t.i,e}function i(t,e){null==t&&(t=+new Date);var r=new s(t),i=e&&e.state,a=function(){return(r.next()>>>0)/4294967296};return a.double=function(){do{var t=((r.next()>>>11)+(r.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},a.int32=r.next,a.quick=a,i&&(i.x&&n(i,r),a.state=function(){return n(r,{})}),a}e&&e.exports?e.exports=i:r&&r.amd?r((function(){return i})):this.xorshift7=i}(0,t,!1)}(f);var d=f.exports,m={exports:{}};!function(t){!function(t,e,r){function s(t){var e=this;e.next=function(){var t,r,s=e.w,n=e.X,i=e.i;return e.w=s=s+1640531527|0,r=n[i+34&127],t=n[i=i+1&127],r^=r<<13,t^=t<<17,r^=r>>>15,t^=t>>>12,r=n[i]=r^t,e.i=i,r+(s^s>>>16)|0},function(t,e){var r,s,n,i,a,o=[],u=128;for(e===(0|e)?(s=e,e=null):(e+="\0",s=0,u=Math.max(u,e.length)),n=0,i=-32;i<u;++i)e&&(s^=e.charCodeAt((i+32)%e.length)),0===i&&(a=s),s^=s<<10,s^=s>>>15,s^=s<<4,s^=s>>>13,i>=0&&(a=a+1640531527|0,n=0==(r=o[127&i]^=s+a)?n+1:0);for(n>=128&&(o[127&(e&&e.length||0)]=-1),n=127,i=512;i>0;--i)s=o[n+34&127],r=o[n=n+1&127],s^=s<<13,r^=r<<17,s^=s>>>15,r^=r>>>12,o[n]=s^r;t.w=a,t.X=o,t.i=n}(e,t)}function n(t,e){return e.i=t.i,e.w=t.w,e.X=t.X.slice(),e}function i(t,e){null==t&&(t=+new Date);var r=new s(t),i=e&&e.state,a=function(){return(r.next()>>>0)/4294967296};return a.double=function(){do{var t=((r.next()>>>11)+(r.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},a.int32=r.next,a.quick=a,i&&(i.X&&n(i,r),a.state=function(){return n(r,{})}),a}e&&e.exports?e.exports=i:r&&r.amd?r((function(){return i})):this.xor4096=i}(0,t,!1)}(m);var p=m.exports,w={exports:{}};!function(t){!function(t,e,r){function s(t){var e=this,r="";e.next=function(){var t=e.b,r=e.c,s=e.d,n=e.a;return t=t<<25^t>>>7^r,r=r-s|0,s=s<<24^s>>>8^n,n=n-t|0,e.b=t=t<<20^t>>>12^r,e.c=r=r-s|0,e.d=s<<16^r>>>16^n,e.a=n-t|0},e.a=0,e.b=0,e.c=-1640531527,e.d=1367130551,t===Math.floor(t)?(e.a=t/4294967296|0,e.b=0|t):r+=t;for(var s=0;s<r.length+20;s++)e.b^=0|r.charCodeAt(s),e.next()}function n(t,e){return e.a=t.a,e.b=t.b,e.c=t.c,e.d=t.d,e}function i(t,e){var r=new s(t),i=e&&e.state,a=function(){return(r.next()>>>0)/4294967296};return a.double=function(){do{var t=((r.next()>>>11)+(r.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},a.int32=r.next,a.quick=a,i&&("object"==typeof i&&n(i,r),a.state=function(){return n(r,{})}),a}e&&e.exports?e.exports=i:r&&r.amd?r((function(){return i})):this.tychei=i}(0,t,!1)}(w);var y=w.exports,g={exports:{}},x=i({__proto__:null,default:{}});!function(t){!function(e,r,s){var n,i=256,a=s.pow(i,6),o=s.pow(2,52),u=2*o,h=255;function l(t,h,l){var w=[],y=m(d((h=1==h?{entropy:!0}:h||{}).entropy?[t,p(r)]:null==t?function(){try{var t;return n&&(t=n.randomBytes)?t=t(i):(t=new Uint8Array(i),(e.crypto||e.msCrypto).getRandomValues(t)),p(t)}catch(t){var s=e.navigator,a=s&&s.plugins;return[+new Date,e,a,e.screen,p(r)]}}():t,3),w),g=new c(w),x=function(){for(var t=g.g(6),e=a,r=0;t<o;)t=(t+r)*i,e*=i,r=g.g(1);for(;t>=u;)t/=2,e/=2,r>>>=1;return(t+r)/e};return x.int32=function(){return 0|g.g(4)},x.quick=function(){return g.g(4)/4294967296},x.double=x,m(p(g.S),r),(h.pass||l||function(t,e,r,n){return n&&(n.S&&f(n,g),t.state=function(){return f(g,{})}),r?(s.random=t,e):t})(x,y,"global"in h?h.global:this==s,h.state)}function c(t){var e,r=t.length,s=this,n=0,a=s.i=s.j=0,o=s.S=[];for(r||(t=[r++]);n<i;)o[n]=n++;for(n=0;n<i;n++)o[n]=o[a=h&a+t[n%r]+(e=o[n])],o[a]=e;(s.g=function(t){for(var e,r=0,n=s.i,a=s.j,o=s.S;t--;)e=o[n=h&n+1],r=r*i+o[h&(o[n]=o[a=h&a+e])+(o[a]=e)];return s.i=n,s.j=a,r})(i)}function f(t,e){return e.i=t.i,e.j=t.j,e.S=t.S.slice(),e}function d(t,e){var r,s=[],n=typeof t;if(e&&"object"==n)for(r in t)try{s.push(d(t[r],e-1))}catch(t){}return s.length?s:"string"==n?t:t+"\0"}function m(t,e){for(var r,s=t+"",n=0;n<s.length;)e[h&n]=h&(r^=19*e[h&n])+s.charCodeAt(n++);return p(e)}function p(t){return String.fromCharCode.apply(0,t)}if(m(s.random(),r),t.exports){t.exports=l;try{n=x}catch(t){}}else s.seedrandom=l}("undefined"!=typeof self?self:n,[],Math)}(g);var v=o,b=h,C=c,z=d,E=p,S=y,R=g.exports;R.alea=v,R.xor128=b,R.xorwow=C,R.xorshift7=z,R.xor4096=E,R.tychei=S;var A,T=R;function F(t,e,r=new Map,s=new Set){if(null==t)return null;if("function"==typeof Blob&&t instanceof Blob)return t.slice();if(s.has(t))throw new Error("Circular references are not supported.");if(r.has(t))return r.get(t);const n=e(t);if(n.recurse&&null!==n.value)throw new Error("A deep map function may not return both a value and recurse=true.");if(n.recurse){if(O(t)){const n=Array.isArray(t)?[]:{};s.add(t);for(const i in t){const a=F(t[i],e,r,s);n[i]=a}return s.delete(t),t.__proto__&&(n.__proto__=t.__proto__),n}throw new Error(`Can't recurse into non-iterable type: ${t}`)}return r.set(t,n.value),n.value}function k(t,e=D){return N(t,e)}function N(t,e,r=new Set){const s=t[0];if(r.has(s))throw new Error("Circular references are not supported.");const n=e(t);if(n.recurse&&null!==n.value)throw new Error("A deep zip function may not return both a value and recurse=true.");if(n.recurse){if(O(s)){const n=Array.isArray(s)?[]:{};r.add(s);for(const i in s){const s=N(t.map((t=>t[i])),e,r);n[i]=s}return r.delete(s),n}throw new Error(`Can't recurse into non-iterable type: ${s}`)}return n.value}function D(t){return null===t?null:O(t[0])?{value:null,recurse:!0}:{value:t,recurse:!1}}async function I(t,e){const r=new Map;F(t,e,r);for(const t of Array.from(r.keys())){const e=r.get(t);if(s.util.isPromise(e)){const s=await e;r.set(t,s)}}return F(t,e,r)}function O(t){let e=!1;if(s.env().get("IS_BROWSER"))e=t instanceof TextDecoder;else{const{StringDecoder:r}=require("string_decoder");e=t instanceof r}return null!=t&&!ArrayBuffer.isView(t)&&(Array.isArray(t)||"object"==typeof t&&!(t instanceof s.Tensor)&&!(t instanceof Promise)&&!e)}function _(t){return F(t,j)}function j(t){return t instanceof s.Tensor?{value:t.clone(),recurse:!1}:O(t)?{value:null,recurse:!0}:{value:t,recurse:!1}}class M{constructor(t){if(this.capacity=t,this.begin=0,this.end=0,null==t)throw new RangeError("Can't create a ring buffer of unknown capacity.");if(t<1)throw new RangeError("Can't create ring buffer of capacity < 1.");this.data=new Array(t),this.doubledCapacity=2*t}wrap(t){for(;t<0;)t+=this.doubledCapacity;return t%this.doubledCapacity}get(t){if(t<0)throw new RangeError("Can't get item at a negative index.");return this.data[t%this.capacity]}set(t,e){if(t<0)throw new RangeError("Can't set item at a negative index.");this.data[t%this.capacity]=e}length(){let t=this.end-this.begin;return t<0&&(t=this.doubledCapacity+t),t}isFull(){return this.length()===this.capacity}isEmpty(){return 0===this.length()}push(t){if(this.isFull())throw new RangeError("Ring buffer is full.");this.set(this.end,t),this.end=this.wrap(this.end+1)}pushAll(t){for(const e of t)this.push(e)}pop(){if(this.isEmpty())throw new RangeError("Ring buffer is empty.");this.end=this.wrap(this.end-1);const t=this.get(this.end);return this.set(this.end,void 0),t}unshift(t){if(this.isFull())throw new RangeError("Ring buffer is full.");this.begin=this.wrap(this.begin-1),this.set(this.begin,t)}shift(){if(this.isEmpty())throw new RangeError("Ring buffer is empty.");const t=this.get(this.begin);return this.set(this.begin,void 0),this.begin=this.wrap(this.begin+1),t}shuffleExcise(t){if(this.isEmpty())throw new RangeError("Ring buffer is empty.");const e=this.wrap(this.begin+t),r=this.get(e);return this.set(e,this.pop()),r}}class B extends M{constructor(){super(B.INITIAL_CAPACITY)}isFull(){return!1}push(t){super.isFull()&&this.expand(),super.push(t)}unshift(t){super.isFull()&&this.expand(),super.unshift(t)}expand(){const t=2*this.capacity,e=new Array(t),r=this.length();for(let t=0;t<r;t++)e[t]=this.get(this.wrap(this.begin+t));this.data=e,this.capacity=t,this.doubledCapacity=2*this.capacity,this.begin=0,this.end=r}}function $(t){return new q(t)}function L(t){return new H(t)}B.INITIAL_CAPACITY=32;class P{async toArray(){const t=[];let e=await this.next();for(;!e.done;)t.push(e.value),e=await this.next();return t}async toArrayForTest(){const t=this.prefetch(100),e=[];let r=await t.next();for(;!r.done;)e.push(r.value),r=await t.next();return e}async resolveFully(){let t=await this.next();for(;!t.done;)t=await this.next()}async resolveWhile(t){let e=await this.next(),r=t(e.value);for(;!e.done&&r;)e=await this.next(),r=t(e.value)}handleErrors(t){return new Z(this,t)}filter(t){return new X(this,t)}map(t){return new G(this,t)}mapAsync(t){return new J(this,t)}serialMapAsync(t){return new J(this,t).serial()}flatmap(t){return new K(this,t)}async forEachAsync(t){return this.map(t).resolveFully()}async serialForEach(t){return this.serialMapAsync(t).resolveWhile((t=>!0===t))}rowMajorBatch(t,e=!0){return new Q(this,t,e)}columnMajorBatch(t,e=!0,r=D){return this.rowMajorBatch(t,e).map((t=>k(t,r)))}concatenate(t,e){return new tt($([this,t]),e)}take(t){return t<0||null==t?this:new U(this,t)}skip(t){return t<0||null==t?this:new V(this,t)}prefetch(t){return new rt(this,t)}shuffle(t,e){return new st(this,t,e)}serial(){return new W(this)}}class q extends P{constructor(t){super(),this.items=t,this.trav=0}summary(){return`Array of ${this.items.length} items`}async next(){if(this.trav>=this.items.length)return{value:null,done:!0};const t=this.items[this.trav];return this.trav++,{value:_(t),done:!1}}}class H extends P{constructor(t){super(),this.nextFn=t}summary(){return"Function call"}async next(){try{return this.nextFn()}catch(t){throw t.message=`Error thrown while iterating through a dataset: ${t.message}`,t}}}class W extends P{constructor(t){super(),this.upstream=t,this.lastRead=Promise.resolve({value:null,done:!1})}summary(){return`${this.upstream.summary()} -> Serial`}async next(){return this.lastRead=this.lastRead.then((()=>this.serialNext())),this.lastRead}async serialNext(){return this.upstream.next()}}class V extends P{constructor(t,e){super(),this.upstream=t,this.maxCount=e,this.count=0,this.lastRead=Promise.resolve({value:null,done:!1})}summary(){return`${this.upstream.summary()} -> Skip`}async next(){return this.lastRead=this.lastRead.then((()=>this.serialNext())),this.lastRead}async serialNext(){for(;this.count++<this.maxCount;){const t=await this.upstream.next();if(t.done)return t;s.dispose(t.value)}return this.upstream.next()}}class U extends P{constructor(t,e){super(),this.upstream=t,this.maxCount=e,this.count=0}summary(){return`${this.upstream.summary()} -> Take`}async next(){return this.count++>=this.maxCount?{value:null,done:!0}:this.upstream.next()}}class Q extends P{constructor(t,e,r=!0){super(),this.upstream=t,this.batchSize=e,this.enableSmallLastBatch=r,this.lastRead=Promise.resolve({value:null,done:!1})}summary(){return`${this.upstream.summary()} -> RowMajorBatch`}async next(){return this.lastRead=this.lastRead.then((()=>this.serialNext())),this.lastRead}async serialNext(){const t=[];for(;t.length<this.batchSize;){const e=await this.upstream.next();if(e.done)return this.enableSmallLastBatch&&t.length>0?{value:t,done:!1}:{value:null,done:!0};t.push(e.value)}return{value:t,done:!1}}}class X extends P{constructor(t,e){super(),this.upstream=t,this.predicate=e,this.lastRead=Promise.resolve({value:null,done:!1})}summary(){return`${this.upstream.summary()} -> Filter`}async next(){return this.lastRead=this.lastRead.then((()=>this.serialNext())),this.lastRead}async serialNext(){for(;;){const t=await this.upstream.next();if(t.done||this.predicate(t.value))return t;s.dispose(t.value)}}}class G extends P{constructor(t,e){super(),this.upstream=t,this.transform=e}summary(){return`${this.upstream.summary()} -> Map`}async next(){const t=await this.upstream.next();if(t.done)return{value:null,done:!0};const e=s.tensor_util.getTensorsInContainer(t.value),r=this.transform(t.value),n=s.tensor_util.getTensorsInContainer(r);for(const t of e)s.tensor_util.isTensorInList(t,n)||t.dispose();return{value:r,done:!1}}}class Z extends P{constructor(t,e){super(),this.upstream=t,this.handler=e,this.count=0,this.lastRead=Promise.resolve({value:null,done:!1})}summary(){return`${this.upstream.summary()} -> handleErrors`}async next(){return this.lastRead=this.lastRead.then((()=>this.serialNext())),this.lastRead}async serialNext(){for(;;)try{return await this.upstream.next()}catch(t){if(!this.handler(t))return{value:null,done:!0}}}}class J extends P{constructor(t,e){super(),this.upstream=t,this.transform=e}summary(){return`${this.upstream.summary()} -> AsyncMap`}async next(){const t=await this.upstream.next();if(t.done)return{value:null,done:!0};const e=s.tensor_util.getTensorsInContainer(t.value),r=await this.transform(t.value),n=s.tensor_util.getTensorsInContainer(r);for(const t of e)s.tensor_util.isTensorInList(t,n)||t.dispose();return{value:r,done:!1}}}class Y extends P{constructor(){super(),this.outputQueue=new B,this.lastRead=Promise.resolve({value:null,done:!1})}async next(){return this.lastRead=this.lastRead.then((()=>this.serialNext())),this.lastRead}async serialNext(){for(;0===this.outputQueue.length();)if(!await this.pump())return{value:null,done:!0};return{value:this.outputQueue.shift(),done:!1}}}class K extends Y{constructor(t,e){super(),this.upstream=t,this.transform=e}summary(){return`${this.upstream.summary()} -> Flatmap`}async pump(){const t=await this.upstream.next();if(t.done)return!1;const e=s.tensor_util.getTensorsInContainer(t.value),r=this.transform(t.value),n=s.tensor_util.getTensorsInContainer(r);this.outputQueue.pushAll(r);for(const t of e)s.tensor_util.isTensorInList(t,n)||t.dispose();return!0}}class tt extends P{constructor(t,e){super(),this.baseErrorHandler=e,this.lastRead=null,this.iterator=null,this.moreIterators=t}summary(){return"TODO: fill in upstream of chained summaries -> Chained"}async next(){return this.lastRead=this.readFromChain(this.lastRead),this.lastRead}async readFromChain(t){if(await t,null==this.iterator){const t=await this.moreIterators.next();if(t.done)return{value:null,done:!0};this.iterator=t.value,null!=this.baseErrorHandler&&(this.iterator=this.iterator.handleErrors(this.baseErrorHandler))}const e=await this.iterator.next();return e.done?(this.iterator=null,this.readFromChain(t)):e}}!function(t){t[t.FAIL=0]="FAIL",t[t.SHORTEST=1]="SHORTEST",t[t.LONGEST=2]="LONGEST"}(A||(A={}));class et extends P{constructor(t,e=A.FAIL){super(),this.iterators=t,this.mismatchMode=e,this.count=0,this.currentPromise=null}summary(){return"{TODO: fill in upstream of zip summaries} -> Zip"}async nextState(t){await t;let e=0,r=0;const s=await I(this.iterators,(function(t){if(t instanceof P){return{value:t.next().then((t=>(e++,t.done&&r++,t.value))),recurse:!1}}return{value:null,recurse:!0}}));if(e===r)return{value:null,done:!0};if(r>0)switch(this.mismatchMode){case A.FAIL:throw new Error(`Zipped streams should have the same length. Mismatched at element ${this.count}.`);case A.SHORTEST:return{value:null,done:!0};case A.LONGEST:}return this.count++,{value:s,done:!1}}async next(){return this.currentPromise=this.nextState(this.currentPromise),this.currentPromise}}class rt extends P{constructor(t,e){super(),this.upstream=t,this.bufferSize=e,this.buffer=new M(e)}summary(){return`${this.upstream.summary()} -> Prefetch`}refill(){for(;!this.buffer.isFull();){const t=this.upstream.next();this.buffer.push(t)}}next(){return this.refill(),this.buffer.shift()}}class st extends rt{constructor(t,e,r){super(t,e),this.upstream=t,this.windowSize=e,this.upstreamExhausted=!1,this.random=T.alea(r||s.util.now().toString()),this.lastRead=Promise.resolve({value:null,done:!1})}async next(){return this.lastRead=this.lastRead.then((()=>this.serialNext())),this.lastRead}randomInt(t){return Math.floor(this.random()*t)}chooseIndex(){return this.randomInt(this.buffer.length())}async serialNext(){for(this.upstreamExhausted||this.refill();!this.buffer.isEmpty();){const t=this.chooseIndex(),e=await this.buffer.shuffleExcise(t);if(!e.done)return this.refill(),e;this.upstreamExhausted=!0}return{value:null,done:!0}}}class nt{constructor(){this.size=null}batch(t,e=!0){const r=this;let n;return s.util.assert(t>0,(()=>`batchSize needs to be positive, but it is\n      ${t}`)),n=this.size===1/0||null==this.size?this.size:e?Math.ceil(this.size/t):Math.floor(this.size/t),it((async()=>(await r.iterator()).columnMajorBatch(t,e,at)),n)}concatenate(t){const e=this;let r;return r=this.size===1/0||t.size===1/0?1/0:null!=this.size&&null!=t.size?this.size+t.size:null,it((async()=>(await e.iterator()).concatenate(await t.iterator())),r)}filter(t){const e=this;let r;return r=this.size===1/0?1/0:null,it((async()=>(await e.iterator()).filter((e=>s.tidy((()=>t(e)))))),r)}async forEachAsync(t){return(await this.iterator()).forEachAsync(t)}map(t){const e=this;return it((async()=>(await e.iterator()).map((e=>s.tidy((()=>t(e)))))),this.size)}mapAsync(t){const e=this;return it((async()=>(await e.iterator()).mapAsync(t)),this.size)}prefetch(t){if(null==t)throw new RangeError("`Dataset.prefetch()` requires bufferSize to be specified.");const e=this;return it((async()=>(await e.iterator()).prefetch(t)),this.size)}repeat(t){const e=this;let r;return r=null!=this.size&&t>0?this.size*t:0===t?0:null!=this.size&&(void 0===t||t<0)?1/0:null,it((async()=>{const r=L((async()=>({value:await e.iterator(),done:!1})));return s=r.take(t),new tt(s,n);var s,n}),r)}skip(t){const e=this;let r;return r=null!=this.size&&t>=0&&this.size>=t?this.size-t:null!=this.size&&(this.size<t||void 0===t||t<0)?0:null,it((async()=>(await e.iterator()).skip(t)),r)}shuffle(t,e,r=!0){if(null==t||t<0)throw null==this.size?new RangeError("`Dataset.shuffle()` requires bufferSize to be specified."):new RangeError(`\`Dataset.shuffle()\` requires bufferSize to be specified.  If your data fits in main memory (for regular JS objects), and/or GPU memory (for \`tf.Tensor\`s), consider setting bufferSize to the dataset size (${this.size} elements)`);const n=this,i=T.alea(e||s.util.now().toString());return it((async()=>{let e=i.int32();return r&&(e+=i.int32()),(await n.iterator()).shuffle(t,e.toString())}),this.size)}take(t){const e=this;let r;return r=null!=this.size&&this.size>t?t:null!=this.size&&this.size<=t?this.size:null,it((async()=>(await e.iterator()).take(t)),r)}async toArray(){if(this.size===1/0)throw new Error("Can not convert infinite data stream to array.");return(await this.iterator()).toArray()}async toArrayForTest(){if(this.size===1/0)throw new Error("Can not convert infinite data stream to array.");return(await this.iterator()).toArrayForTest()}}function it(t,e=null){return new class extends nt{constructor(){super(...arguments),this.size=e}async iterator(){return t()}}}function at(t){if(null===t)return null;const e=t[0];if(null==(r=e)||null===(n=r)||"object"!=typeof n&&"function"!=typeof n||Array.isArray(r)||"object"==typeof r&&r instanceof s.Tensor||s.util.isTypedArray(r)){return{value:function(t){if(0===t.length)throw new Error("Can't make a batch of zero elements.");return t[0]instanceof s.Tensor?s.stack(t):s.tensor(t)}(t),recurse:!1}}var r,n;return{value:null,recurse:!0}}nt.MAX_BUFFER_SIZE=1e4;class ot extends nt{constructor(t){super(),this.input=t}async iterator(){return(await this.input.iterator()).decodeUTF8().split("\n").map((t=>(t.endsWith("\r")&&(t=t.slice(0,-1)),t)))}}const ut='"',ht=Symbol("out"),lt=Symbol("field"),ct=Symbol("quote"),ft=Symbol("quoteafterquote"),dt=Symbol("quoteinquote");class mt extends nt{async columnNames(){return this.columnNamesValidated||await this.setColumnNames(),this.configuredColumnsOnly?Object.keys(this.columnConfigs):this.fullColumnNames}async setColumnNames(){const t=await this.maybeReadHeaderLine();if(!this.fullColumnNames&&!t)throw new Error("Column names must be provided if there is no header line.");this.fullColumnNames&&t&&e.util.assert(t.length===this.fullColumnNames.length,(()=>"The length of provided columnNames ("+this.fullColumnNames.length.toString()+") does not match the length of the header line read from file ("+t.length.toString()+").")),this.fullColumnNames||(this.fullColumnNames=t);const r=this.fullColumnNames.reduce(((t,e)=>(t[e]=t[e]+1||1,t)),{}),s=Object.keys(r).filter((t=>r[t]>1));if(e.util.assert(0===s.length,(()=>"Duplicate column names found: "+s.toString())),this.columnConfigs)for(const t of Object.keys(this.columnConfigs)){if(-1===this.fullColumnNames.indexOf(t))throw new Error('The key "'+t+'" provided in columnConfigs does not match any of the column names ('+this.fullColumnNames.toString()+").")}this.columnNamesValidated=!0}async maybeReadHeaderLine(){if(this.hasHeader){const t=await this.base.iterator(),e=await t.next();if(e.done)throw new Error("No data was found for CSV parsing.");const r=e.value;return this.parseRow(r,!1)}return null}constructor(t,r){super(),this.input=t,this.hasHeader=!0,this.fullColumnNames=null,this.columnNamesValidated=!1,this.columnConfigs=null,this.configuredColumnsOnly=!1,this.delimiter=",",this.delimWhitespace=!1,this.base=new ot(t),r||(r={}),this.hasHeader=!1!==r.hasHeader,this.fullColumnNames=r.columnNames,this.columnConfigs=r.columnConfigs,this.configuredColumnsOnly=r.configuredColumnsOnly,r.delimWhitespace?(e.util.assert(null==r.delimiter,(()=>"Delimiter should not be provided when delimWhitespace is true.")),this.delimWhitespace=!0,this.delimiter=" "):this.delimiter=r.delimiter?r.delimiter:","}async iterator(){this.columnNamesValidated||await this.setColumnNames();let t=await this.base.iterator();return this.hasHeader&&(t=t.skip(1)),t.map((t=>this.makeDataElement(t)))}makeDataElement(t){const e=this.parseRow(t),r={},s={};for(let n=0;n<this.fullColumnNames.length;n++){const i=this.fullColumnNames[n],a=this.columnConfigs?this.columnConfigs[i]:null;if(!this.configuredColumnsOnly||a){const o=e[n];let u=null;if(""===o)if(a&&void 0!==a.default)u=a.default;else{if(a&&(a.required||a.isLabel))throw new Error(`Required column ${i} is empty in this line: ${t}`);u=void 0}else{const t=Number(o);if(isNaN(t))u=a&&"bool"===a.dtype?this.getBoolean(o):o;else if(a&&a.dtype)switch(a.dtype){case"float32":default:u=t;break;case"int32":u=Math.floor(t);break;case"bool":u=this.getBoolean(o)}else u=t}a&&a.isLabel?s[i]=u:r[i]=u}}return 0===Object.keys(s).length?r:{xs:r,ys:s}}getBoolean(t){return"1"===t||"true"===t.toLowerCase()?1:0}parseRow(t,e=!0){const r=[];let s=0;const n=t.length;let i=ht;for(let e=0;e<n;e++)switch(i){case ht:switch(t.charAt(e)){case ut:s=e+1,i=ct;break;case this.delimiter:if(s=e+1," "===this.delimiter&&this.delimWhitespace)break;r.push(""),i=ht;break;default:i=lt,s=e}break;case lt:if(t.charAt(e)===this.delimiter)r.push(t.substring(s,e)),i=ht,s=e+1;break;case ct:if(t.charAt(e)===ut)i=ft;break;case ft:switch(t.charAt(e)){case this.delimiter:r.push(t.substring(s,e-1)),i=ht,s=e+1;break;case ut:i=ct;break;default:i=dt}break;case dt:if(t.charAt(e)===ut)i=ct}if(i===ft?r.push(t.substring(s,n-1)):r.push(t.substring(s)),e&&r.length!==this.fullColumnNames.length)throw new Error(`Invalid row in csv file. Should have ${this.fullColumnNames.length} elements in a row, but got ${r}`);return r}}class pt extends P{constructor(t){super(),this.microphoneConfig=t,this.isClosed=!1,this.fftSize=t.fftSize||1024;const e=Math.log2(this.fftSize);if(this.fftSize<0||e<4||e>14||!Number.isInteger(e))throw new Error(`Invalid fftSize: it must be a power of 2 between 2 to 4 and 2 to 14, but got ${this.fftSize}`);if(this.numFrames=t.numFramesPerSpectrogram||43,this.sampleRateHz=t.sampleRateHz,this.columnTruncateLength=t.columnTruncateLength||this.fftSize,this.audioTrackConstraints=t.audioTrackConstraints,this.smoothingTimeConstant=t.smoothingTimeConstant||0,this.includeSpectrogram=!1!==t.includeSpectrogram,this.includeWaveform=!0===t.includeWaveform,!this.includeSpectrogram&&!this.includeWaveform)throw new Error("Both includeSpectrogram and includeWaveform are false. At least one type of data should be returned.")}summary(){return"microphone"}static async create(t={}){if(!e.env().get("IS_BROWSER"))throw new Error("microphone API is only supported in browser environment.");const r=new pt(t);return await r.start(),r}async start(){try{this.stream=await navigator.mediaDevices.getUserMedia({audio:null==this.audioTrackConstraints||this.audioTrackConstraints,video:!1})}catch(t){throw new Error(`Error thrown while initializing video stream: ${t.message}`)}if(!this.stream)throw new Error("Could not obtain audio from microphone.");const t=window.AudioContext||window.webkitAudioContext;if(this.audioContext=new t,this.sampleRateHz){if(this.audioContext.sampleRate!==this.sampleRateHz)throw new Error(`Mismatch in sampling rate: Expected: ${this.sampleRateHz}; Actual: ${this.audioContext.sampleRate}`)}else this.sampleRateHz=this.audioContext.sampleRate;const e=this.audioContext.createMediaStreamSource(this.stream);this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=2*this.fftSize,this.analyser.smoothingTimeConstant=this.smoothingTimeConstant,e.connect(this.analyser),this.freqData=new Float32Array(this.fftSize),this.timeData=new Float32Array(this.fftSize)}async next(){if(this.isClosed)return{value:null,done:!0};let t,e;const r=await this.getAudioData();if(this.includeSpectrogram){const e=this.flattenQueue(r.freqDataQueue);t=this.getTensorFromAudioDataArray(e,[this.numFrames,this.columnTruncateLength,1])}if(this.includeWaveform){const t=this.flattenQueue(r.timeDataQueue);e=this.getTensorFromAudioDataArray(t,[this.numFrames*this.fftSize,1])}return{value:{spectrogram:t,waveform:e},done:!1}}async capture(){return(await this.next()).value}async getAudioData(){const t=[],e=[];let r=0;return new Promise((s=>{const n=setInterval((()=>{this.includeSpectrogram&&(this.analyser.getFloatFrequencyData(this.freqData),this.freqData[0]===-1/0&&s({freqDataQueue:t,timeDataQueue:e}),t.push(this.freqData.slice(0,this.columnTruncateLength))),this.includeWaveform&&(this.analyser.getFloatTimeDomainData(this.timeData),e.push(this.timeData.slice())),++r===this.numFrames&&(clearInterval(n),s({freqDataQueue:t,timeDataQueue:e}))}),this.fftSize/this.sampleRateHz*1e3)}))}stop(){this.isClosed||(this.isClosed=!0,this.analyser.disconnect(),this.audioContext.close(),null!=this.stream&&this.stream.getTracks().length>0&&this.stream.getTracks()[0].stop())}toArray(){throw new Error("Can not convert infinite audio stream to array.")}getSampleRate(){return this.sampleRateHz}flattenQueue(t){const e=t[0].length,r=new Float32Array(t.length*e);return t.forEach(((t,s)=>r.set(t,s*e))),r}getTensorFromAudioDataArray(t,r){const s=new Float32Array(e.util.sizeFromShape(r));return s.set(t,s.length-t.length),e.tensor(s,r)}}class wt extends P{constructor(t,r){if(super(),this.webcamVideoElement=t,this.webcamConfig=r,this.isClosed=!0,this.resize=!1,this.needToResize())if(this.resize=!0,this.cropSize=[this.webcamConfig.resizeHeight,this.webcamConfig.resizeWidth],this.cropBoxInd=e.tensor1d([0],"int32"),this.webcamConfig.centerCrop){const t=1*this.webcamConfig.resizeWidth/this.webcamVideoElement.width,r=1*this.webcamConfig.resizeHeight/this.webcamVideoElement.height,s=(1-t)/2,n=(1-r)/2,i=s+t,a=r+n;this.cropBox=e.tensor2d([n,s,a,i],[1,4])}else this.cropBox=e.tensor2d([0,0,1,1],[1,4])}summary(){return"webcam"}static async create(t,r={}){if(!e.env().get("IS_BROWSER"))throw new Error("tf.data.webcam is only supported in browser environment.");if(!t){if(t=document.createElement("video"),!r.resizeWidth||!r.resizeHeight)throw new Error("Please provide webcam video element, or resizeWidth and resizeHeight to create a hidden video element.");t.width=r.resizeWidth,t.height=r.resizeHeight}const s=new wt(t,r);return await s.start(),s}async start(){this.webcamConfig.facingMode&&e.util.assert("user"===this.webcamConfig.facingMode||"environment"===this.webcamConfig.facingMode,(()=>`Invalid webcam facing mode: ${this.webcamConfig.facingMode}. Please provide 'user' or 'environment'`));try{this.stream=await navigator.mediaDevices.getUserMedia({video:{deviceId:this.webcamConfig.deviceId,facingMode:this.webcamConfig.facingMode?this.webcamConfig.facingMode:"user",width:this.webcamVideoElement.width,height:this.webcamVideoElement.height}})}catch(t){throw t.message=`Error thrown while initializing video stream: ${t.message}`,t}if(!this.stream)throw new Error("Could not obtain video from webcam.");try{this.webcamVideoElement.srcObject=this.stream}catch(t){console.log(t),this.webcamVideoElement.src=window.URL.createObjectURL(this.stream)}return this.webcamVideoElement.play(),this.isClosed=!1,new Promise((t=>{this.webcamVideoElement.onloadedmetadata=()=>{t()}}))}async next(){if(this.isClosed)return{value:null,done:!0};let t;try{t=e.browser.fromPixels(this.webcamVideoElement)}catch(t){throw new Error(`Error thrown converting video to pixels: ${JSON.stringify(t)}`)}if(!this.resize)return{value:t,done:!1};try{return{value:this.cropAndResizeFrame(t),done:!1}}catch(t){throw new Error(`Error thrown cropping the video: ${t.message}`)}finally{t.dispose()}}needToResize(){return!(!this.webcamConfig.resizeWidth||!this.webcamConfig.resizeHeight||this.webcamVideoElement.width===this.webcamConfig.resizeWidth&&this.webcamVideoElement.height===this.webcamConfig.resizeHeight)}cropAndResizeFrame(t){return e.tidy((()=>{const r=e.expandDims(e.cast(t,"float32"),0);let s;s=e.image.cropAndResize(r,this.cropBox,this.cropBoxInd,this.cropSize,"bilinear");const n=s.shape;return e.reshape(s,n.slice(1))}))}async capture(){return(await this.next()).value}stop(){this.stream.getTracks().forEach((t=>t.stop()));try{this.webcamVideoElement.srcObject=null}catch(t){console.log(t),this.webcamVideoElement.src=null}this.isClosed=!0}toArray(){throw new Error("Can not convert infinite video stream to array.")}}class yt{}class gt extends P{split(t){return new xt(this,t)}}class xt extends gt{constructor(t,e){super(),this.upstream=t,this.impl=new vt(t,e)}summary(){return this.impl.summary()}async next(){return this.impl.next()}}class vt extends Y{constructor(t,e){super(),this.upstream=t,this.separator=e,this.carryover=""}summary(){return`${this.upstream.summary()} -> Split('${this.separator}')`}async pump(){const t=await this.upstream.next();if(t.done)return""!==this.carryover&&(this.outputQueue.push(this.carryover),this.carryover="",!0);const e=t.value.split(this.separator);e[0]=this.carryover+e[0];for(const t of e.slice(0,-1))this.outputQueue.push(t);return this.carryover=e[e.length-1],!0}}class bt extends P{decodeUTF8(){return new Ct(this)}}class Ct extends gt{constructor(t){super(),this.upstream=t,this.impl=new zt(t)}summary(){return this.impl.summary()}async next(){return this.impl.next()}}class zt extends Y{constructor(t){if(super(),this.upstream=t,e.env().get("IS_BROWSER"))this.decoder=new TextDecoder("utf-8");else{const{StringDecoder:t}=require("string_decoder");this.decoder=new t("utf8")}}summary(){return`${this.upstream.summary()} -> Utf8`}async pump(){const t=await this.upstream.next();let r,s;return!t.done&&(r=t.value,s=e.env().get("IS_BROWSER")?this.decoder.decode(r,{stream:!0}):this.decoder.write(Buffer.from(r.buffer)),this.outputQueue.push(s),!0)}}class Et extends bt{constructor(t,r={}){super(),this.file=t,this.options=r,e.util.assert(t instanceof Uint8Array||!!e.env().get("IS_BROWSER")&&(t instanceof File||t instanceof Blob),(()=>"FileChunkIterator only supports File, Blob and Uint8Array right now.")),this.offset=r.offset||0,this.chunkSize=r.chunkSize||1048576}summary(){return`FileChunks ${this.file}`}async next(){if(this.offset>=(this.file instanceof Uint8Array?this.file.byteLength:this.file.size))return{value:null,done:!0};const t=new Promise(((t,e)=>{const r=this.offset+this.chunkSize;if(this.file instanceof Uint8Array)t(new Uint8Array(this.file.slice(this.offset,r)));else{const s=new FileReader;s.onload=r=>{let n=s.result;if(n instanceof ArrayBuffer&&(n=new Uint8Array(n)),!(n instanceof Uint8Array))return e(new TypeError("FileReader returned unknown type."));t(n)},s.onabort=t=>e(new Error("Aborted")),s.onerror=t=>e(new Error(t.type));const n=this.file.slice(this.offset,r);s.readAsArrayBuffer(n)}this.offset=r}));return{value:await t,done:!1}}}const St=t=>({method:t.method,headers:t.headers,body:t.body,mode:t.mode,credentials:t.credentials,cache:t.cache,redirect:t.redirect,referrer:t.referrer,integrity:t.integrity});function Rt(t){return"string"==typeof t&&"file://"===t.slice(0,7)}class At extends yt{constructor(t,e={}){super(),this.input=t,this.options=e}async iterator(){if(Rt(this.input)&&e.env().get("IS_NODE")){const t=require("fs");this.input=t.readFileSync(this.input.slice(7))}return new Et(this.input,this.options)}}class Tt extends yt{constructor(t,e={}){super(),this.url=t,this.fileOptions=e}async iterator(){return Rt(this.url)?new At(this.url,this.fileOptions).iterator():async function(t,r={},s){let n,i;"string"==typeof t?n=t:(n=t.url,i=St(t));const a=await(s||e.util.fetch)(n,i);if(a.ok){const t=new Uint8Array(await a.arrayBuffer());return new Et(t,r)}throw new Error(a.statusText)}(this.url,this.fileOptions)}}t.CSVDataset=mt,t.Dataset=nt,t.FileDataSource=At,t.TextLineDataset=ot,t.URLDataSource=Tt,t.array=function(t){return it((async()=>$(t)),t.length)},t.csv=function(t,e={}){return new mt(new Tt(t),e)},t.func=function(t){const e=L(t);return it((async()=>e))},t.generator=function(t){return it((async()=>{const e=await t();return L((()=>e.next()))}))},t.microphone=async function(t){return pt.create(t)},t.version_data="4.22.0",t.webcam=async function(t,e){return wt.create(t,e)},t.zip=function(t){if(!O(t))throw new Error("The argument to zip() must be an object or array.");let e;if(Array.isArray(t))for(let r=0;r<t.length;r++)e=null==e?t[r].size:Math.min(e,t[r].size);else if(t instanceof Object)for(const r in t)e=null==e?t[r].size:Math.min(e,t[r].size);return it((async()=>function(t,e=A.FAIL){return new et(t,e)}(await I(t,(t=>{if(t instanceof nt)return{value:t.iterator(),recurse:!1};if(O(t))return{value:null,recurse:!0};throw new Error("Leaves of the structure passed to zip() must be Datasets, not primitives.")})),A.SHORTEST)),e)}}));
//# sourceMappingURL=tf-data.es2017.min.js.map
