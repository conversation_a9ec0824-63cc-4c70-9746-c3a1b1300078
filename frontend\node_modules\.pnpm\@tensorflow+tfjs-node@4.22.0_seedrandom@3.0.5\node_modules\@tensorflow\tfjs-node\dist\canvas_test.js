"use strict";
/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var tf = require("@tensorflow/tfjs");
var MockContext = /** @class */ (function () {
    function MockContext() {
    }
    MockContext.prototype.getImageData = function (x, y, width, height) {
        var data = new Uint8ClampedArray(width * height * 4);
        for (var i = 0; i < data.length; ++i) {
            data[i] = i + 1;
        }
        return { data: data };
    };
    return MockContext;
}());
var MockCanvas = /** @class */ (function () {
    function MockCanvas(width, height) {
        this.width = width;
        this.height = height;
    }
    MockCanvas.prototype.getContext = function (type) {
        return new MockContext();
    };
    return MockCanvas;
}());
describe('tf.browser.fromPixels with polyfills', function () {
    it('accepts a canvas-like element', function () { return __awaiter(void 0, void 0, void 0, function () {
        var c, t, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    c = new MockCanvas(2, 2);
                    t = tf.browser.fromPixels(c);
                    expect(t.dtype).toBe('int32');
                    expect(t.shape).toEqual([2, 2, 3]);
                    _b = (_a = tf.test_util).expectArraysEqual;
                    return [4 /*yield*/, t.data()];
                case 1:
                    _b.apply(_a, [_c.sent(), [1, 2, 3, 5, 6, 7, 9, 10, 11, 13, 14, 15]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('accepts a canvas-like element, numChannels=4', function () { return __awaiter(void 0, void 0, void 0, function () {
        var c, t, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    c = new MockCanvas(2, 2);
                    t = tf.browser.fromPixels(c, 4);
                    expect(t.dtype).toBe('int32');
                    expect(t.shape).toEqual([2, 2, 4]);
                    _b = (_a = tf.test_util).expectArraysEqual;
                    return [4 /*yield*/, t.data()];
                case 1:
                    _b.apply(_a, [_c.sent(), [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('errors when passed a non-canvas object', function () {
        var c = 5;
        // tslint:disable-next-line:no-any
        expect(function () { return tf.browser.fromPixels(c); })
            .toThrowError(/pixels passed to tf\.browser\.fromPixels\(\) must be either an/);
    });
});
