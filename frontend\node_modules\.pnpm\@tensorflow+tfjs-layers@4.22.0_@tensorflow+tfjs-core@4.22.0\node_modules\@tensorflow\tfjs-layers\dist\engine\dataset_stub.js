/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/**
 * Stub interfaces and classes for testing tf.LayersModel.fitDataset().
 *
 * TODO(cais, soergel): Remove this in favor of actual interfaces and classes
 *   when ready.
 */
export class LazyIterator {
}
export class Dataset {
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZGF0YXNldF9zdHViLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vdGZqcy1sYXllcnMvc3JjL2VuZ2luZS9kYXRhc2V0X3N0dWIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7Ozs7O0dBUUc7QUFFSDs7Ozs7R0FLRztBQUVILE1BQU0sT0FBZ0IsWUFBWTtDQUVqQztBQUVELE1BQU0sT0FBZ0IsT0FBTztDQUc1QiIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDE4IEdvb2dsZSBMTENcbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGVcbiAqIGxpY2Vuc2UgdGhhdCBjYW4gYmUgZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBvciBhdFxuICogaHR0cHM6Ly9vcGVuc291cmNlLm9yZy9saWNlbnNlcy9NSVQuXG4gKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICovXG5cbi8qKlxuICogU3R1YiBpbnRlcmZhY2VzIGFuZCBjbGFzc2VzIGZvciB0ZXN0aW5nIHRmLkxheWVyc01vZGVsLmZpdERhdGFzZXQoKS5cbiAqXG4gKiBUT0RPKGNhaXMsIHNvZXJnZWwpOiBSZW1vdmUgdGhpcyBpbiBmYXZvciBvZiBhY3R1YWwgaW50ZXJmYWNlcyBhbmQgY2xhc3Nlc1xuICogICB3aGVuIHJlYWR5LlxuICovXG5cbmV4cG9ydCBhYnN0cmFjdCBjbGFzcyBMYXp5SXRlcmF0b3I8VD4ge1xuICBhYnN0cmFjdCBuZXh0KCk6IFByb21pc2U8SXRlcmF0b3JSZXN1bHQ8VD4+O1xufVxuXG5leHBvcnQgYWJzdHJhY3QgY2xhc3MgRGF0YXNldDxUPiB7XG4gIGFic3RyYWN0IGl0ZXJhdG9yKCk6IFByb21pc2U8TGF6eUl0ZXJhdG9yPFQ+PjtcbiAgc2l6ZTogbnVtYmVyO1xufVxuIl19