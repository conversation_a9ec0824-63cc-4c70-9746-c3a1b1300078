/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid.
/**
 * A string array of valid ConvolutionalDepthwiseLayer class names.
 *
 * This is guaranteed to match the `ConvolutionalDepthwiseLayerClassName` union
 * type.
 */
export const convolutionalDepthwiseLayerClassNames = [
    'DepthwiseConv2D',
];
//# sourceMappingURL=data:application/json;base64,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