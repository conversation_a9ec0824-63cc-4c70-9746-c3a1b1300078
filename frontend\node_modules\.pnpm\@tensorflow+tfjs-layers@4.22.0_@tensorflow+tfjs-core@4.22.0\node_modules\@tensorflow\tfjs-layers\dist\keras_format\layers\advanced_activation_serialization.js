/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid.
/**
 * A string array of valid AdvancedActivationLayer class names.
 *
 * This is guaranteed to match the `AdvancedActivationLayerClassName` union
 * type.
 */
export const advancedActivationLayerClassNames = [
    'ReLU',
    'LeakyReLU',
    'PReLU',
    'ELU',
    'ThresholdedReLU',
    'Softmax',
];
//# sourceMappingURL=data:application/json;base64,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