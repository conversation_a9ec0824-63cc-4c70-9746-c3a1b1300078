"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.depthToSpaceConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.depthToSpaceConfig = {
    kernelName: tfjs_1.DepthToSpace,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var x = args.inputs.x;
        var backend = args.backend;
        var _a = args.attrs, blockSize = _a.blockSize, dataFormat = _a.dataFormat;
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', x), {
                name: 'block_size',
                type: backend.binding.TF_ATTR_INT,
                value: blockSize < 2 ? 2 : blockSize
            },
            {
                name: 'data_format',
                type: backend.binding.TF_ATTR_STRING,
                value: dataFormat
            }
        ];
        var inputs = [x];
        return backend.executeSingleOutput(tfjs_1.DepthToSpace, opAttrs, inputs);
    }
};
