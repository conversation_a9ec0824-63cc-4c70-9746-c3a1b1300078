"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.squaredDifferenceConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.squaredDifferenceConfig = {
    kernelName: tfjs_1.SquaredDifference,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var _a = args.inputs, a = _a.a, b = _a.b;
        var backend = args.backend;
        var opAttrs = [(0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', a.dtype)];
        return backend.executeSingleOutput(tfjs_1.SquaredDifference, opAttrs, [a, b]);
    }
};
