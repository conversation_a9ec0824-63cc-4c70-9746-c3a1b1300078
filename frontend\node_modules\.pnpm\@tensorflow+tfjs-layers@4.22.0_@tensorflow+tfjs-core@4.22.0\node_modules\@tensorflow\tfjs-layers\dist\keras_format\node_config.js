/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
export {};
//# sourceMappingURL=data:application/json;base64,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