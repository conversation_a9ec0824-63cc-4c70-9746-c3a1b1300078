/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid.
/**
 * A string array of valid InputLayer class names.
 *
 * This is guaranteed to match the `InputLayerClassName` union type.
 */
export const inputLayerClassNames = [
    'InputLayer',
];
//# sourceMappingURL=data:application/json;base64,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