"use strict";
/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.tensorScatterUpdateConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.tensorScatterUpdateConfig = {
    kernelName: tfjs_1.TensorScatterUpdate,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var _a = args.inputs, tensor = _a.tensor, indices = _a.indices, updates = _a.updates;
        var backend = args.backend;
        var _b = args.attrs;
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', updates.dtype),
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('Tindices', 'int32')
        ];
        var ret = backend.executeSingleOutput(tfjs_1.TensorScatterUpdate, opAttrs, [tensor, indices, updates]);
        return ret;
    }
};
