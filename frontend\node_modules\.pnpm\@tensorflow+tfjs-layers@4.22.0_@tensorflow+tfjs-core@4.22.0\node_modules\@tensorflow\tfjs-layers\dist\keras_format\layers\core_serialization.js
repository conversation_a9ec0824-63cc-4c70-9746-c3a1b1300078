/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid.
/**
 * A string array of valid CoreLayer class names.
 *
 * This is guaranteed to match the `CoreLayerClassName` union type.
 */
export const coreLayerClassNames = [
    'Activation',
    'Dense',
    'Dropout',
    'Flatten',
    'Permute',
    'RepeatVector',
    'Reshape',
];
//# sourceMappingURL=data:application/json;base64,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