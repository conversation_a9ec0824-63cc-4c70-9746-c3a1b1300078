"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.mirrorPadConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.mirrorPadConfig = {
    kernelName: tfjs_1.MirrorPad,
    backendName: 'tensorflow',
    kernelFunc: function (_a) {
        var inputs = _a.inputs, backend = _a.backend, attrs = _a.attrs;
        var x = inputs.x;
        var _b = attrs, paddings = _b.paddings, mode = _b.mode;
        var nodeBackend = backend;
        var paddingsTensor = (0, tfjs_1.tensor2d)(paddings, [paddings.length, 2], 'int32');
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', x.dtype),
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('Tpaddings', paddingsTensor.dtype), {
                name: 'mode',
                type: nodeBackend.binding.TF_ATTR_STRING,
                value: mode.toUpperCase()
            }
        ];
        var output = nodeBackend.executeSingleOutput('MirrorPad', opAttrs, [x, paddingsTensor]);
        paddingsTensor.dispose();
        return output;
    }
};
