/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid.
/**
 * A string array of valid PoolingLayer class names.
 *
 * This is guaranteed to match the `PoolingLayerClassName` union type.
 */
export const poolingLayerClassNames = [
    'AveragePooling1D',
    'AveragePooling2D',
    'GlobalAveragePooling1D',
    'GlobalAveragePooling2D',
    'GlobalMaxPooling1D',
    'GlobalMaxPooling2D',
    'MaxPooling1D',
    'MaxPooling2D',
];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicG9vbGluZ19zZXJpYWxpemF0aW9uLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vdGZqcy1sYXllcnMvc3JjL2tlcmFzX2Zvcm1hdC9sYXllcnMvcG9vbGluZ19zZXJpYWxpemF0aW9uLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7OztHQVFHO0FBd0RILDRFQUE0RTtBQUM1RSw4RUFBOEU7QUFFOUU7Ozs7R0FJRztBQUNILE1BQU0sQ0FBQyxNQUFNLHNCQUFzQixHQUE0QjtJQUM3RCxrQkFBa0I7SUFDbEIsa0JBQWtCO0lBQ2xCLHdCQUF3QjtJQUN4Qix3QkFBd0I7SUFDeEIsb0JBQW9CO0lBQ3BCLG9CQUFvQjtJQUNwQixjQUFjO0lBQ2QsY0FBYztDQUNmLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAxOCBHb29nbGUgTExDXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlXG4gKiBsaWNlbnNlIHRoYXQgY2FuIGJlIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgb3IgYXRcbiAqIGh0dHBzOi8vb3BlbnNvdXJjZS5vcmcvbGljZW5zZXMvTUlULlxuICogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAqL1xuXG5pbXBvcnQge0RhdGFGb3JtYXRTZXJpYWxpemF0aW9uLCBQYWRkaW5nTW9kZX0gZnJvbSAnLi4vY29tbW9uJztcbmltcG9ydCB7QmFzZUxheWVyU2VyaWFsaXphdGlvbiwgTGF5ZXJDb25maWd9IGZyb20gJy4uL3RvcG9sb2d5X2NvbmZpZyc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgUG9vbGluZzFETGF5ZXJDb25maWcgZXh0ZW5kcyBMYXllckNvbmZpZyB7XG4gIHBvb2xfc2l6ZT86IFtudW1iZXJdO1xuICBzdHJpZGVzPzogW251bWJlcl07XG4gIHBhZGRpbmc/OiBQYWRkaW5nTW9kZTtcbn1cblxuZXhwb3J0IHR5cGUgTWF4UG9vbGluZzFETGF5ZXJTZXJpYWxpemF0aW9uID1cbiAgICBCYXNlTGF5ZXJTZXJpYWxpemF0aW9uPCdNYXhQb29saW5nMUQnLCBQb29saW5nMURMYXllckNvbmZpZz47XG5cbmV4cG9ydCB0eXBlIEF2ZXJhZ2VQb29saW5nMURMYXllclNlcmlhbGl6YXRpb24gPVxuICAgIEJhc2VMYXllclNlcmlhbGl6YXRpb248J0F2ZXJhZ2VQb29saW5nMUQnLCBQb29saW5nMURMYXllckNvbmZpZz47XG5cbmV4cG9ydCBpbnRlcmZhY2UgUG9vbGluZzJETGF5ZXJDb25maWcgZXh0ZW5kcyBMYXllckNvbmZpZyB7XG4gIHBvb2xfc2l6ZT86IG51bWJlcnxbbnVtYmVyLCBudW1iZXJdO1xuICBzdHJpZGVzPzogbnVtYmVyfFtudW1iZXIsIG51bWJlcl07XG4gIHBhZGRpbmc/OiBQYWRkaW5nTW9kZTtcbiAgZGF0YV9mb3JtYXQ/OiBEYXRhRm9ybWF0U2VyaWFsaXphdGlvbjtcbn1cblxuZXhwb3J0IHR5cGUgTWF4UG9vbGluZzJETGF5ZXJTZXJpYWxpemF0aW9uID1cbiAgICBCYXNlTGF5ZXJTZXJpYWxpemF0aW9uPCdNYXhQb29saW5nMkQnLCBQb29saW5nMkRMYXllckNvbmZpZz47XG5cbmV4cG9ydCB0eXBlIEF2ZXJhZ2VQb29saW5nMkRMYXllclNlcmlhbGl6YXRpb24gPVxuICAgIEJhc2VMYXllclNlcmlhbGl6YXRpb248J0F2ZXJhZ2VQb29saW5nMkQnLCBQb29saW5nMkRMYXllckNvbmZpZz47XG5cbmV4cG9ydCB0eXBlIEdsb2JhbEF2ZXJhZ2VQb29saW5nMURMYXllclNlcmlhbGl6YXRpb24gPVxuICAgIEJhc2VMYXllclNlcmlhbGl6YXRpb248J0dsb2JhbEF2ZXJhZ2VQb29saW5nMUQnLCBMYXllckNvbmZpZz47XG5cbmV4cG9ydCB0eXBlIEdsb2JhbE1heFBvb2xpbmcxRExheWVyU2VyaWFsaXphdGlvbiA9XG4gICAgQmFzZUxheWVyU2VyaWFsaXphdGlvbjwnR2xvYmFsTWF4UG9vbGluZzFEJywgTGF5ZXJDb25maWc+O1xuXG5leHBvcnQgaW50ZXJmYWNlIEdsb2JhbFBvb2xpbmcyRExheWVyQ29uZmlnIGV4dGVuZHMgTGF5ZXJDb25maWcge1xuICBkYXRhX2Zvcm1hdD86IERhdGFGb3JtYXRTZXJpYWxpemF0aW9uO1xufVxuXG5leHBvcnQgdHlwZSBHbG9iYWxBdmVyYWdlUG9vbGluZzJETGF5ZXJTZXJpYWxpemF0aW9uID0gQmFzZUxheWVyU2VyaWFsaXphdGlvbjxcbiAgICAnR2xvYmFsQXZlcmFnZVBvb2xpbmcyRCcsIEdsb2JhbFBvb2xpbmcyRExheWVyQ29uZmlnPjtcblxuZXhwb3J0IHR5cGUgR2xvYmFsTWF4UG9vbGluZzJETGF5ZXJTZXJpYWxpemF0aW9uID1cbiAgICBCYXNlTGF5ZXJTZXJpYWxpemF0aW9uPCdHbG9iYWxNYXhQb29saW5nMkQnLCBHbG9iYWxQb29saW5nMkRMYXllckNvbmZpZz47XG5cbi8vIFVwZGF0ZSBwb29saW5nTGF5ZXJDbGFzc05hbWVzIGJlbG93IGluIGNvbmNlcnQgd2l0aCB0aGlzLlxuZXhwb3J0IHR5cGUgUG9vbGluZ0xheWVyU2VyaWFsaXphdGlvbiA9IE1heFBvb2xpbmcxRExheWVyU2VyaWFsaXphdGlvbnxcbiAgICBBdmVyYWdlUG9vbGluZzFETGF5ZXJTZXJpYWxpemF0aW9ufE1heFBvb2xpbmcyRExheWVyU2VyaWFsaXphdGlvbnxcbiAgICBBdmVyYWdlUG9vbGluZzJETGF5ZXJTZXJpYWxpemF0aW9ufEdsb2JhbEF2ZXJhZ2VQb29saW5nMURMYXllclNlcmlhbGl6YXRpb258XG4gICAgR2xvYmFsTWF4UG9vbGluZzFETGF5ZXJTZXJpYWxpemF0aW9ufFxuICAgIEdsb2JhbEF2ZXJhZ2VQb29saW5nMkRMYXllclNlcmlhbGl6YXRpb258XG4gICAgR2xvYmFsTWF4UG9vbGluZzJETGF5ZXJTZXJpYWxpemF0aW9uO1xuXG5leHBvcnQgdHlwZSBQb29saW5nTGF5ZXJDbGFzc05hbWUgPSBQb29saW5nTGF5ZXJTZXJpYWxpemF0aW9uWydjbGFzc19uYW1lJ107XG5cbi8vIFdlIGNhbid0IGVhc2lseSBleHRyYWN0IGEgc3RyaW5nW10gZnJvbSB0aGUgc3RyaW5nIHVuaW9uIHR5cGUsIGJ1dCB3ZSBjYW5cbi8vIHJlY2FwaXR1bGF0ZSB0aGUgbGlzdCwgZW5mb3JjaW5nIGF0IGNvbXBpbGUgdGltZSB0aGF0IHRoZSB2YWx1ZXMgYXJlIHZhbGlkLlxuXG4vKipcbiAqIEEgc3RyaW5nIGFycmF5IG9mIHZhbGlkIFBvb2xpbmdMYXllciBjbGFzcyBuYW1lcy5cbiAqXG4gKiBUaGlzIGlzIGd1YXJhbnRlZWQgdG8gbWF0Y2ggdGhlIGBQb29saW5nTGF5ZXJDbGFzc05hbWVgIHVuaW9uIHR5cGUuXG4gKi9cbmV4cG9ydCBjb25zdCBwb29saW5nTGF5ZXJDbGFzc05hbWVzOiBQb29saW5nTGF5ZXJDbGFzc05hbWVbXSA9IFtcbiAgJ0F2ZXJhZ2VQb29saW5nMUQnLFxuICAnQXZlcmFnZVBvb2xpbmcyRCcsXG4gICdHbG9iYWxBdmVyYWdlUG9vbGluZzFEJyxcbiAgJ0dsb2JhbEF2ZXJhZ2VQb29saW5nMkQnLFxuICAnR2xvYmFsTWF4UG9vbGluZzFEJyxcbiAgJ0dsb2JhbE1heFBvb2xpbmcyRCcsXG4gICdNYXhQb29saW5nMUQnLFxuICAnTWF4UG9vbGluZzJEJyxcbl07XG4iXX0=