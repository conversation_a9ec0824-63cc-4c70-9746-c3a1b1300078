/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
import { constraintClassNames } from './constraint_config';
import { initializerClassNames } from './initializer_config';
import { layerClassNames } from './layers/layer_serialization';
import { optimizerClassNames } from './optimizer_config';
import { regularizerClassNames } from './regularizer_config';
export const kerasClassNames = [
    ...layerClassNames, ...constraintClassNames, ...initializerClassNames,
    ...regularizerClassNames, ...optimizerClassNames
];
//# sourceMappingURL=data:application/json;base64,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