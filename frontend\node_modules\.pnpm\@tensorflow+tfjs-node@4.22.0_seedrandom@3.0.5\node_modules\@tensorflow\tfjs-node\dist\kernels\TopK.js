"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.topKConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var util_1 = require("util");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.topKConfig = {
    kernelName: tfjs_1.TopK,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var x = args.inputs.x;
        var backend = args.backend;
        var _a = args.attrs, k = _a.k, sorted = _a.sorted;
        var kCount = (0, util_1.isNullOrUndefined)(k) ? 1 : k;
        var isSorted = (0, util_1.isNullOrUndefined)(sorted) ? true : sorted;
        var opAttrs = [
            { name: 'sorted', type: backend.binding.TF_ATTR_BOOL, value: isSorted },
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', x.dtype),
        ];
        var kTensor = (0, tfjs_1.scalar)(kCount, 'int32');
        // 'TopKV2' has two-hard coded output attributes:
        // TODO(yassogba) consider renamine constant in kernel names;
        var res = backend.executeMultipleOutputs('TopKV2', opAttrs, [x, kTensor], 2);
        kTensor.dispose();
        return res;
    }
};
