"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.splitVConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.splitVConfig = {
    kernelName: tfjs_1.SplitV,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var x = args.inputs.x;
        var backend = args.backend;
        var _a = args.attrs, numOrSizeSplits = _a.numOrSizeSplits, axis = _a.axis;
        var $axis = tfjs_1.util.parseAxisParam(axis, x.shape)[0];
        var splitSizes = tfjs_1.backend_util.prepareSplitSize(x, numOrSizeSplits, $axis);
        var opAttrs = [
            {
                name: 'num_split',
                type: backend.binding.TF_ATTR_INT,
                value: splitSizes.length
            },
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', x), {
                name: 'Tlen',
                type: backend.binding.TF_ATTR_TYPE,
                value: backend.binding.TF_INT32
            }
        ];
        var inputs = [x];
        return (0, tfjs_1.tidy)(function () {
            inputs.push((0, tfjs_1.tensor1d)(splitSizes, 'int32'));
            inputs.push((0, tfjs_1.scalar)($axis, 'int32'));
            return backend.executeMultipleOutputs(tfjs_1.SplitV, opAttrs, inputs, splitSizes.length);
        });
    }
};
