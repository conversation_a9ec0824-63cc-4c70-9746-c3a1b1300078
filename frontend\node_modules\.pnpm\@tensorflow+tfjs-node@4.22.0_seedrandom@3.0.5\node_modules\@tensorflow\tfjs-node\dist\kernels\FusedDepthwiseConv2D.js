"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.fusedDepthwiseConv2DConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var DepthwiseConv2dNative_1 = require("./DepthwiseConv2dNative");
exports.fusedDepthwiseConv2DConfig = {
    kernelName: tfjs_1.FusedDepthwiseConv2D,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var _a = args.inputs, x = _a.x, filter = _a.filter, bias = _a.bias, preluActivationWeights = _a.preluActivationWeights;
        var backend = args.backend;
        var _b = args.attrs, strides = _b.strides, pad = _b.pad, dilations = _b.dilations, dimRoundingMode = _b.dimRoundingMode, activation = _b.activation, leakyreluAlpha = _b.leakyreluAlpha;
        var $dilations = dilations;
        if ($dilations == null) {
            $dilations = [1, 1];
        }
        var convInfo = tfjs_1.backend_util.computeConv2DInfo(x.shape, filter.shape, strides, $dilations, pad, dimRoundingMode, true /* depthwise */);
        var result = (0, DepthwiseConv2dNative_1.depthwiseConv2dNativeImpl)(x, filter, convInfo, backend);
        var toDispose = [];
        if (bias != null) {
            toDispose.push(result);
            result = (0, tfjs_1.add)(result, bias);
        }
        var temp = result;
        result = backend.applyActivation(result, activation, preluActivationWeights, leakyreluAlpha);
        if (temp !== result) {
            toDispose.push(temp);
        }
        toDispose.forEach(function (t) { return t.dispose(); });
        return result;
    }
};
