/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/**
 * Padding Layers.
 */
// Porting Note: In Python Keras, the padding layers are in convolutional.py,
//   but we decided to put them in a separate file (padding.ts) for clarity.
import * as tfc from '@tensorflow/tfjs-core';
import { serialization, tidy } from '@tensorflow/tfjs-core';
import { imageDataFormat } from '../backend/common';
import { InputSpec, Layer } from '../engine/topology';
import { ValueError } from '../errors';
import { getExactlyOneShape, getExactlyOneTensor } from '../utils/types_utils';
/**
 * Pads the middle dimension of a 3D tensor.
 *
 * @param x Input `tf.Tensor` to be padded.
 * @param padding `Array` of 2 integers, how many zeros to add at the start and
 *   end of the middle dimension (i.e., dimension 1).
 * @return A padded 3D `tf.Tensor`.
 */
export function temporalPadding(x, padding) {
    return tidy(() => {
        if (x.rank !== 3) {
            throw new ValueError(`temporalPadding expects input tensor to be 3-D, but received a ` +
                `${x.rank}-D tensor.`);
        }
        if (padding == null) {
            padding = [1, 1];
        }
        if (padding.length !== 2) {
            throw new ValueError(`temporalPadding expects input padding pattern to be a length-2 ` +
                `array, but received a length-${padding.length} array.`);
        }
        const pattern = [[0, 0], padding, [0, 0]];
        return tfc.pad(x, pattern);
    });
}
/**
 * Pads the 2nd and 3rd dimensions of a 4D tensor.
 *
 * @param x Input `tf.Tensor` to be padded.
 * @param padding `Array` of two `Array`s, each of which is an `Array` of two
 *   integers. The amount of padding at the beginning and end of the 2nd and 3rd
 *   dimensions, respectively.
 * @param dataFormat 'channelsLast' (default) or 'channelsFirst'.
 * @return Padded 4D `tf.Tensor`.
 */
export function spatial2dPadding(x, padding, dataFormat) {
    return tidy(() => {
        if (x.rank !== 4) {
            throw new ValueError(`temporalPadding expects input tensor to be 4-D, but received a ` +
                `${x.rank}-D tensor.`);
        }
        if (padding == null) {
            padding = [[1, 1], [1, 1]];
        }
        if (padding.length !== 2 || padding[0].length !== 2 ||
            padding[1].length !== 2) {
            throw new ValueError('spatial2dPadding expects `padding` to be an Array of two Arrays, ' +
                'each of which is an Array of two integers.');
        }
        if (dataFormat == null) {
            dataFormat = imageDataFormat();
        }
        if (dataFormat !== 'channelsLast' && dataFormat !== 'channelsFirst') {
            throw new ValueError(`Unknown data format: ${dataFormat}. ` +
                `Supported data formats are 'channelsLast' and 'channelsFirst.`);
        }
        let pattern;
        if (dataFormat === 'channelsFirst') {
            pattern = [[0, 0], [0, 0], padding[0], padding[1]];
        }
        else {
            pattern = [[0, 0], padding[0], padding[1], [0, 0]];
        }
        return tfc.pad(x, pattern);
    });
}
class ZeroPadding2D extends Layer {
    constructor(args) {
        if (args == null) {
            args = {};
        }
        super(args);
        this.dataFormat =
            args.dataFormat == null ? imageDataFormat() : args.dataFormat;
        // TODO(cais): Maybe refactor the following logic surrounding `padding`
        //   into a helper method.
        if (args.padding == null) {
            this.padding = [[1, 1], [1, 1]];
        }
        else if (typeof args.padding === 'number') {
            this.padding =
                [[args.padding, args.padding], [args.padding, args.padding]];
        }
        else {
            args.padding = args.padding;
            if (args.padding.length !== 2) {
                throw new ValueError(`ZeroPadding2D expects padding to be a length-2 array, but ` +
                    `received a length-${args.padding.length} array.`);
            }
            let heightPadding;
            let widthPadding;
            if (typeof args.padding[0] === 'number') {
                heightPadding = [args.padding[0], args.padding[0]];
                widthPadding = [args.padding[1], args.padding[1]];
            }
            else {
                args.padding = args.padding;
                if (args.padding[0].length !== 2) {
                    throw new ValueError(`ZeroPadding2D expects height padding to be a length-2 array, ` +
                        `but received a length-${args.padding[0].length} array.`);
                }
                heightPadding = args.padding[0];
                if (args.padding[1].length !== 2) {
                    throw new ValueError(`ZeroPadding2D expects width padding to be a length-2 array, ` +
                        `but received a length-${args.padding[1].length} array.`);
                }
                widthPadding = args.padding[1];
            }
            this.padding = [heightPadding, widthPadding];
        }
        this.inputSpec = [new InputSpec({ ndim: 4 })];
    }
    computeOutputShape(inputShape) {
        inputShape = getExactlyOneShape(inputShape);
        let rows;
        let cols;
        if (this.dataFormat === 'channelsFirst') {
            if (inputShape[2] != null && inputShape[2] >= 0) {
                rows = inputShape[2] + this.padding[0][0] + this.padding[0][1];
            }
            else {
                rows = null;
            }
            if (inputShape[3] != null && inputShape[3] >= 0) {
                cols = inputShape[3] + this.padding[1][0] + this.padding[1][1];
            }
            else {
                cols = null;
            }
            return [inputShape[0], inputShape[1], rows, cols];
        }
        else {
            if (inputShape[1] != null && inputShape[1] >= 0) {
                rows = inputShape[1] + this.padding[0][0] + this.padding[0][1];
            }
            else {
                rows = null;
            }
            if (inputShape[2] != null && inputShape[2] >= 0) {
                cols = inputShape[2] + this.padding[1][0] + this.padding[1][1];
            }
            else {
                cols = null;
            }
            return [inputShape[0], rows, cols, inputShape[3]];
        }
    }
    call(inputs, kwargs) {
        return tidy(() => spatial2dPadding(getExactlyOneTensor(inputs), this.padding, this.dataFormat));
    }
    getConfig() {
        const config = {
            padding: this.padding,
            dataFormat: this.dataFormat,
        };
        const baseConfig = super.getConfig();
        Object.assign(config, baseConfig);
        return config;
    }
}
/** @nocollapse */
ZeroPadding2D.className = 'ZeroPadding2D';
export { ZeroPadding2D };
serialization.registerClass(ZeroPadding2D);
//# sourceMappingURL=data:application/json;base64,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