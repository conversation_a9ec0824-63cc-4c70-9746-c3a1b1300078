{"version": 3, "file": "esm_module_provider.js", "sourceRoot": "", "sources": ["../../../tools/custom_module/esm_module_provider.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,uBAAyB;AACzB,2BAA6B;AAE7B,iDAAmF;AACnF,+CAA+C;AAE/C,+BAAwE;AAExE,SAAgB,iBAAiB,CAAC,IAAQ;IACxC,OAAO,IAAI,iBAAiB,EAAE,CAAC;AACjC,CAAC;AAFD,8CAEC;AAED;IAAA;IA8CA,CAAC;IA7CC;;OAEG;IACH,mDAAuB,GAAvB,UAAwB,MAA8B;QAC7C,IAAA,oBAAoB,GAAI,MAAM,qBAAV,CAAW;QAEtC,IAAM,UAAU,GAAG,IAAA,qCAAqB,EAAC,MAAM,EAAE,yBAAiB,CAAC,CAAC;QAEpE,EAAE,CAAC,SAAS,CAAC,oBAAoB,EAAE,EAAC,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,4CAAqC,oBAAoB,CAAE,CAAC,CAAC;QAEzE,IAAM,kBAAkB,GAAG,gBAAgB,CAAC;QAC5C,IAAM,sBAAsB,GAAG,qBAAqB,CAAC;QAErD,uEAAuE;QACvE,EAAE,CAAC,aAAa,CACZ,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,EACvD,UAAU,CAAC,IAAI,CAAC,CAAC;QACrB,EAAE,CAAC,aAAa,CACZ,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;QAE1E,kEAAkE;QAElE,IAAI,WAAW,CAAC;QAChB,IAAI,WAAW,CAAC;QAChB,IAAI;YACF,WAAW;gBACP,OAAO,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC;YAC1E,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;SACjE;QAAC,OAAO,CAAC,EAAE;YACV,IAAA,WAAI,EAAC,mDAA4C,WAAW,CAAE,CAAC,CAAC;SACjE;QAED,IAAM,YAAY,GAAG,IAAA,8BAAe,EAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC1D,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,IAAM,kBAAkB,GACpB,IAAA,2CAA2B,EAAC,YAAY,EAAE,yBAAiB,CAAC,CAAC;YAEjE,IAAM,0BAA0B,GAAG,6BAA6B,CAAC;YAEjE,EAAE,CAAC,aAAa,CACZ,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,0BAA0B,CAAC,EAC3D,kBAAkB,CAAC,CAAC;SACzB;IACH,CAAC;IACH,wBAAC;AAAD,CAAC,AA9CD,IA8CC;AAED;;GAEG;AACH,sBAAsB;AACT,QAAA,iBAAiB,GAAmB;IAC/C,aAAa,YAAC,eAAwB;QACpC,IAAM,WAAW,GAAG;YAClB,iEAAiE;YACjE,wDAAwD;YACxD,kDAAkD;SACnD,CAAC;QAEF,IAAI,CAAC,eAAe,EAAE;YACpB,WAAW,CAAC,IAAI,CACZ,mEAAmE,CAAC,CAAC;SAC1E;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,kBAAkB;QAChB,OAAO,6CAA6C,CAAC;IACvD,CAAC;IAED,gBAAgB,YAAC,OAAyB;QACxC,IAAM,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;QAC3C,OAAO,yBAAkB,UAAU,iBAAc,CAAC;IACpD,CAAC;IAED,eAAe,YAAC,UAAkB,EAAE,OAAyB;QAC3D,IAAM,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAM,cAAc,GAAG,UAAG,UAAU,cAAI,OAAO,CAAE,CAAC;QAClD,IAAM,UAAU,GAAG,UAAG,UAAU,2BAAiB,UAAU,CAAE,CAAC;QAC9D,IAAM,eAAe,GACjB,kBAAW,IAAA,+BAAwB,EAAC,UAAU,CAAC,uBAC3C,cAAc,qBAAW,UAAU,OAAI,CAAC;QAChD,OAAO,EAAC,UAAU,YAAA,EAAE,eAAe,iBAAA,EAAE,cAAc,gBAAA,EAAC,CAAC;IACvD,CAAC;IAED,uBAAuB,YAAC,UAAkB;QACxC,IAAM,YAAY,GAAG,UAAG,IAAA,+BAAwB,EAAC,UAAU,CAAC,eAAY,CAAC;QACzE,IAAM,UAAU,GACZ,+CAAwC,UAAU,UAAO,CAAC;QAC9D,IAAM,eAAe,GAAG,kBAAW,YAAY,qBAAW,UAAU,OAAI,CAAC;QACzE,OAAO,EAAC,UAAU,YAAA,EAAE,eAAe,iBAAA,EAAE,YAAY,cAAA,EAAC,CAAC;IACrD,CAAC;IAED,uBAAuB,YAAC,QAAQ;QAC9B,IAAM,UAAU,GAAG,IAAA,uBAAgB,EAAC,QAAQ,CAAC,CAAC;QAC9C,OAAO,kBAAW,QAAQ,oDACtB,UAAU,OAAI,CAAC;IACrB,CAAC;IAED,kCAAkC,YAAC,SAAS,EAAE,SAAS;QACrD,IAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,KAAuB,UAAS,EAAT,uBAAS,EAAT,uBAAS,EAAT,IAAS,EAAE;YAA7B,IAAM,QAAQ,kBAAA;YACjB,IAAM,UAAU,GAAG,IAAA,uBAAgB,EAAC,QAAQ,CAAC,CAAC;YAC9C,IAAM,OAAO,GAAG,UAAG,QAAQ,cAAI,SAAS,CAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,kBAAW,QAAQ,iBAC3B,OAAO,oDAA0C,SAAS,cAC1D,UAAU,OAAI,CAAC,CAAC;SACrB;QAED,MAAM,CAAC,IAAI,CAAC,uBAAgB,SAAS,SAAM,CAAC,CAAC;QAC7C,KAAuB,UAAS,EAAT,uBAAS,EAAT,uBAAS,EAAT,IAAS,EAAE;YAA7B,IAAM,QAAQ,kBAAA;YACjB,IAAM,OAAO,GAAG,UAAG,QAAQ,cAAI,SAAS,CAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,YAAK,QAAQ,eAAK,OAAO,MAAG,CAAC,CAAC;SAC3C;QACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,kBAAkB,YAAC,UAAkB;QACnC,IAAI;YACF,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,KAAK,CAAC;SACd;IACH,CAAC;CACF,CAAC;AAEF,SAAS,cAAc,CAAC,OAAyB;IAC/C,QAAQ,OAAO,EAAE;QACf,KAAK,KAAK;YACR,OAAO,8BAA8B,CAAC;QACxC,KAAK,OAAO;YACV,OAAO,gCAAgC,CAAC;QAC1C,KAAK,MAAM;YACT,OAAO,+BAA+B,CAAC;QACzC;YACE,MAAM,IAAI,KAAK,CAAC,8BAAuB,OAAO,CAAE,CAAC,CAAC;KACrD;AACH,CAAC"}