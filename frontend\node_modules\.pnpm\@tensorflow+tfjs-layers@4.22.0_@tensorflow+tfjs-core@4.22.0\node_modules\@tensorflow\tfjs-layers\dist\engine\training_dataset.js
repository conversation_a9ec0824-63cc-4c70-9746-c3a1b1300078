/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/**
 * Interfaces and methods for training models using TensorFlow.js datasets.
 */
import * as tfc from '@tensorflow/tfjs-core';
import { scalar } from '@tensorflow/tfjs-core';
import { configureCallbacks, standardizeCallbacks } from '../base_callbacks';
import { NotImplementedError, ValueError } from '../errors';
import { disposeTensorsInLogs } from '../logs';
import { singletonOrArray, toList } from '../utils/generic_utils';
import { standardizeClassWeights, standardizeWeights } from './training_utils';
// Default batch size used during tensor-based validation.
const DEFAULT_VALIDATION_BATCH_SIZE = 32;
/**
 * Standardize the output of a dataset iterator for use by
 * LayersModel.fitDataset().
 *
 * @param model: A `tf.LayersModel` object.
 * @param iteratorOut The output of a dataset iterator. It is required to be
 *   an object of the form `{xs: TensorOrArrayOrMap, ys:
 * TensorOrArrayOrMap}`, where `TensorOrArrayOrMap` is a single `tf.Tensor`,
 * a `tf.Tensor[]`, or a flat map from string names to `tf.Tensor`s.
 * @returns A flat array of `tf.Tensor` objects: the input `tf.Tensor`s
 *   followed by the target `tf.Tensor`s.  When `tf.Tensor`s are provided
 *   as a map, the order in the resulting array is taken from the `inputNames`
 *   and `outputNames` of the model.
 */
function standardizeDataIteratorOutput(
// Type `model` as `any` here to avoid circular dependency w/
// training.ts.
// tslint:disable-next-line:no-any
model, iteratorOut) {
    let xs;
    let ys;
    const iteratorOutObj = iteratorOut;
    xs = iteratorOutObj['xs'];
    ys = iteratorOutObj['ys'];
    tfc.util.assert(xs != null && ys != null, () => 'A Dataset iterator for fitDataset() is expected to generate ' +
        'objects of the form `{xs: xVal, ys: yVal}`, where the two ' +
        'values may be `tf.Tensor`, an array of Tensors, or a map of ' +
        'string to Tensor.  The provided Dataset instead generates ' +
        `${iteratorOut}`);
    const flattenedXs = flattenTensorOrArrayOrMap('input', model.inputNames, xs);
    const flattenedYs = flattenTensorOrArrayOrMap('output', model.outputNames, ys);
    const batchSize = flattenedXs[0].shape[0];
    tfc.util.assert(flattenedXs.length === model.inputs.length, () => `LayersModel has ${model.inputs.length} inputs, but the dataset ` +
        `provides ${flattenedXs.length} inputs.  (Expected input keys: ` +
        `${JSON.stringify(model.inputNames)})`);
    tfc.util.assert(flattenedYs.length === model.outputs.length, () => `LayersModel has ${model.outputs.length} outputs, but the dataset ` +
        `provides ${flattenedYs.length} outputs.  (Expected output keys: ` +
        `${JSON.stringify(model.outputNames)})`);
    for (let xIndex = 0; xIndex < flattenedXs.length; xIndex++) {
        tfc.util.assert(flattenedXs[xIndex].shape[0] === batchSize, () => `Batch size mismatch: input ` +
            `${model.inputNames[xIndex]} has ${flattenedXs[xIndex].shape[0]}; ` +
            `expected  ${batchSize} based on input ${model.inputNames[0]}.`);
    }
    for (let yIndex = 0; yIndex < flattenedYs.length; yIndex++) {
        tfc.util.assert(flattenedYs[yIndex].shape[0] === batchSize, () => `Batch size mismatch: output ` +
            `${model.outputNames[yIndex]} has ${flattenedYs[yIndex].shape[0]}; ` +
            `expected  ${batchSize} based on input ${model.inputNames[0]}.`);
    }
    return { xs: flattenedXs, ys: flattenedYs };
}
function flattenTensorOrArrayOrMap(inputOrOutput, names, values) {
    if (values instanceof tfc.Tensor) {
        return [values];
    }
    else if (Array.isArray(values)) {
        tfc.util.assert(values.length === names.length, () => `Received an array of ${values.length} Tensors, but expected ${names.length} to match the ${inputOrOutput} keys ${names}.`);
        return values;
    }
    else {
        const result = [];
        // Check that all the required keys are available.
        for (const name of names) {
            if (values[name] == null) {
                throw new ValueError(`The feature data generated by the dataset lacks the required ` +
                    `${inputOrOutput} key '${name}'.`);
            }
            result.push(values[name]);
        }
        return result;
    }
}
function standardizeTensorValidationData(data) {
    if (data.length === 3) {
        throw new NotImplementedError('Validation with sample weights is not implemented yet.');
    }
    return { xs: data[0], ys: data[1] };
}
export async function fitDataset(
// Type `model` as `any` here to avoid circular dependency w/
// training.ts.
// tslint:disable-next-line:no-any
model, dataset, args) {
    const hasBatchesPerEpoch = args.batchesPerEpoch != null;
    tfc.util.assert(model.optimizer != null, () => 'You must compile a model before training/testing. Use ' +
        'LayersModel.compile(modelCompileConfig).');
    tfc.util.assert(args != null, () => `For fitDataset(), the 2nd argument (config) is required, ` +
        `but it is not provided in this call.`);
    tfc.util.assert(args.epochs != null && args.epochs > 0 && Number.isInteger(args.epochs), () => `For fitDataset(), config.epochs is expected to be a positive ` +
        `integer, but got ${args.epochs}`);
    tfc.util.assert(!hasBatchesPerEpoch ||
        (args.batchesPerEpoch > 0 && Number.isInteger(args.batchesPerEpoch)), () => `For fitDataset(), config.batchesPerEpoch is expected to be a ` +
        `positive integer if specified, but got ${args.batchesPerEpoch}`);
    tfc.util.assert(
    // tslint:disable-next-line:no-any
    args['validationSplit'] == null, () => '`validationSplit` is not supported by `fitDataset()`. ' +
        'Use validationData instead.');
    if (model.isTraining) {
        throw new Error('Cannot start training because another fit() call is ongoing.');
    }
    model.isTraining = true;
    try {
        const doValidation = args.validationData != null;
        let valXs;
        let valYs;
        if (doValidation) {
            if (isDatasetObject(args.validationData)) {
                tfc.util.assert(args.validationBatches == null ||
                    (args.validationBatches > 0 &&
                        Number.isInteger(args.validationBatches)), () => `For fitDataset() with dataset-based validation, ` +
                    `config.validationBatches is expected not to be provided, ` +
                    `or to be a positive integer, ` +
                    `but got ${args.validationBatches}`);
            }
            else {
                const validationData = standardizeTensorValidationData(args.validationData);
                valXs = validationData.xs;
                valYs = validationData.ys;
            }
        }
        const trainFunction = model.makeTrainFunction();
        const outLabels = model.getDedupedMetricsNames();
        let callbackMetrics;
        if (doValidation) {
            callbackMetrics =
                outLabels.slice().concat(outLabels.map(n => 'val_' + n));
        }
        else {
            callbackMetrics = outLabels.slice();
        }
        const callbacks = standardizeCallbacks(args.callbacks, args.yieldEvery);
        const verbose = args.verbose == null ? 1 : args.verbose;
        const { callbackList, history } = configureCallbacks(callbacks, verbose, args.epochs, null, null, getStepsPerEpoch(dataset, args), null, // Batch size determined by the dataset itself.
        doValidation, callbackMetrics);
        callbackList.setModel(model);
        model.history = history;
        await callbackList.onTrainBegin();
        model.stopTraining_ = false;
        let epoch = args.initialEpoch == null ? 0 : args.initialEpoch;
        let dataIterator = await dataset.iterator();
        while (epoch < args.epochs) {
            const epochLogs = {};
            await callbackList.onEpochBegin(epoch);
            let stepsDone = 0;
            let batchIndex = 0;
            if (!hasBatchesPerEpoch) {
                dataIterator = await dataset.iterator();
            }
            while (hasBatchesPerEpoch ? stepsDone < args.batchesPerEpoch : true) {
                const iteratorOut = await dataIterator.next();
                // If `batchesPerEpoch` is specified, the dataset should not be
                // exhausted until all epoches are done.
                if (hasBatchesPerEpoch && iteratorOut.done) {
                    console.warn('You provided `batchesPerEpoch` as ' +
                        `${args.batchesPerEpoch}, ` +
                        'but your dataset iterator ran out of data after ' +
                        `${stepsDone} batches; ` +
                        'interrupting training. Make sure that your ' +
                        'dataset can generate at least `batchesPerEpoch * epochs` ' +
                        'batches (in this case, ' +
                        `${args.batchesPerEpoch * args.epochs} batches). ` +
                        'You may need to use the repeat() function when building ' +
                        'your dataset.');
                    break;
                }
                if (iteratorOut.value != null) {
                    const { xs, ys } = standardizeDataIteratorOutput(model, iteratorOut.value);
                    const batchLogs = {};
                    batchLogs['batch'] = batchIndex;
                    batchLogs['size'] = xs[0].shape[0];
                    await callbackList.onBatchBegin(batchIndex, batchLogs);
                    const sampleWeights = [];
                    if (args.classWeight != null) {
                        const standardClassWeights = standardizeClassWeights(args.classWeight, model.outputNames);
                        for (let i = 0; i < standardClassWeights.length; ++i) {
                            sampleWeights.push(await standardizeWeights(ys[i], null, standardClassWeights[i]));
                        }
                    }
                    // Train on batch.
                    const ins = xs.concat(ys).concat(sampleWeights);
                    const outs = trainFunction(ins);
                    tfc.dispose(ins);
                    for (let i = 0; i < outLabels.length; ++i) {
                        const label = outLabels[i];
                        const out = outs[i];
                        batchLogs[label] = out;
                        tfc.keep(out);
                    }
                    await callbackList.onBatchEnd(batchIndex, batchLogs);
                    disposeTensorsInLogs(batchLogs);
                    batchIndex++;
                    stepsDone++;
                }
                if (hasBatchesPerEpoch ? stepsDone >= args.batchesPerEpoch :
                    iteratorOut.done) {
                    // Epoch finished. Perform validation.
                    if (doValidation) {
                        let valOuts;
                        if (isDatasetObject(args.validationData)) {
                            valOuts = toList(await model.evaluateDataset(args.validationData, { batches: args.validationBatches }));
                        }
                        else {
                            valOuts = toList(model.evaluate(valXs, valYs, {
                                batchSize: args.validationBatchSize == null ?
                                    DEFAULT_VALIDATION_BATCH_SIZE :
                                    args.validationBatchSize,
                                verbose: 0
                            }));
                        }
                        for (let i = 0; i < model.metricsNames.length; ++i) {
                            epochLogs[`val_${model.metricsNames[i]}`] = valOuts[i];
                        }
                    }
                    // Call `break` to exit one epoch lopp after validation is done. If
                    // config.batchesPerEpoch is specified, an epoch while loop will
                    // stop when `stepsDone >= config.batchesPerEpoch`. When
                    // config.batchesPerEpoch is not provided, the following `break` is
                    // required to exit the while lopp after dataset is exhausted.
                    break;
                }
                if (model.stopTraining_) {
                    break;
                }
            }
            await callbackList.onEpochEnd(epoch, epochLogs);
            epoch++;
            if (model.stopTraining_) {
                break;
            }
        }
        await callbackList.onTrainEnd();
        await model.history.syncData();
        return model.history;
    }
    finally {
        model.isTraining = false;
    }
}
/** Helper function that determines number of steps (batches) per epoch. */
function getStepsPerEpoch(dataset, args) {
    // Attempt to determine # of batches in an epoch.
    let stepsPerEpoch = null;
    if (args.batchesPerEpoch != null) {
        stepsPerEpoch = args.batchesPerEpoch;
    }
    else if (Number.isFinite(dataset.size)) {
        stepsPerEpoch = dataset.size;
    }
    return stepsPerEpoch;
}
// Check if provided object is a Dataset object by checking its .iterator
// element.
function isDatasetObject(dataset) {
    return (typeof dataset.iterator === 'function');
}
// Check if provided object is a LazyIterator object by checking it's .next
// element.
function isLazyIteratorObject(iterator) {
    return (typeof iterator.next === 'function');
}
export async function evaluateDataset(
// Type `model` as `any` here to avoid circular dependency w/
// training.ts.
// tslint:disable-next-line:no-any
model, dataset, args) {
    args = args || {};
    const hasBatches = args.batches != null;
    const f = model.testFunction;
    let outs = [];
    if (args.verbose > 0) {
        throw new NotImplementedError('Verbose mode is not implemented yet.');
    }
    tfc.util.assert(!hasBatches || (args.batches > 0 && Number.isInteger(args.batches)), () => 'Test loop expects `batches` to be a positive integer, but ' +
        `received ${JSON.stringify(args.batches)}`);
    const dataIterator = isLazyIteratorObject(dataset) ?
        dataset :
        await dataset.iterator();
    // Keeps track of number of examples used in this evaluation.
    let numExamples = 0;
    let batch = 0;
    while (hasBatches ? batch < args.batches : true) {
        const iteratorOut = await dataIterator.next();
        outs = tfc.tidy(() => {
            if (iteratorOut.value) {
                // TODO(cais): Once real dataset is available, use
                //   `map(x => standardizeDataIteratorOutput(model, x).map(f)`.
                const { xs, ys } = standardizeDataIteratorOutput(model, iteratorOut.value);
                const xsAndYs = xs.concat(ys);
                const batchOuts = tfc.tidy(() => f(xsAndYs));
                tfc.dispose(xsAndYs);
                if (batch === 0) {
                    for (let i = 0; i < batchOuts.length; ++i) {
                        outs.push(scalar(0));
                    }
                }
                const batchSize = xsAndYs[0].shape[0];
                for (let i = 0; i < batchOuts.length; ++i) {
                    const batchOut = batchOuts[i];
                    const oldScalar = outs[i];
                    outs[i] =
                        tfc.tidy(() => tfc.add(outs[i], tfc.mul(batchSize, batchOut)));
                    if (batch > 0) {
                        tfc.dispose(oldScalar);
                    }
                }
                tfc.dispose(batchOuts);
                numExamples += batchSize;
                ++batch;
            }
            return outs;
        });
        if (iteratorOut.done) {
            if (hasBatches) {
                console.warn('Your dataset iterator ran out of data during evaluateDataset(). ' +
                    'Interrupting evalution. Make sure that your ' +
                    'dataset can generate at least `batches` ' +
                    `batches (in this case, ${args.batches} batches). ` +
                    'You may need to use the repeat() function when building ' +
                    'your dataset.');
            }
            break;
        }
    }
    for (let i = 0; i < outs.length; ++i) {
        const oldScalar = outs[i];
        outs[i] = tfc.div(outs[i], numExamples);
        tfc.dispose(oldScalar);
    }
    return singletonOrArray(outs);
}
//# sourceMappingURL=data:application/json;base64,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