/**
 * @license
 * Copyright 2023 CodeSmith LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-layers/dist/backend/random_seed" />
/**
 * Keeps track of seed and handles pseudorandomness
 * Instance created in BaseRandomLayer class
 * Utilized for random preprocessing layers
 */
export declare class RandomSeed {
    static className: string;
    seed: number | undefined;
    constructor(seed: number | undefined);
    next(): number | undefined;
}
