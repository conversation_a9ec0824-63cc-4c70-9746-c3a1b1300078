/**
 * @license
 * Copyright 2024 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@tensorflow/tfjs-core")):"function"==typeof define&&define.amd?define(["exports","@tensorflow/tfjs-core"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).tf=t.tf||{},t.tf)}(this,(function(t,e){"use strict";function n(t){var e=Object.create(null);return t&&Object.keys(t).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,r.get?r:{enumerable:!0,get:function(){return t[n]}})}})),e.default=t,e}var r=n(e),i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},i(t,e)};function o(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}function u(t,e,n,r){return new(n||(n=Promise))((function(i,o){function u(t){try{a(r.next(t))}catch(t){o(t)}}function s(t){try{a(r.throw(t))}catch(t){o(t)}}function a(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,s)}a((r=r.apply(t,e||[])).next())}))}function s(t,e){var n,r,i,o,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,r=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!(i=u.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){u=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){u.label=o[1];break}if(6===o[0]&&u.label<i[1]){u.label=i[1],i=o;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(o);break}i[2]&&u.ops.pop(),u.trys.pop();continue}o=e.call(t,u)}catch(t){o=[6,t],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}function a(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function c(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,i,o=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=o.next()).done;)u.push(r.value)}catch(t){i={error:t}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return u}function l(t,e,n){if(n||2===arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))}var f="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function h(t){if(t.__esModule)return t;var e=t.default;if("function"==typeof e){var n=function t(){if(this instanceof t){var n=[null];n.push.apply(n,arguments);var r=Function.bind.apply(e,n);return new r}return e.apply(this,arguments)};n.prototype=e.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(t).forEach((function(e){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(n,e,r.get?r:{enumerable:!0,get:function(){return t[e]}})})),n}var p={exports:{}};!function(t){!function(t,e,n){function r(t){var e,n=this,r=(e=4022871197,function(t){t=String(t);for(var n=0;n<t.length;n++){var r=.02519603282416938*(e+=t.charCodeAt(n));r-=e=r>>>0,e=(r*=e)>>>0,e+=4294967296*(r-=e)}return 2.3283064365386963e-10*(e>>>0)});n.next=function(){var t=2091639*n.s0+2.3283064365386963e-10*n.c;return n.s0=n.s1,n.s1=n.s2,n.s2=t-(n.c=0|t)},n.c=1,n.s0=r(" "),n.s1=r(" "),n.s2=r(" "),n.s0-=r(t),n.s0<0&&(n.s0+=1),n.s1-=r(t),n.s1<0&&(n.s1+=1),n.s2-=r(t),n.s2<0&&(n.s2+=1),r=null}function i(t,e){return e.c=t.c,e.s0=t.s0,e.s1=t.s1,e.s2=t.s2,e}function o(t,e){var n=new r(t),o=e&&e.state,u=n.next;return u.int32=function(){return 4294967296*n.next()|0},u.double=function(){return u()+11102230246251565e-32*(2097152*u()|0)},u.quick=u,o&&("object"==typeof o&&i(o,n),u.state=function(){return i(n,{})}),u}e&&e.exports?e.exports=o:n&&n.amd?n((function(){return o})):this.alea=o}(0,t,!1)}(p);var d=p.exports,v={exports:{}};!function(t){!function(t,e,n){function r(t){var e=this,n="";e.x=0,e.y=0,e.z=0,e.w=0,e.next=function(){var t=e.x^e.x<<11;return e.x=e.y,e.y=e.z,e.z=e.w,e.w^=e.w>>>19^t^t>>>8},t===(0|t)?e.x=t:n+=t;for(var r=0;r<n.length+64;r++)e.x^=0|n.charCodeAt(r),e.next()}function i(t,e){return e.x=t.x,e.y=t.y,e.z=t.z,e.w=t.w,e}function o(t,e){var n=new r(t),o=e&&e.state,u=function(){return(n.next()>>>0)/4294967296};return u.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},u.int32=n.next,u.quick=u,o&&("object"==typeof o&&i(o,n),u.state=function(){return i(n,{})}),u}e&&e.exports?e.exports=o:n&&n.amd?n((function(){return o})):this.xor128=o}(0,t,!1)}(v);var m=v.exports,y={exports:{}};!function(t){!function(t,e,n){function r(t){var e=this,n="";e.next=function(){var t=e.x^e.x>>>2;return e.x=e.y,e.y=e.z,e.z=e.w,e.w=e.v,(e.d=e.d+362437|0)+(e.v=e.v^e.v<<4^t^t<<1)|0},e.x=0,e.y=0,e.z=0,e.w=0,e.v=0,t===(0|t)?e.x=t:n+=t;for(var r=0;r<n.length+64;r++)e.x^=0|n.charCodeAt(r),r==n.length&&(e.d=e.x<<10^e.x>>>4),e.next()}function i(t,e){return e.x=t.x,e.y=t.y,e.z=t.z,e.w=t.w,e.v=t.v,e.d=t.d,e}function o(t,e){var n=new r(t),o=e&&e.state,u=function(){return(n.next()>>>0)/4294967296};return u.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},u.int32=n.next,u.quick=u,o&&("object"==typeof o&&i(o,n),u.state=function(){return i(n,{})}),u}e&&e.exports?e.exports=o:n&&n.amd?n((function(){return o})):this.xorwow=o}(0,t,!1)}(y);var w=y.exports,b={exports:{}};!function(t){!function(t,e,n){function r(t){var e=this;e.next=function(){var t,n,r=e.x,i=e.i;return t=r[i],n=(t^=t>>>7)^t<<24,n^=(t=r[i+1&7])^t>>>10,n^=(t=r[i+3&7])^t>>>3,n^=(t=r[i+4&7])^t<<7,t=r[i+7&7],n^=(t^=t<<13)^t<<9,r[i]=n,e.i=i+1&7,n},function(t,e){var n,r=[];if(e===(0|e))r[0]=e;else for(e=""+e,n=0;n<e.length;++n)r[7&n]=r[7&n]<<15^e.charCodeAt(n)+r[n+1&7]<<13;for(;r.length<8;)r.push(0);for(n=0;n<8&&0===r[n];++n);for(8==n?r[7]=-1:r[n],t.x=r,t.i=0,n=256;n>0;--n)t.next()}(e,t)}function i(t,e){return e.x=t.x.slice(),e.i=t.i,e}function o(t,e){null==t&&(t=+new Date);var n=new r(t),o=e&&e.state,u=function(){return(n.next()>>>0)/4294967296};return u.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},u.int32=n.next,u.quick=u,o&&(o.x&&i(o,n),u.state=function(){return i(n,{})}),u}e&&e.exports?e.exports=o:n&&n.amd?n((function(){return o})):this.xorshift7=o}(0,t,!1)}(b);var g=b.exports,x={exports:{}};!function(t){!function(t,e,n){function r(t){var e=this;e.next=function(){var t,n,r=e.w,i=e.X,o=e.i;return e.w=r=r+1640531527|0,n=i[o+34&127],t=i[o=o+1&127],n^=n<<13,t^=t<<17,n^=n>>>15,t^=t>>>12,n=i[o]=n^t,e.i=o,n+(r^r>>>16)|0},function(t,e){var n,r,i,o,u,s=[],a=128;for(e===(0|e)?(r=e,e=null):(e+="\0",r=0,a=Math.max(a,e.length)),i=0,o=-32;o<a;++o)e&&(r^=e.charCodeAt((o+32)%e.length)),0===o&&(u=r),r^=r<<10,r^=r>>>15,r^=r<<4,r^=r>>>13,o>=0&&(u=u+1640531527|0,i=0==(n=s[127&o]^=r+u)?i+1:0);for(i>=128&&(s[127&(e&&e.length||0)]=-1),i=127,o=512;o>0;--o)r=s[i+34&127],n=s[i=i+1&127],r^=r<<13,n^=n<<17,r^=r>>>15,n^=n>>>12,s[i]=r^n;t.w=u,t.X=s,t.i=i}(e,t)}function i(t,e){return e.i=t.i,e.w=t.w,e.X=t.X.slice(),e}function o(t,e){null==t&&(t=+new Date);var n=new r(t),o=e&&e.state,u=function(){return(n.next()>>>0)/4294967296};return u.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},u.int32=n.next,u.quick=u,o&&(o.X&&i(o,n),u.state=function(){return i(n,{})}),u}e&&e.exports?e.exports=o:n&&n.amd?n((function(){return o})):this.xor4096=o}(0,t,!1)}(x);var C=x.exports,z={exports:{}};!function(t){!function(t,e,n){function r(t){var e=this,n="";e.next=function(){var t=e.b,n=e.c,r=e.d,i=e.a;return t=t<<25^t>>>7^n,n=n-r|0,r=r<<24^r>>>8^i,i=i-t|0,e.b=t=t<<20^t>>>12^n,e.c=n=n-r|0,e.d=r<<16^n>>>16^i,e.a=i-t|0},e.a=0,e.b=0,e.c=-1640531527,e.d=1367130551,t===Math.floor(t)?(e.a=t/4294967296|0,e.b=0|t):n+=t;for(var r=0;r<n.length+20;r++)e.b^=0|n.charCodeAt(r),e.next()}function i(t,e){return e.a=t.a,e.b=t.b,e.c=t.c,e.d=t.d,e}function o(t,e){var n=new r(t),o=e&&e.state,u=function(){return(n.next()>>>0)/4294967296};return u.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},u.int32=n.next,u.quick=u,o&&("object"==typeof o&&i(o,n),u.state=function(){return i(n,{})}),u}e&&e.exports?e.exports=o:n&&n.amd?n((function(){return o})):this.tychei=o}(0,t,!1)}(z);var E=z.exports,S={exports:{}},A=h({__proto__:null,default:{}});!function(t){!function(e,n,r){var i,o=256,u=r.pow(o,6),s=r.pow(2,52),a=2*s,c=255;function l(t,c,l){var m=[],y=d(p((c=1==c?{entropy:!0}:c||{}).entropy?[t,v(n)]:null==t?function(){try{var t;return i&&(t=i.randomBytes)?t=t(o):(t=new Uint8Array(o),(e.crypto||e.msCrypto).getRandomValues(t)),v(t)}catch(t){var r=e.navigator,u=r&&r.plugins;return[+new Date,e,u,e.screen,v(n)]}}():t,3),m),w=new f(m),b=function(){for(var t=w.g(6),e=u,n=0;t<s;)t=(t+n)*o,e*=o,n=w.g(1);for(;t>=a;)t/=2,e/=2,n>>>=1;return(t+n)/e};return b.int32=function(){return 0|w.g(4)},b.quick=function(){return w.g(4)/4294967296},b.double=b,d(v(w.S),n),(c.pass||l||function(t,e,n,i){return i&&(i.S&&h(i,w),t.state=function(){return h(w,{})}),n?(r.random=t,e):t})(b,y,"global"in c?c.global:this==r,c.state)}function f(t){var e,n=t.length,r=this,i=0,u=r.i=r.j=0,s=r.S=[];for(n||(t=[n++]);i<o;)s[i]=i++;for(i=0;i<o;i++)s[i]=s[u=c&u+t[i%n]+(e=s[i])],s[u]=e;(r.g=function(t){for(var e,n=0,i=r.i,u=r.j,s=r.S;t--;)e=s[i=c&i+1],n=n*o+s[c&(s[i]=s[u=c&u+e])+(s[u]=e)];return r.i=i,r.j=u,n})(o)}function h(t,e){return e.i=t.i,e.j=t.j,e.S=t.S.slice(),e}function p(t,e){var n,r=[],i=typeof t;if(e&&"object"==i)for(n in t)try{r.push(p(t[n],e-1))}catch(t){}return r.length?r:"string"==i?t:t+"\0"}function d(t,e){for(var n,r=t+"",i=0;i<r.length;)e[c&i]=c&(n^=19*e[c&i])+r.charCodeAt(i++);return v(e)}function v(t){return String.fromCharCode.apply(0,t)}if(d(r.random(),n),t.exports){t.exports=l;try{i=A}catch(t){}}else r.seedrandom=l}("undefined"!=typeof self?self:f,[],Math)}(S);var R=d,T=m,k=w,F=g,N=C,O=E,_=S.exports;_.alea=R,_.xor128=T,_.xorwow=k,_.xorshift7=F,_.xor4096=N,_.tychei=O;var D=_;function I(t,e,n,r){if(void 0===n&&(n=new Map),void 0===r&&(r=new Set),null==t)return null;if("function"==typeof Blob&&t instanceof Blob)return t.slice();if(r.has(t))throw new Error("Circular references are not supported.");if(n.has(t))return n.get(t);var i=e(t);if(i.recurse&&null!==i.value)throw new Error("A deep map function may not return both a value and recurse=true.");if(i.recurse){if(L(t)){var o=Array.isArray(t)?[]:{};for(var u in r.add(t),t){var s=I(t[u],e,n,r);o[u]=s}return r.delete(t),t.__proto__&&(o.__proto__=t.__proto__),o}throw new Error("Can't recurse into non-iterable type: ".concat(t))}return n.set(t,i.value),i.value}function j(t,e){return void 0===e&&(e=B),M(t,e)}function M(t,e,n){void 0===n&&(n=new Set);var r=t[0];if(n.has(r))throw new Error("Circular references are not supported.");var i=e(t);if(i.recurse&&null!==i.value)throw new Error("A deep zip function may not return both a value and recurse=true.");if(i.recurse){if(L(r)){var o=Array.isArray(r)?[]:{};n.add(r);var u=function(r){var i=M(t.map((function(t){return t[r]})),e,n);o[r]=i};for(var s in r)u(s);return n.delete(r),o}throw new Error("Can't recurse into non-iterable type: ".concat(r))}return i.value}function B(t){return null===t?null:L(t[0])?{value:null,recurse:!0}:{value:t,recurse:!1}}function P(t,e){return u(this,void 0,void 0,(function(){var n,i,o,u,c,l,f,h,p;return s(this,(function(s){switch(s.label){case 0:n=new Map,I(t,e,n),s.label=1;case 1:s.trys.push([1,6,7,8]),i=a(Array.from(n.keys())),o=i.next(),s.label=2;case 2:return o.done?[3,5]:(u=o.value,c=n.get(u),r.util.isPromise(c)?[4,c]:[3,4]);case 3:l=s.sent(),n.set(u,l),s.label=4;case 4:return o=i.next(),[3,2];case 5:return[3,8];case 6:return f=s.sent(),h={error:f},[3,8];case 7:try{o&&!o.done&&(p=i.return)&&p.call(i)}finally{if(h)throw h.error}return[7];case 8:return[2,I(t,e,n)]}}))}))}function L(t){var e=!1;r.env().get("IS_BROWSER")?e=t instanceof TextDecoder:e=t instanceof require("string_decoder").StringDecoder;return null!=t&&!ArrayBuffer.isView(t)&&(Array.isArray(t)||"object"==typeof t&&!(t instanceof r.Tensor)&&!(t instanceof Promise)&&!e)}function q(t){return I(t,H)}function H(t){return t instanceof r.Tensor?{value:t.clone(),recurse:!1}:L(t)?{value:null,recurse:!0}:{value:t,recurse:!1}}var W=function(){function t(t){if(this.capacity=t,this.begin=0,this.end=0,null==t)throw new RangeError("Can't create a ring buffer of unknown capacity.");if(t<1)throw new RangeError("Can't create ring buffer of capacity < 1.");this.data=new Array(t),this.doubledCapacity=2*t}return t.prototype.wrap=function(t){for(;t<0;)t+=this.doubledCapacity;return t%this.doubledCapacity},t.prototype.get=function(t){if(t<0)throw new RangeError("Can't get item at a negative index.");return this.data[t%this.capacity]},t.prototype.set=function(t,e){if(t<0)throw new RangeError("Can't set item at a negative index.");this.data[t%this.capacity]=e},t.prototype.length=function(){var t=this.end-this.begin;return t<0&&(t=this.doubledCapacity+t),t},t.prototype.isFull=function(){return this.length()===this.capacity},t.prototype.isEmpty=function(){return 0===this.length()},t.prototype.push=function(t){if(this.isFull())throw new RangeError("Ring buffer is full.");this.set(this.end,t),this.end=this.wrap(this.end+1)},t.prototype.pushAll=function(t){var e,n;try{for(var r=a(t),i=r.next();!i.done;i=r.next()){var o=i.value;this.push(o)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(e)throw e.error}}},t.prototype.pop=function(){if(this.isEmpty())throw new RangeError("Ring buffer is empty.");this.end=this.wrap(this.end-1);var t=this.get(this.end);return this.set(this.end,void 0),t},t.prototype.unshift=function(t){if(this.isFull())throw new RangeError("Ring buffer is full.");this.begin=this.wrap(this.begin-1),this.set(this.begin,t)},t.prototype.shift=function(){if(this.isEmpty())throw new RangeError("Ring buffer is empty.");var t=this.get(this.begin);return this.set(this.begin,void 0),this.begin=this.wrap(this.begin+1),t},t.prototype.shuffleExcise=function(t){if(this.isEmpty())throw new RangeError("Ring buffer is empty.");var e=this.wrap(this.begin+t),n=this.get(e);return this.set(e,this.pop()),n},t}(),V=function(t){function e(){return t.call(this,e.INITIAL_CAPACITY)||this}return o(e,t),e.prototype.isFull=function(){return!1},e.prototype.push=function(e){t.prototype.isFull.call(this)&&this.expand(),t.prototype.push.call(this,e)},e.prototype.unshift=function(e){t.prototype.isFull.call(this)&&this.expand(),t.prototype.unshift.call(this,e)},e.prototype.expand=function(){for(var t=2*this.capacity,e=new Array(t),n=this.length(),r=0;r<n;r++)e[r]=this.get(this.wrap(this.begin+r));this.data=e,this.capacity=t,this.doubledCapacity=2*this.capacity,this.begin=0,this.end=n},e}(W);function U(t){return new Z(t)}function Q(t){return new J(t)}V.INITIAL_CAPACITY=32;var X,G=function(){function t(){}return t.prototype.toArray=function(){return u(this,void 0,void 0,(function(){var t,e;return s(this,(function(n){switch(n.label){case 0:return t=[],[4,this.next()];case 1:e=n.sent(),n.label=2;case 2:return e.done?[3,4]:(t.push(e.value),[4,this.next()]);case 3:return e=n.sent(),[3,2];case 4:return[2,t]}}))}))},t.prototype.toArrayForTest=function(){return u(this,void 0,void 0,(function(){var t,e,n;return s(this,(function(r){switch(r.label){case 0:return t=this.prefetch(100),e=[],[4,t.next()];case 1:n=r.sent(),r.label=2;case 2:return n.done?[3,4]:(e.push(n.value),[4,t.next()]);case 3:return n=r.sent(),[3,2];case 4:return[2,e]}}))}))},t.prototype.resolveFully=function(){return u(this,void 0,void 0,(function(){var t;return s(this,(function(e){switch(e.label){case 0:return[4,this.next()];case 1:t=e.sent(),e.label=2;case 2:return t.done?[3,4]:[4,this.next()];case 3:return t=e.sent(),[3,2];case 4:return[2]}}))}))},t.prototype.resolveWhile=function(t){return u(this,void 0,void 0,(function(){var e,n;return s(this,(function(r){switch(r.label){case 0:return[4,this.next()];case 1:e=r.sent(),n=t(e.value),r.label=2;case 2:return e.done||!n?[3,4]:[4,this.next()];case 3:return e=r.sent(),n=t(e.value),[3,2];case 4:return[2]}}))}))},t.prototype.handleErrors=function(t){return new rt(this,t)},t.prototype.filter=function(t){return new et(this,t)},t.prototype.map=function(t){return new nt(this,t)},t.prototype.mapAsync=function(t){return new it(this,t)},t.prototype.serialMapAsync=function(t){return new it(this,t).serial()},t.prototype.flatmap=function(t){return new ut(this,t)},t.prototype.forEachAsync=function(t){return u(this,void 0,void 0,(function(){return s(this,(function(e){return[2,this.map(t).resolveFully()]}))}))},t.prototype.serialForEach=function(t){return u(this,void 0,void 0,(function(){return s(this,(function(e){return[2,this.serialMapAsync(t).resolveWhile((function(t){return!0===t}))]}))}))},t.prototype.rowMajorBatch=function(t,e){return void 0===e&&(e=!0),new tt(this,t,e)},t.prototype.columnMajorBatch=function(t,e,n){return void 0===e&&(e=!0),void 0===n&&(n=B),this.rowMajorBatch(t,e).map((function(t){return j(t,n)}))},t.prototype.concatenate=function(t,e){return new st(U([this,t]),e)},t.prototype.take=function(t){return t<0||null==t?this:new $(this,t)},t.prototype.skip=function(t){return t<0||null==t?this:new K(this,t)},t.prototype.prefetch=function(t){return new ct(this,t)},t.prototype.shuffle=function(t,e){return new lt(this,t,e)},t.prototype.serial=function(){return new Y(this)},t}(),Z=function(t){function e(e){var n=t.call(this)||this;return n.items=e,n.trav=0,n}return o(e,t),e.prototype.summary=function(){return"Array of ".concat(this.items.length," items")},e.prototype.next=function(){return u(this,void 0,void 0,(function(){var t;return s(this,(function(e){return this.trav>=this.items.length?[2,{value:null,done:!0}]:(t=this.items[this.trav],this.trav++,[2,{value:q(t),done:!1}])}))}))},e}(G),J=function(t){function e(e){var n=t.call(this)||this;return n.nextFn=e,n}return o(e,t),e.prototype.summary=function(){return"Function call"},e.prototype.next=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){try{return[2,this.nextFn()]}catch(t){throw t.message="Error thrown while iterating through a dataset: ".concat(t.message),t}return[2]}))}))},e}(G),Y=function(t){function e(e){var n=t.call(this)||this;return n.upstream=e,n.lastRead=Promise.resolve({value:null,done:!1}),n}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> Serial")},e.prototype.next=function(){return u(this,void 0,void 0,(function(){var t=this;return s(this,(function(e){return this.lastRead=this.lastRead.then((function(){return t.serialNext()})),[2,this.lastRead]}))}))},e.prototype.serialNext=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){return[2,this.upstream.next()]}))}))},e}(G),K=function(t){function e(e,n){var r=t.call(this)||this;return r.upstream=e,r.maxCount=n,r.count=0,r.lastRead=Promise.resolve({value:null,done:!1}),r}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> Skip")},e.prototype.next=function(){return u(this,void 0,void 0,(function(){var t=this;return s(this,(function(e){return this.lastRead=this.lastRead.then((function(){return t.serialNext()})),[2,this.lastRead]}))}))},e.prototype.serialNext=function(){return u(this,void 0,void 0,(function(){var t;return s(this,(function(e){switch(e.label){case 0:return this.count++<this.maxCount?[4,this.upstream.next()]:[3,2];case 1:return(t=e.sent()).done?[2,t]:(r.dispose(t.value),[3,0]);case 2:return[2,this.upstream.next()]}}))}))},e}(G),$=function(t){function e(e,n){var r=t.call(this)||this;return r.upstream=e,r.maxCount=n,r.count=0,r}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> Take")},e.prototype.next=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){return this.count++>=this.maxCount?[2,{value:null,done:!0}]:[2,this.upstream.next()]}))}))},e}(G),tt=function(t){function e(e,n,r){void 0===r&&(r=!0);var i=t.call(this)||this;return i.upstream=e,i.batchSize=n,i.enableSmallLastBatch=r,i.lastRead=Promise.resolve({value:null,done:!1}),i}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> RowMajorBatch")},e.prototype.next=function(){return u(this,void 0,void 0,(function(){var t=this;return s(this,(function(e){return this.lastRead=this.lastRead.then((function(){return t.serialNext()})),[2,this.lastRead]}))}))},e.prototype.serialNext=function(){return u(this,void 0,void 0,(function(){var t,e;return s(this,(function(n){switch(n.label){case 0:t=[],n.label=1;case 1:return t.length<this.batchSize?[4,this.upstream.next()]:[3,3];case 2:return(e=n.sent()).done?this.enableSmallLastBatch&&t.length>0?[2,{value:t,done:!1}]:[2,{value:null,done:!0}]:(t.push(e.value),[3,1]);case 3:return[2,{value:t,done:!1}]}}))}))},e}(G),et=function(t){function e(e,n){var r=t.call(this)||this;return r.upstream=e,r.predicate=n,r.lastRead=Promise.resolve({value:null,done:!1}),r}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> Filter")},e.prototype.next=function(){return u(this,void 0,void 0,(function(){var t=this;return s(this,(function(e){return this.lastRead=this.lastRead.then((function(){return t.serialNext()})),[2,this.lastRead]}))}))},e.prototype.serialNext=function(){return u(this,void 0,void 0,(function(){var t;return s(this,(function(e){switch(e.label){case 0:return[4,this.upstream.next()];case 1:return(t=e.sent()).done||this.predicate(t.value)?[2,t]:(r.dispose(t.value),[3,0]);case 2:return[2]}}))}))},e}(G),nt=function(t){function e(e,n){var r=t.call(this)||this;return r.upstream=e,r.transform=n,r}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> Map")},e.prototype.next=function(){return u(this,void 0,void 0,(function(){var t,e,n,i,o,u,c,l,f;return s(this,(function(s){switch(s.label){case 0:return[4,this.upstream.next()];case 1:if((t=s.sent()).done)return[2,{value:null,done:!0}];e=r.tensor_util.getTensorsInContainer(t.value),n=this.transform(t.value),i=r.tensor_util.getTensorsInContainer(n);try{for(o=a(e),u=o.next();!u.done;u=o.next())c=u.value,r.tensor_util.isTensorInList(c,i)||c.dispose()}catch(t){l={error:t}}finally{try{u&&!u.done&&(f=o.return)&&f.call(o)}finally{if(l)throw l.error}}return[2,{value:n,done:!1}]}}))}))},e}(G),rt=function(t){function e(e,n){var r=t.call(this)||this;return r.upstream=e,r.handler=n,r.count=0,r.lastRead=Promise.resolve({value:null,done:!1}),r}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> handleErrors")},e.prototype.next=function(){return u(this,void 0,void 0,(function(){var t=this;return s(this,(function(e){return this.lastRead=this.lastRead.then((function(){return t.serialNext()})),[2,this.lastRead]}))}))},e.prototype.serialNext=function(){return u(this,void 0,void 0,(function(){var t;return s(this,(function(e){switch(e.label){case 0:e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.upstream.next()];case 2:return[2,e.sent()];case 3:return t=e.sent(),this.handler(t)?[3,4]:[2,{value:null,done:!0}];case 4:return[3,0];case 5:return[2]}}))}))},e}(G),it=function(t){function e(e,n){var r=t.call(this)||this;return r.upstream=e,r.transform=n,r}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> AsyncMap")},e.prototype.next=function(){return u(this,void 0,void 0,(function(){var t,e,n,i,o,u,c,l,f;return s(this,(function(s){switch(s.label){case 0:return[4,this.upstream.next()];case 1:return(t=s.sent()).done?[2,{value:null,done:!0}]:(e=r.tensor_util.getTensorsInContainer(t.value),[4,this.transform(t.value)]);case 2:n=s.sent(),i=r.tensor_util.getTensorsInContainer(n);try{for(o=a(e),u=o.next();!u.done;u=o.next())c=u.value,r.tensor_util.isTensorInList(c,i)||c.dispose()}catch(t){l={error:t}}finally{try{u&&!u.done&&(f=o.return)&&f.call(o)}finally{if(l)throw l.error}}return[2,{value:n,done:!1}]}}))}))},e}(G),ot=function(t){function e(){var e=t.call(this)||this;return e.outputQueue=new V,e.lastRead=Promise.resolve({value:null,done:!1}),e}return o(e,t),e.prototype.next=function(){return u(this,void 0,void 0,(function(){var t=this;return s(this,(function(e){return this.lastRead=this.lastRead.then((function(){return t.serialNext()})),[2,this.lastRead]}))}))},e.prototype.serialNext=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return 0!==this.outputQueue.length()?[3,2]:[4,this.pump()];case 1:return t.sent()?[3,0]:[2,{value:null,done:!0}];case 2:return[2,{value:this.outputQueue.shift(),done:!1}]}}))}))},e}(G),ut=function(t){function e(e,n){var r=t.call(this)||this;return r.upstream=e,r.transform=n,r}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> Flatmap")},e.prototype.pump=function(){return u(this,void 0,void 0,(function(){var t,e,n,i,o,u,c,l,f;return s(this,(function(s){switch(s.label){case 0:return[4,this.upstream.next()];case 1:if((t=s.sent()).done)return[2,!1];e=r.tensor_util.getTensorsInContainer(t.value),n=this.transform(t.value),i=r.tensor_util.getTensorsInContainer(n),this.outputQueue.pushAll(n);try{for(o=a(e),u=o.next();!u.done;u=o.next())c=u.value,r.tensor_util.isTensorInList(c,i)||c.dispose()}catch(t){l={error:t}}finally{try{u&&!u.done&&(f=o.return)&&f.call(o)}finally{if(l)throw l.error}}return[2,!0]}}))}))},e}(ot),st=function(t){function e(e,n){var r=t.call(this)||this;return r.baseErrorHandler=n,r.lastRead=null,r.iterator=null,r.moreIterators=e,r}return o(e,t),e.prototype.summary=function(){return"".concat("TODO: fill in upstream of chained summaries"," -> Chained")},e.prototype.next=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){return this.lastRead=this.readFromChain(this.lastRead),[2,this.lastRead]}))}))},e.prototype.readFromChain=function(t){return u(this,void 0,void 0,(function(){var e,n;return s(this,(function(r){switch(r.label){case 0:return[4,t];case 1:return r.sent(),null!=this.iterator?[3,3]:[4,this.moreIterators.next()];case 2:if((e=r.sent()).done)return[2,{value:null,done:!0}];this.iterator=e.value,null!=this.baseErrorHandler&&(this.iterator=this.iterator.handleErrors(this.baseErrorHandler)),r.label=3;case 3:return[4,this.iterator.next()];case 4:return(n=r.sent()).done?(this.iterator=null,[2,this.readFromChain(t)]):[2,n]}}))}))},e}(G);!function(t){t[t.FAIL=0]="FAIL",t[t.SHORTEST=1]="SHORTEST",t[t.LONGEST=2]="LONGEST"}(X||(X={}));var at=function(t){function e(e,n){void 0===n&&(n=X.FAIL);var r=t.call(this)||this;return r.iterators=e,r.mismatchMode=n,r.count=0,r.currentPromise=null,r}return o(e,t),e.prototype.summary=function(){return"{".concat("TODO: fill in upstream of zip summaries","} -> Zip")},e.prototype.nextState=function(t){return u(this,void 0,void 0,(function(){function e(t){return t instanceof G?{value:t.next().then((function(t){return n++,t.done&&r++,t.value})),recurse:!1}:{value:null,recurse:!0}}var n,r,i;return s(this,(function(o){switch(o.label){case 0:return[4,t];case 1:return o.sent(),n=0,r=0,[4,P(this.iterators,e)];case 2:if(i=o.sent(),n===r)return[2,{value:null,done:!0}];if(r>0)switch(this.mismatchMode){case X.FAIL:throw new Error("Zipped streams should have the same length. "+"Mismatched at element ".concat(this.count,"."));case X.SHORTEST:return[2,{value:null,done:!0}];case X.LONGEST:}return this.count++,[2,{value:i,done:!1}]}}))}))},e.prototype.next=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){return this.currentPromise=this.nextState(this.currentPromise),[2,this.currentPromise]}))}))},e}(G),ct=function(t){function e(e,n){var r=t.call(this)||this;return r.upstream=e,r.bufferSize=n,r.buffer=new W(n),r}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> Prefetch")},e.prototype.refill=function(){for(;!this.buffer.isFull();){var t=this.upstream.next();this.buffer.push(t)}},e.prototype.next=function(){return this.refill(),this.buffer.shift()},e}(G),lt=function(t){function e(e,n,i){var o=t.call(this,e,n)||this;return o.upstream=e,o.windowSize=n,o.upstreamExhausted=!1,o.random=D.alea(i||r.util.now().toString()),o.lastRead=Promise.resolve({value:null,done:!1}),o}return o(e,t),e.prototype.next=function(){return u(this,void 0,void 0,(function(){var t=this;return s(this,(function(e){return this.lastRead=this.lastRead.then((function(){return t.serialNext()})),[2,this.lastRead]}))}))},e.prototype.randomInt=function(t){return Math.floor(this.random()*t)},e.prototype.chooseIndex=function(){return this.randomInt(this.buffer.length())},e.prototype.serialNext=function(){return u(this,void 0,void 0,(function(){var t,e;return s(this,(function(n){switch(n.label){case 0:this.upstreamExhausted||this.refill(),n.label=1;case 1:return this.buffer.isEmpty()?[3,3]:(t=this.chooseIndex(),[4,this.buffer.shuffleExcise(t)]);case 2:return(e=n.sent()).done?(this.upstreamExhausted=!0,[3,1]):(this.refill(),[2,e]);case 3:return[2,{value:null,done:!0}]}}))}))},e}(ct),ft=function(){function t(){this.size=null}return t.prototype.batch=function(t,e){var n=this;void 0===e&&(e=!0);var i=this;return r.util.assert(t>0,(function(){return"batchSize needs to be positive, but it is\n      ".concat(t)})),ht((function(){return u(n,void 0,void 0,(function(){return s(this,(function(n){switch(n.label){case 0:return[4,i.iterator()];case 1:return[2,n.sent().columnMajorBatch(t,e,pt)]}}))}))}),this.size===1/0||null==this.size?this.size:e?Math.ceil(this.size/t):Math.floor(this.size/t))},t.prototype.concatenate=function(t){var e=this,n=this;return ht((function(){return u(e,void 0,void 0,(function(){var e,r;return s(this,(function(i){switch(i.label){case 0:return[4,n.iterator()];case 1:return r=(e=i.sent()).concatenate,[4,t.iterator()];case 2:return[2,r.apply(e,[i.sent()])]}}))}))}),this.size===1/0||t.size===1/0?1/0:null!=this.size&&null!=t.size?this.size+t.size:null)},t.prototype.filter=function(t){var e=this,n=this;return ht((function(){return u(e,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,n.iterator()];case 1:return[2,e.sent().filter((function(e){return r.tidy((function(){return t(e)}))}))]}}))}))}),this.size===1/0?1/0:null)},t.prototype.forEachAsync=function(t){return u(this,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,this.iterator()];case 1:return[2,e.sent().forEachAsync(t)]}}))}))},t.prototype.map=function(t){var e=this,n=this;return ht((function(){return u(e,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,n.iterator()];case 1:return[2,e.sent().map((function(e){return r.tidy((function(){return t(e)}))}))]}}))}))}),this.size)},t.prototype.mapAsync=function(t){var e=this,n=this;return ht((function(){return u(e,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,n.iterator()];case 1:return[2,e.sent().mapAsync(t)]}}))}))}),this.size)},t.prototype.prefetch=function(t){var e=this;if(null==t)throw new RangeError("`Dataset.prefetch()` requires bufferSize to be specified.");var n=this;return ht((function(){return u(e,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,n.iterator()];case 1:return[2,e.sent().prefetch(t)]}}))}))}),this.size)},t.prototype.repeat=function(t){var e=this,n=this;return ht((function(){return u(e,void 0,void 0,(function(){var e,r=this;return s(this,(function(i){return e=Q((function(){return u(r,void 0,void 0,(function(){var t;return s(this,(function(e){switch(e.label){case 0:return t={},[4,n.iterator()];case 1:return[2,(t.value=e.sent(),t.done=!1,t)]}}))}))})),[2,(o=e.take(t),new st(o,a))];var o,a}))}))}),null!=this.size&&t>0?this.size*t:0===t?0:null!=this.size&&(void 0===t||t<0)?1/0:null)},t.prototype.skip=function(t){var e=this,n=this;return ht((function(){return u(e,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,n.iterator()];case 1:return[2,e.sent().skip(t)]}}))}))}),null!=this.size&&t>=0&&this.size>=t?this.size-t:null!=this.size&&(this.size<t||void 0===t||t<0)?0:null)},t.prototype.shuffle=function(t,e,n){var i=this;if(void 0===n&&(n=!0),null==t||t<0)throw null==this.size?new RangeError("`Dataset.shuffle()` requires bufferSize to be specified."):new RangeError("`Dataset.shuffle()` requires bufferSize to be specified.  If your data fits in main memory (for regular JS objects), and/or GPU memory (for `tf.Tensor`s), consider setting "+"bufferSize to the dataset size (".concat(this.size," elements)"));var o=this,a=D.alea(e||r.util.now().toString());return ht((function(){return u(i,void 0,void 0,(function(){var e;return s(this,(function(r){switch(r.label){case 0:return e=a.int32(),n&&(e+=a.int32()),[4,o.iterator()];case 1:return[2,r.sent().shuffle(t,e.toString())]}}))}))}),this.size)},t.prototype.take=function(t){var e=this,n=this;return ht((function(){return u(e,void 0,void 0,(function(){return s(this,(function(e){switch(e.label){case 0:return[4,n.iterator()];case 1:return[2,e.sent().take(t)]}}))}))}),null!=this.size&&this.size>t?t:null!=this.size&&this.size<=t?this.size:null)},t.prototype.toArray=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:if(this.size===1/0)throw new Error("Can not convert infinite data stream to array.");return[4,this.iterator()];case 1:return[2,t.sent().toArray()]}}))}))},t.prototype.toArrayForTest=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:if(this.size===1/0)throw new Error("Can not convert infinite data stream to array.");return[4,this.iterator()];case 1:return[2,t.sent().toArrayForTest()]}}))}))},t}();function ht(t,e){return void 0===e&&(e=null),new(function(n){function r(){var t=n.apply(this,l([],c(arguments),!1))||this;return t.size=e,t}return o(r,n),r.prototype.iterator=function(){return u(this,void 0,void 0,(function(){return s(this,(function(e){return[2,t()]}))}))},r}(ft))}function pt(t){return null===t?null:function(t){return null==t||null===(e=t)||"object"!=typeof e&&"function"!=typeof e||Array.isArray(t)||"object"==typeof t&&t instanceof r.Tensor||r.util.isTypedArray(t);var e}(t[0])?{value:function(t){if(0===t.length)throw new Error("Can't make a batch of zero elements.");return t[0]instanceof r.Tensor?r.stack(t):r.tensor(t)}(t),recurse:!1}:{value:null,recurse:!0}}ft.MAX_BUFFER_SIZE=1e4;var dt=function(t){function e(e){var n=t.call(this)||this;return n.input=e,n}return o(e,t),e.prototype.iterator=function(){return u(this,void 0,void 0,(function(){var t,e;return s(this,(function(n){switch(n.label){case 0:return[4,this.input.iterator()];case 1:return t=n.sent(),e=t.decodeUTF8(),[2,e.split("\n").map((function(t){return t.endsWith("\r")&&(t=t.slice(0,-1)),t}))]}}))}))},e}(ft),vt='"',mt=Symbol("out"),yt=Symbol("field"),wt=Symbol("quote"),bt=Symbol("quoteafterquote"),gt=Symbol("quoteinquote"),xt=function(t){function n(n,r){var i=t.call(this)||this;return i.input=n,i.hasHeader=!0,i.fullColumnNames=null,i.columnNamesValidated=!1,i.columnConfigs=null,i.configuredColumnsOnly=!1,i.delimiter=",",i.delimWhitespace=!1,i.base=new dt(n),r||(r={}),i.hasHeader=!1!==r.hasHeader,i.fullColumnNames=r.columnNames,i.columnConfigs=r.columnConfigs,i.configuredColumnsOnly=r.configuredColumnsOnly,r.delimWhitespace?(e.util.assert(null==r.delimiter,(function(){return"Delimiter should not be provided when delimWhitespace is true."})),i.delimWhitespace=!0,i.delimiter=" "):i.delimiter=r.delimiter?r.delimiter:",",i}return o(n,t),n.prototype.columnNames=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return this.columnNamesValidated?[3,2]:[4,this.setColumnNames()];case 1:t.sent(),t.label=2;case 2:return[2,this.configuredColumnsOnly?Object.keys(this.columnConfigs):this.fullColumnNames]}}))}))},n.prototype.setColumnNames=function(){return u(this,void 0,void 0,(function(){var t,n,r,i,o,u,c,l,f=this;return s(this,(function(s){switch(s.label){case 0:return[4,this.maybeReadHeaderLine()];case 1:if(t=s.sent(),!this.fullColumnNames&&!t)throw new Error("Column names must be provided if there is no header line.");if(this.fullColumnNames&&t&&e.util.assert(t.length===this.fullColumnNames.length,(function(){return"The length of provided columnNames ("+f.fullColumnNames.length.toString()+") does not match the length of the header line read from file ("+t.length.toString()+")."})),this.fullColumnNames||(this.fullColumnNames=t),n=this.fullColumnNames.reduce((function(t,e){return t[e]=t[e]+1||1,t}),{}),r=Object.keys(n).filter((function(t){return n[t]>1})),e.util.assert(0===r.length,(function(){return"Duplicate column names found: "+r.toString()})),this.columnConfigs)try{for(i=a(Object.keys(this.columnConfigs)),o=i.next();!o.done;o=i.next())if(u=o.value,-1===this.fullColumnNames.indexOf(u))throw new Error('The key "'+u+'" provided in columnConfigs does not match any of the column names ('+this.fullColumnNames.toString()+").")}catch(t){c={error:t}}finally{try{o&&!o.done&&(l=i.return)&&l.call(i)}finally{if(c)throw c.error}}return this.columnNamesValidated=!0,[2]}}))}))},n.prototype.maybeReadHeaderLine=function(){return u(this,void 0,void 0,(function(){var t,e;return s(this,(function(n){switch(n.label){case 0:return this.hasHeader?[4,this.base.iterator()]:[3,3];case 1:return[4,n.sent().next()];case 2:if((t=n.sent()).done)throw new Error("No data was found for CSV parsing.");return e=t.value,[2,this.parseRow(e,!1)];case 3:return[2,null]}}))}))},n.prototype.iterator=function(){return u(this,void 0,void 0,(function(){var t,e=this;return s(this,(function(n){switch(n.label){case 0:return this.columnNamesValidated?[3,2]:[4,this.setColumnNames()];case 1:n.sent(),n.label=2;case 2:return[4,this.base.iterator()];case 3:return t=n.sent(),this.hasHeader&&(t=t.skip(1)),[2,t.map((function(t){return e.makeDataElement(t)}))]}}))}))},n.prototype.makeDataElement=function(t){for(var e=this.parseRow(t),n={},r={},i=0;i<this.fullColumnNames.length;i++){var o=this.fullColumnNames[i],u=this.columnConfigs?this.columnConfigs[o]:null;if(!this.configuredColumnsOnly||u){var s=e[i],a=null;if(""===s)if(u&&void 0!==u.default)a=u.default;else{if(u&&(u.required||u.isLabel))throw new Error("Required column ".concat(o," is empty in this line: ").concat(t));a=void 0}else{var c=Number(s);if(isNaN(c))a=u&&"bool"===u.dtype?this.getBoolean(s):s;else if(u&&u.dtype)switch(u.dtype){case"float32":default:a=c;break;case"int32":a=Math.floor(c);break;case"bool":a=this.getBoolean(s)}else a=c}u&&u.isLabel?r[o]=a:n[o]=a}}return 0===Object.keys(r).length?n:{xs:n,ys:r}},n.prototype.getBoolean=function(t){return"1"===t||"true"===t.toLowerCase()?1:0},n.prototype.parseRow=function(t,e){void 0===e&&(e=!0);for(var n=[],r=0,i=t.length,o=mt,u=0;u<i;u++)switch(o){case mt:switch(t.charAt(u)){case vt:r=u+1,o=wt;break;case this.delimiter:if(r=u+1," "===this.delimiter&&this.delimWhitespace)break;n.push(""),o=mt;break;default:o=yt,r=u}break;case yt:if(t.charAt(u)===this.delimiter)n.push(t.substring(r,u)),o=mt,r=u+1;break;case wt:if(t.charAt(u)===vt)o=bt;break;case bt:switch(t.charAt(u)){case this.delimiter:n.push(t.substring(r,u-1)),o=mt,r=u+1;break;case vt:o=wt;break;default:o=gt}break;case gt:if(t.charAt(u)===vt)o=wt}if(o===bt?n.push(t.substring(r,i-1)):n.push(t.substring(r)),e&&n.length!==this.fullColumnNames.length)throw new Error("Invalid row in csv file. Should have ".concat(this.fullColumnNames.length," elements in a row, but got ").concat(n));return n},n}(ft),Ct=function(t){function n(e){var n=t.call(this)||this;n.microphoneConfig=e,n.isClosed=!1,n.fftSize=e.fftSize||1024;var r=Math.log2(n.fftSize);if(n.fftSize<0||r<4||r>14||!Number.isInteger(r))throw new Error("Invalid fftSize: it must be a power of 2 between "+"2 to 4 and 2 to 14, but got ".concat(n.fftSize));if(n.numFrames=e.numFramesPerSpectrogram||43,n.sampleRateHz=e.sampleRateHz,n.columnTruncateLength=e.columnTruncateLength||n.fftSize,n.audioTrackConstraints=e.audioTrackConstraints,n.smoothingTimeConstant=e.smoothingTimeConstant||0,n.includeSpectrogram=!1!==e.includeSpectrogram,n.includeWaveform=!0===e.includeWaveform,!n.includeSpectrogram&&!n.includeWaveform)throw new Error("Both includeSpectrogram and includeWaveform are false. At least one type of data should be returned.");return n}return o(n,t),n.prototype.summary=function(){return"microphone"},n.create=function(t){return void 0===t&&(t={}),u(this,void 0,void 0,(function(){var r;return s(this,(function(i){switch(i.label){case 0:if(!e.env().get("IS_BROWSER"))throw new Error("microphone API is only supported in browser environment.");return[4,(r=new n(t)).start()];case 1:return i.sent(),[2,r]}}))}))},n.prototype.start=function(){return u(this,void 0,void 0,(function(){var t,e,n,r;return s(this,(function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),t=this,[4,navigator.mediaDevices.getUserMedia({audio:null==this.audioTrackConstraints||this.audioTrackConstraints,video:!1})];case 1:return t.stream=i.sent(),[3,3];case 2:throw e=i.sent(),new Error("Error thrown while initializing video stream: ".concat(e.message));case 3:if(!this.stream)throw new Error("Could not obtain audio from microphone.");if(n=window.AudioContext||window.webkitAudioContext,this.audioContext=new n,this.sampleRateHz){if(this.audioContext.sampleRate!==this.sampleRateHz)throw new Error("Mismatch in sampling rate: "+"Expected: ".concat(this.sampleRateHz,"; ")+"Actual: ".concat(this.audioContext.sampleRate))}else this.sampleRateHz=this.audioContext.sampleRate;return r=this.audioContext.createMediaStreamSource(this.stream),this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=2*this.fftSize,this.analyser.smoothingTimeConstant=this.smoothingTimeConstant,r.connect(this.analyser),this.freqData=new Float32Array(this.fftSize),this.timeData=new Float32Array(this.fftSize),[2]}}))}))},n.prototype.next=function(){return u(this,void 0,void 0,(function(){var t,e,n,r,i;return s(this,(function(o){switch(o.label){case 0:return this.isClosed?[2,{value:null,done:!0}]:[4,this.getAudioData()];case 1:return n=o.sent(),this.includeSpectrogram&&(r=this.flattenQueue(n.freqDataQueue),t=this.getTensorFromAudioDataArray(r,[this.numFrames,this.columnTruncateLength,1])),this.includeWaveform&&(i=this.flattenQueue(n.timeDataQueue),e=this.getTensorFromAudioDataArray(i,[this.numFrames*this.fftSize,1])),[2,{value:{spectrogram:t,waveform:e},done:!1}]}}))}))},n.prototype.capture=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return[4,this.next()];case 1:return[2,t.sent().value]}}))}))},n.prototype.getAudioData=function(){return u(this,void 0,void 0,(function(){var t,e,n,r=this;return s(this,(function(i){return t=[],e=[],n=0,[2,new Promise((function(i){var o=setInterval((function(){r.includeSpectrogram&&(r.analyser.getFloatFrequencyData(r.freqData),r.freqData[0]===-1/0&&i({freqDataQueue:t,timeDataQueue:e}),t.push(r.freqData.slice(0,r.columnTruncateLength))),r.includeWaveform&&(r.analyser.getFloatTimeDomainData(r.timeData),e.push(r.timeData.slice())),++n===r.numFrames&&(clearInterval(o),i({freqDataQueue:t,timeDataQueue:e}))}),r.fftSize/r.sampleRateHz*1e3)}))]}))}))},n.prototype.stop=function(){this.isClosed||(this.isClosed=!0,this.analyser.disconnect(),this.audioContext.close(),null!=this.stream&&this.stream.getTracks().length>0&&this.stream.getTracks()[0].stop())},n.prototype.toArray=function(){throw new Error("Can not convert infinite audio stream to array.")},n.prototype.getSampleRate=function(){return this.sampleRateHz},n.prototype.flattenQueue=function(t){var e=t[0].length,n=new Float32Array(t.length*e);return t.forEach((function(t,r){return n.set(t,r*e)})),n},n.prototype.getTensorFromAudioDataArray=function(t,n){var r=new Float32Array(e.util.sizeFromShape(n));return r.set(t,r.length-t.length),e.tensor(r,n)},n}(G),zt=function(t){function n(n,r){var i=t.call(this)||this;if(i.webcamVideoElement=n,i.webcamConfig=r,i.isClosed=!0,i.resize=!1,i.needToResize())if(i.resize=!0,i.cropSize=[i.webcamConfig.resizeHeight,i.webcamConfig.resizeWidth],i.cropBoxInd=e.tensor1d([0],"int32"),i.webcamConfig.centerCrop){var o=1*i.webcamConfig.resizeWidth/i.webcamVideoElement.width,u=1*i.webcamConfig.resizeHeight/i.webcamVideoElement.height,s=(1-o)/2,a=(1-u)/2,c=s+o,l=u+a;i.cropBox=e.tensor2d([a,s,l,c],[1,4])}else i.cropBox=e.tensor2d([0,0,1,1],[1,4]);return i}return o(n,t),n.prototype.summary=function(){return"webcam"},n.create=function(t,r){return void 0===r&&(r={}),u(this,void 0,void 0,(function(){var i;return s(this,(function(o){switch(o.label){case 0:if(!e.env().get("IS_BROWSER"))throw new Error("tf.data.webcam is only supported in browser environment.");if(!t){if(t=document.createElement("video"),!r.resizeWidth||!r.resizeHeight)throw new Error("Please provide webcam video element, or resizeWidth and resizeHeight to create a hidden video element.");t.width=r.resizeWidth,t.height=r.resizeHeight}return[4,(i=new n(t,r)).start()];case 1:return o.sent(),[2,i]}}))}))},n.prototype.start=function(){return u(this,void 0,void 0,(function(){var t,n,r=this;return s(this,(function(i){switch(i.label){case 0:this.webcamConfig.facingMode&&e.util.assert("user"===this.webcamConfig.facingMode||"environment"===this.webcamConfig.facingMode,(function(){return"Invalid webcam facing mode: ".concat(r.webcamConfig.facingMode,". ")+"Please provide 'user' or 'environment'"})),i.label=1;case 1:return i.trys.push([1,3,,4]),t=this,[4,navigator.mediaDevices.getUserMedia({video:{deviceId:this.webcamConfig.deviceId,facingMode:this.webcamConfig.facingMode?this.webcamConfig.facingMode:"user",width:this.webcamVideoElement.width,height:this.webcamVideoElement.height}})];case 2:return t.stream=i.sent(),[3,4];case 3:throw(n=i.sent()).message="Error thrown while initializing video stream: ".concat(n.message),n;case 4:if(!this.stream)throw new Error("Could not obtain video from webcam.");try{this.webcamVideoElement.srcObject=this.stream}catch(t){console.log(t),this.webcamVideoElement.src=window.URL.createObjectURL(this.stream)}return this.webcamVideoElement.play(),this.isClosed=!1,[2,new Promise((function(t){r.webcamVideoElement.onloadedmetadata=function(){t()}}))]}}))}))},n.prototype.next=function(){return u(this,void 0,void 0,(function(){var t;return s(this,(function(n){if(this.isClosed)return[2,{value:null,done:!0}];try{t=e.browser.fromPixels(this.webcamVideoElement)}catch(t){throw new Error("Error thrown converting video to pixels: ".concat(JSON.stringify(t)))}if(!this.resize)return[2,{value:t,done:!1}];try{return[2,{value:this.cropAndResizeFrame(t),done:!1}]}catch(t){throw new Error("Error thrown cropping the video: ".concat(t.message))}finally{t.dispose()}return[2]}))}))},n.prototype.needToResize=function(){return!(!this.webcamConfig.resizeWidth||!this.webcamConfig.resizeHeight||this.webcamVideoElement.width===this.webcamConfig.resizeWidth&&this.webcamVideoElement.height===this.webcamConfig.resizeHeight)},n.prototype.cropAndResizeFrame=function(t){var n=this;return e.tidy((function(){var r,i=e.expandDims(e.cast(t,"float32"),0),o=(r=e.image.cropAndResize(i,n.cropBox,n.cropBoxInd,n.cropSize,"bilinear")).shape;return e.reshape(r,o.slice(1))}))},n.prototype.capture=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){switch(t.label){case 0:return[4,this.next()];case 1:return[2,t.sent().value]}}))}))},n.prototype.stop=function(){this.stream.getTracks().forEach((function(t){return t.stop()}));try{this.webcamVideoElement.srcObject=null}catch(t){console.log(t),this.webcamVideoElement.src=null}this.isClosed=!0},n.prototype.toArray=function(){throw new Error("Can not convert infinite video stream to array.")},n}(G),Et=function(){},St=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.split=function(t){return new At(this,t)},e}(G),At=function(t){function e(e,n){var r=t.call(this)||this;return r.upstream=e,r.impl=new Rt(e,n),r}return o(e,t),e.prototype.summary=function(){return this.impl.summary()},e.prototype.next=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){return[2,this.impl.next()]}))}))},e}(St),Rt=function(t){function e(e,n){var r=t.call(this)||this;return r.upstream=e,r.separator=n,r.carryover="",r}return o(e,t),e.prototype.summary=function(){return"".concat(this.upstream.summary()," -> Split('").concat(this.separator,"')")},e.prototype.pump=function(){return u(this,void 0,void 0,(function(){var t,e,n,r,i,o,u;return s(this,(function(s){switch(s.label){case 0:return[4,this.upstream.next()];case 1:if((t=s.sent()).done)return""===this.carryover?[2,!1]:(this.outputQueue.push(this.carryover),this.carryover="",[2,!0]);(e=t.value.split(this.separator))[0]=this.carryover+e[0];try{for(n=a(e.slice(0,-1)),r=n.next();!r.done;r=n.next())i=r.value,this.outputQueue.push(i)}catch(t){o={error:t}}finally{try{r&&!r.done&&(u=n.return)&&u.call(n)}finally{if(o)throw o.error}}return this.carryover=e[e.length-1],[2,!0]}}))}))},e}(ot),Tt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e.prototype.decodeUTF8=function(){return new kt(this)},e}(G),kt=function(t){function e(e){var n=t.call(this)||this;return n.upstream=e,n.impl=new Ft(e),n}return o(e,t),e.prototype.summary=function(){return this.impl.summary()},e.prototype.next=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){return[2,this.impl.next()]}))}))},e}(St),Ft=function(t){function n(n){var r=t.call(this)||this;if(r.upstream=n,e.env().get("IS_BROWSER"))r.decoder=new TextDecoder("utf-8");else{var i=require("string_decoder").StringDecoder;r.decoder=new i("utf8")}return r}return o(n,t),n.prototype.summary=function(){return"".concat(this.upstream.summary()," -> Utf8")},n.prototype.pump=function(){return u(this,void 0,void 0,(function(){var t,n,r;return s(this,(function(i){switch(i.label){case 0:return[4,this.upstream.next()];case 1:return(t=i.sent()).done?[2,!1]:(n=t.value,r=e.env().get("IS_BROWSER")?this.decoder.decode(n,{stream:!0}):this.decoder.write(Buffer.from(n.buffer)),this.outputQueue.push(r),[2,!0])}}))}))},n}(ot),Nt=function(t){function n(n,r){void 0===r&&(r={});var i=t.call(this)||this;return i.file=n,i.options=r,e.util.assert(n instanceof Uint8Array||!!e.env().get("IS_BROWSER")&&(n instanceof File||n instanceof Blob),(function(){return"FileChunkIterator only supports File, Blob and Uint8Array right now."})),i.offset=r.offset||0,i.chunkSize=r.chunkSize||1048576,i}return o(n,t),n.prototype.summary=function(){return"FileChunks ".concat(this.file)},n.prototype.next=function(){return u(this,void 0,void 0,(function(){var t,e,n=this;return s(this,(function(r){switch(r.label){case 0:return this.offset>=(this.file instanceof Uint8Array?this.file.byteLength:this.file.size)?[2,{value:null,done:!0}]:(t=new Promise((function(t,e){var r=n.offset+n.chunkSize;if(n.file instanceof Uint8Array)t(new Uint8Array(n.file.slice(n.offset,r)));else{var i=new FileReader;i.onload=function(n){var r=i.result;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),!(r instanceof Uint8Array))return e(new TypeError("FileReader returned unknown type."));t(r)},i.onabort=function(t){return e(new Error("Aborted"))},i.onerror=function(t){return e(new Error(t.type))};var o=n.file.slice(n.offset,r);i.readAsArrayBuffer(o)}n.offset=r})),e={},[4,t]);case 1:return[2,(e.value=r.sent(),e.done=!1,e)]}}))}))},n}(Tt);function Ot(t,n,r){return void 0===n&&(n={}),u(this,void 0,void 0,(function(){var i,o,u,a,c;return s(this,(function(s){switch(s.label){case 0:return"string"==typeof t?i=t:(i=t.url,o=_t(t)),[4,(r||e.util.fetch)(i,o)];case 1:return(u=s.sent()).ok?(c=Uint8Array.bind,[4,u.arrayBuffer()]):[3,3];case 2:return a=new(c.apply(Uint8Array,[void 0,s.sent()])),[2,new Nt(a,n)];case 3:throw new Error(u.statusText)}}))}))}var _t=function(t){return{method:t.method,headers:t.headers,body:t.body,mode:t.mode,credentials:t.credentials,cache:t.cache,redirect:t.redirect,referrer:t.referrer,integrity:t.integrity}};function Dt(t){return"string"==typeof t&&"file://"===t.slice(0,7)}var It=function(t){function n(e,n){void 0===n&&(n={});var r=t.call(this)||this;return r.input=e,r.options=n,r}return o(n,t),n.prototype.iterator=function(){return u(this,void 0,void 0,(function(){var t;return s(this,(function(n){return Dt(this.input)&&e.env().get("IS_NODE")&&(t=require("fs"),this.input=t.readFileSync(this.input.slice(7))),[2,new Nt(this.input,this.options)]}))}))},n}(Et),jt=function(t){function e(e,n){void 0===n&&(n={});var r=t.call(this)||this;return r.url=e,r.fileOptions=n,r}return o(e,t),e.prototype.iterator=function(){return u(this,void 0,void 0,(function(){return s(this,(function(t){return Dt(this.url)?[2,new It(this.url,this.fileOptions).iterator()]:[2,Ot(this.url,this.fileOptions)]}))}))},e}(Et);t.CSVDataset=xt,t.Dataset=ft,t.FileDataSource=It,t.TextLineDataset=dt,t.URLDataSource=jt,t.array=function(t){var e=this;return ht((function(){return u(e,void 0,void 0,(function(){return s(this,(function(e){return[2,U(t)]}))}))}),t.length)},t.csv=function(t,e){return void 0===e&&(e={}),new xt(new jt(t),e)},t.func=function(t){var e=this,n=Q(t);return ht((function(){return u(e,void 0,void 0,(function(){return s(this,(function(t){return[2,n]}))}))}))},t.generator=function(t){var e=this;return ht((function(){return u(e,void 0,void 0,(function(){var e;return s(this,(function(n){switch(n.label){case 0:return[4,t()];case 1:return e=n.sent(),[2,Q((function(){return e.next()}))]}}))}))}))},t.microphone=function(t){return u(this,void 0,void 0,(function(){return s(this,(function(e){return[2,Ct.create(t)]}))}))},t.version_data="4.22.0",t.webcam=function(t,e){return u(this,void 0,void 0,(function(){return s(this,(function(n){return[2,zt.create(t,e)]}))}))},t.zip=function(t){var e,n=this;if(!L(t))throw new Error("The argument to zip() must be an object or array.");if(Array.isArray(t))for(var r=0;r<t.length;r++)e=null==e?t[r].size:Math.min(e,t[r].size);else if(t instanceof Object)for(var i in t)e=null==e?t[i].size:Math.min(e,t[i].size);return ht((function(){return u(n,void 0,void 0,(function(){var e;return s(this,(function(n){switch(n.label){case 0:return[4,P(t,(function(t){if(t instanceof ft)return{value:t.iterator(),recurse:!1};if(L(t))return{value:null,recurse:!0};throw new Error("Leaves of the structure passed to zip() must be Datasets, not primitives.")}))];case 1:return e=n.sent(),[2,(r=e,i=X.SHORTEST,void 0===i&&(i=X.FAIL),new at(r,i))]}var r,i}))}))}),e)}}));
//# sourceMappingURL=tf-data.min.js.map
