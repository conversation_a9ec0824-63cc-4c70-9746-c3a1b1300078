/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import {KernelConfig, SparseToDense, SparseToDenseAttrs, SparseToDenseInputs, tensor1d} from '@tensorflow/tfjs';

import {createTensorsTypeOpAttr, NodeJSKernelBackend} from '../nodejs_kernel_backend';

export const sparseToDenseConfig: KernelConfig = {
  kernelName: SparseToDense,
  backendName: 'tensorflow',
  kernelFunc: (args) => {
    const {sparseIndices, sparseValues, defaultValue} =
        args.inputs as SparseToDenseInputs;
    const backend = args.backend as NodeJSKernelBackend;
    const {outputShape} = args.attrs as unknown as SparseToDenseAttrs;

    const opAttrs = [
      {
        name: 'validate_indices',
        type: backend.binding.TF_ATTR_BOOL,
        value: true
      },
      createTensorsTypeOpAttr('T', sparseValues.dtype),
      createTensorsTypeOpAttr('Tindices', sparseIndices.dtype)
    ];
    const outputShapeTensor = tensor1d(outputShape, 'int32');
    const res = backend.executeSingleOutput(
        SparseToDense, opAttrs,
        [sparseIndices, outputShapeTensor, sparseValues, defaultValue]);
    outputShapeTensor.dispose();
    return res;
  }
};
