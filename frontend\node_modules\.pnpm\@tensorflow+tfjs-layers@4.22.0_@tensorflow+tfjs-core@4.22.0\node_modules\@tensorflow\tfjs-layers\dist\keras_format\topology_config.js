/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
export {};
//# sourceMappingURL=data:application/json;base64,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