/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '@tensorflow/tfjs';
import { backend_util, BackendTimingInfo, DataId, DataType, KernelBackend, ModelTensorInfo, Scalar, ScalarLike, Tensor, Tensor1D, Tensor2D, Tensor3D, Tensor4D, TensorInfo } from '@tensorflow/tfjs';
import { TFEOpAttr, TFJSBinding } from './tfjs_binding';
export declare class NodeJSKernelBackend extends KernelBackend {
    binding: TFJSBinding;
    isGPUPackage: boolean;
    isUsingGpuDevice: boolean;
    private tensorMap;
    constructor(binding: TFJSBinding, packageName: string);
    getDTypeInteger(dtype: DataType): number;
    private typeAttributeFromTensor;
    private createOutputTensor;
    private getInputTensorIds;
    createReductionOpAttrs(tensor: TensorInfo, keepDims?: boolean): TFEOpAttr[];
    floatPrecision(): 16 | 32;
    epsilon(): number;
    /**
     * Executes an op that has a single input and output.
     *
     * Helper function to wrap executeSingleOutput in a particular case.
     * @param name The name of the Op to execute.
     * @param input The input Tensor for the Op.
     */
    executeSingleInput(name: string, input: TensorInfo): Tensor;
    /**
     * Executes a TensorFlow Eager Op that provides one output Tensor.
     * @param name The name of the Op to execute.
     * @param opAttrs The list of Op attributes required to execute.
     * @param inputs The list of input Tensors for the Op.
     * @return A resulting Tensor from Op execution.
     */
    executeSingleOutput(name: string, opAttrs: TFEOpAttr[], inputs: TensorInfo[]): Tensor;
    /**
     * Executes a TensorFlow Eager Op that provides multiple output Tensors.
     * @param name The name of the Op to execute.
     * @param opAttrs The list of Op attributes required to execute.
     * @param inputs The list of input Tensors for the Op.
     * @param numOutputs The number of output Tensors for Op execution.
     * @return A resulting Tensor array from Op execution.
     */
    executeMultipleOutputs(name: string, opAttrs: TFEOpAttr[], inputs: TensorInfo[], numOutputs: number): Tensor[];
    numDataIds(): number;
    dispose(): void;
    read(dataId: DataId): Promise<backend_util.BackendValues>;
    readSync(dataId: DataId): backend_util.BackendValues;
    /**
     * Dispose the memory if the dataId has 0 refCount. Return true if the memory
     * is released, false otherwise.
     * @param dataId
     * @oaram force Optional, remove the data regardless of refCount
     */
    disposeData(dataId: DataId, force?: boolean): boolean;
    /** Return refCount of a `TensorData`. */
    refCount(dataId: DataId): number;
    incRef(dataId: DataId): void;
    move(dataId: DataId, values: backend_util.BackendValues, shape: number[], dtype: DataType, refCount: number): void;
    write(values: backend_util.BackendValues, shape: number[], dtype: DataType): DataId;
    applyActivation<T extends Tensor>(input: T, activation: string, preluActivationWeights?: Tensor, leakyreluAlpha?: number): T;
    divide(a: Tensor, b: Tensor): Tensor;
    divNoNan(a: Tensor, b: Tensor): Tensor;
    where(condition: Tensor): Tensor2D;
    topKValues<T extends Tensor>(x: T, k: number): Tensor1D;
    topKIndices(x: Tensor, k: number): Tensor1D;
    int<T extends Tensor>(x: T): T;
    decodeJpeg(contents: Uint8Array, channels: number, ratio: number, fancyUpscaling: boolean, tryRecoverTruncated: boolean, acceptableFraction: number, dctMethod: string): Tensor3D;
    decodePng(contents: Uint8Array, channels: number): Tensor3D;
    decodeBmp(contents: Uint8Array, channels: number): Tensor3D;
    decodeGif(contents: Uint8Array): Tensor4D;
    executeEncodeImageOp(name: string, opAttrs: TFEOpAttr[], imageData: Uint8Array, imageShape: number[]): Tensor;
    encodeJpeg(imageData: Uint8Array, imageShape: number[], format: '' | 'grayscale' | 'rgb', quality: number, progressive: boolean, optimizeSize: boolean, chromaDownsampling: boolean, densityUnit: 'in' | 'cm', xDensity: number, yDensity: number, xmpMetadata: string): Tensor;
    encodePng(imageData: Uint8Array, imageShape: number[], compression: number): Tensor;
    deleteSavedModel(id: number): void;
    loadSavedModelMetaGraph(path: string, tags: string): number;
    private getMappedInputTensorIds;
    runSavedModel(id: number, inputs: Tensor[], inputTensorInfos: ModelTensorInfo[], outputOpNames: string[]): Tensor[];
    summaryWriter(logdir: string): Tensor1D;
    createSummaryFileWriter(resourceHandle: Tensor, logdir: string, maxQueue?: number, flushMillis?: number, filenameSuffix?: string): void;
    writeScalarSummary(resourceHandle: Tensor, step: number, name: string, value: Scalar | number): void;
    writeHistogramSummary(resourceHandle: Tensor, step: number, name: string, data: Tensor, bucketCount: number | undefined, description: string | undefined): void;
    flushSummaryWriter(resourceHandle: Tensor): void;
    /**
     * Group data into histogram buckets.
     *
     * @param data A `Tensor` of any shape. Must be castable to `float32`
     * @param bucketCount Optional positive `number`
     * @returns A `Tensor` of shape `[k, 3]` and type `float32`. The `i`th row
     *     is
     *   a triple `[leftEdge, rightEdge, count]` for a single bucket. The value
     * of `k` is either `bucketCount`, `1` or `0`.
     */
    private buckets;
    memory(): {
        unreliable: boolean;
    };
    time(f: () => void): Promise<BackendTimingInfo>;
    getNumOfSavedModels(): number;
    getNumOfTFTensors(): number;
}
/** Returns an instance of the Node.js backend. */
export declare function nodeBackend(): NodeJSKernelBackend;
/** Returns the TF dtype for a given DataType. */
export declare function getTFDType(dataType: tf.DataType): number;
/**
 * Creates a TFEOpAttr for a 'type' OpDef attribute from a Tensor or list of
 * Tensors.
 */
export declare function createTensorsTypeOpAttr(attrName: string, tensorsOrDtype: tf.Tensor | tf.Tensor[] | tf.DataType): TFEOpAttr;
export declare function createOpAttr(attrName: string, tensorsOrDtype: tf.Tensor | tf.Tensor[] | tf.DataType, value: ScalarLike): TFEOpAttr;
export declare function ensureTensorflowBackend(): void;
