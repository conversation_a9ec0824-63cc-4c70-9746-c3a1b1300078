/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid.
/**
 * A string array of valid MergeLayer class names.
 *
 * This is guaranteed to match the `MergeLayerClassName` union type.
 */
export const mergeLayerClassNames = [
    'Add',
    'Average',
    'Concatenate',
    'Dot',
    'Maximum',
    'Minimum',
    'Multiply',
];
//# sourceMappingURL=data:application/json;base64,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