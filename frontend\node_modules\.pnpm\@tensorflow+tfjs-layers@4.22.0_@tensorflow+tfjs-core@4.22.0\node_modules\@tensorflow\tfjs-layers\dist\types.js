/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
export {};
//# sourceMappingURL=data:application/json;base64,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