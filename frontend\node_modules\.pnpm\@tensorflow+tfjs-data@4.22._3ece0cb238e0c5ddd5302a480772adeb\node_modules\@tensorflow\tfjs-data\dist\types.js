/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * =============================================================================
 */
export {};
//# sourceMappingURL=data:application/json;base64,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