"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.cropAndResizeConfig = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.cropAndResizeConfig = {
    kernelName: tfjs_1.CropAndResize,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var _a = args.inputs, image = _a.image, boxes = _a.boxes, boxInd = _a.boxInd;
        var backend = args.backend;
        var _b = args.attrs, cropSize = _b.cropSize, method = _b.method, extrapolationValue = _b.extrapolationValue;
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', image.dtype),
            { name: 'method', type: backend.binding.TF_ATTR_STRING, value: method }, {
                name: 'extrapolation_value',
                type: backend.binding.TF_ATTR_FLOAT,
                value: extrapolationValue
            }
        ];
        var cropSizeTensor = (0, tfjs_1.tensor1d)(cropSize, 'int32');
        var res = backend.executeSingleOutput(tfjs_1.CropAndResize, opAttrs, [image, boxes, boxInd, cropSizeTensor]);
        cropSizeTensor.dispose();
        return res;
    }
};
