"use strict";
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.conv3DBackpropFilterV2Config = void 0;
var tfjs_1 = require("@tensorflow/tfjs");
var nodejs_kernel_backend_1 = require("../nodejs_kernel_backend");
exports.conv3DBackpropFilterV2Config = {
    kernelName: tfjs_1.Conv3DBackpropFilterV2,
    backendName: 'tensorflow',
    kernelFunc: function (args) {
        var _a = args.inputs, x = _a.x, dy = _a.dy;
        var backend = args.backend;
        var _b = args.attrs, strides = _b.strides, pad = _b.pad, filterShape = _b.filterShape;
        var dilations = 1;
        var convInfo = tfjs_1.backend_util.computeConv3DInfo(x.shape, filterShape, strides, dilations, pad);
        var $strides = [
            1, convInfo.strideDepth, convInfo.strideHeight, convInfo.strideWidth, 1
        ];
        var padding = convInfo.padInfo.type;
        var dataFormat = convInfo.dataFormat === 'channelsLast' ? 'NDHWC' : 'NCDHW';
        if (!backend.isGPUPackage && convInfo.dilationDepth > 1) {
            throw new Error('CPU Dilation depth must be 1');
        }
        var $dilations = [
            1, convInfo.dilationDepth, convInfo.dilationHeight,
            convInfo.dilationWidth, 1
        ];
        var opAttrs = [
            (0, nodejs_kernel_backend_1.createTensorsTypeOpAttr)('T', x.dtype),
            { name: 'strides', type: backend.binding.TF_ATTR_INT, value: $strides },
            { name: 'padding', type: backend.binding.TF_ATTR_STRING, value: padding }, {
                name: 'data_format',
                type: backend.binding.TF_ATTR_STRING,
                value: dataFormat
            },
            { name: 'dilations', type: backend.binding.TF_ATTR_INT, value: $dilations }
        ];
        var filterSizes = (0, tfjs_1.tensor1d)(filterShape, 'int32');
        var res = backend.executeSingleOutput(tfjs_1.Conv3DBackpropFilterV2, opAttrs, [x, filterSizes, dy]);
        filterSizes.dispose();
        return res;
    }
};
