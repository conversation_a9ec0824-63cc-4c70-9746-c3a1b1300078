/**
 * @license
 * Copyright 2018 Google LLC
 *
 * Use of this source code is governed by an MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 * =============================================================================
 */
// We can't easily extract a string[] from the string union type, but we can
// recapitulate the list, enforcing at compile time that the values are valid.
/**
 * A string array of valid Optimizer class names.
 *
 * This is guaranteed to match the `OptimizerClassName` union type.
 */
export const optimizerClassNames = ['Adadelta', 'Adagrad', 'Adam', 'Adamax', 'Momentum', 'RMSProp', 'SGD'];
//# sourceMappingURL=data:application/json;base64,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